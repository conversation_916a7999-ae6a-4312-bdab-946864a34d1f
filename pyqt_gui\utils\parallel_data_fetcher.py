#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
并行数据获取器

用于并行获取多只股票的历史数据，提升数据获取效率

作者: LilyBullRider团队
创建时间: 2024
"""

import platform
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Tuple, Optional, Callable
import pandas as pd
import akshare as ak
from datetime import datetime, timedelta
from utils.logging_config import get_logger


class ParallelDataFetcher:
    """并行数据获取器"""
    
    def __init__(self, progress_callback: Optional[Callable] = None):
        """
        初始化并行数据获取器
        
        Parameters
        ----------
        progress_callback : Callable, optional
            进度回调函数，接收 (progress: int, message: str) 参数
        """
        self.progress_callback = progress_callback
        self.logger = get_logger(__name__)
        
    def get_optimal_workers(self, task_type: str = "io", task_count: int = None) -> int:
        """
        根据系统配置和任务类型获取最优线程数
        
        Parameters
        ----------
        task_type : str
            任务类型，"io" 或 "cpu"
        task_count : int, optional
            任务数量，如果提供则限制最大线程数
            
        Returns
        -------
        int
            最优线程数
        """
        cpu_count = multiprocessing.cpu_count()
        cpu_info = platform.processor().lower()
        
        # 检测特殊CPU类型
        is_amd_cpu = 'amd' in cpu_info
        is_3d_vcache = any(vcache_model in cpu_info for vcache_model in ['7950x3d', '7900x3d', '7800x3d', 'x3d'])
        
        if task_type == "io":
            # IO密集型任务（数据获取）
            if is_3d_vcache:
                # 3D V-Cache CPU使用更保守的线程数
                max_workers = 4
            elif is_amd_cpu:
                # 普通AMD CPU
                max_workers = 6
            else:
                # Intel或其他CPU
                max_workers = 8
        else:
            # CPU密集型任务（计算）
            if is_3d_vcache:
                # 3D V-Cache CPU避免过多并发
                max_workers = cpu_count // 2
            else:
                # 其他CPU充分利用核心数
                max_workers = cpu_count
        
        # 如果提供了任务数量，则限制最大线程数
        if task_count is not None:
            max_workers = min(max_workers, task_count)
                
        return max(1, max_workers)
    
    def fetch_single_stock_data(self, stock_info: Tuple[str, str], 
                              start_date: str, end_date: str) -> Tuple[str, pd.DataFrame, Optional[str]]:
        """
        获取单只股票的历史数据
        
        Parameters
        ----------
        stock_info : Tuple[str, str]
            股票信息 (代码, 名称)
        start_date : str
            开始日期
        end_date : str
            结束日期
            
        Returns
        -------
        Tuple[str, pd.DataFrame, Optional[str]]
            (股票代码, 数据DataFrame, 错误信息)
        """
        stock_code, stock_name = stock_info
        try:
            # 获取股票历史数据
            stock_data = ak.stock_zh_a_hist(
                symbol=stock_code,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                adjust="qfq"
            )
            
            if stock_data.empty:
                return stock_code, pd.DataFrame(), f"股票 {stock_code} 无数据"
            
            # 重命名列以符合评分模型要求
            column_mapping = {
                "日期": "date",
                "开盘": "open",
                "收盘": "close",
                "最高": "high",
                "最低": "low",
                "成交量": "volume",
                "成交额": "amount"
            }
            stock_data = stock_data.rename(columns=column_mapping)
            
            return stock_code, stock_data, None
            
        except Exception as e:
            error_msg = f"获取股票 {stock_code}({stock_name}) 数据失败: {str(e)}"
            self.logger.error(error_msg)
            return stock_code, pd.DataFrame(), error_msg
    
    def batch_fetch_stock_data(self, constituents: pd.DataFrame, 
                             start_date: str, end_date: str) -> Dict[str, pd.DataFrame]:
        """
        批量并行获取股票历史数据
        
        Parameters
        ----------
        constituents : pd.DataFrame
            成分股数据，包含'代码'和'名称'列
        start_date : str
            开始日期
        end_date : str
            结束日期
            
        Returns
        -------
        Dict[str, pd.DataFrame]
            股票代码到数据DataFrame的映射
        """
        if constituents.empty:
            return {}
        
        # 准备股票信息列表
        stock_infos = []
        for _, row in constituents.iterrows():
            stock_code = str(row['代码']).split('.')[0]
            stock_name = row['名称']
            stock_infos.append((stock_code, stock_name))
        
        # 获取最优线程数
        max_workers = self.get_optimal_workers("io", len(stock_infos))
        
        if self.progress_callback:
            self.progress_callback(10, f"开始并行获取 {len(stock_infos)} 只股票数据（{max_workers} 线程）...")
        
        # 并行获取数据
        stock_data_dict = {}
        completed_count = 0
        error_count = 0
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_stock = {
                executor.submit(self.fetch_single_stock_data, stock_info, start_date, end_date): stock_info[0]
                for stock_info in stock_infos
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_stock):
                stock_code = future_to_stock[future]
                try:
                    code, data, error = future.result()
                    completed_count += 1
                    
                    if error is None and not data.empty:
                        stock_data_dict[code] = data
                    else:
                        error_count += 1
                        if error:
                            self.logger.warning(error)
                    
                    # 更新进度
                    if self.progress_callback:
                        progress = 10 + int(20 * completed_count / len(stock_infos))
                        self.progress_callback(progress, 
                                             f"数据获取进度: {completed_count}/{len(stock_infos)} (失败: {error_count})")
                        
                except Exception as e:
                    completed_count += 1
                    error_count += 1
                    self.logger.error(f"处理股票 {stock_code} 时发生异常: {str(e)}")
        
        if self.progress_callback:
            self.progress_callback(30, 
                                 f"数据获取完成: 成功 {len(stock_data_dict)} 只，失败 {error_count} 只")
        
        return stock_data_dict