# styles.py
from PyQt6.QtGui import QColor, QPalette, QFont
from PyQt6.QtWidgets import QApplication


class AppStyles:
    """应用程序样式管理类"""
    
    # 颜色方案 - 更新了一些颜色以提高对比度和美观度
    COLORS = {
        'bg': '#f8f9fa',           # 背景色
        'fg': '#212529',           # 前景色
        'accent': '#0d6efd',       # 强调色
        'accent_hover': '#0b5ed7', # 强调色悬停
        'card_bg': '#ffffff',      # 卡片背景
        'border': '#dee2e6',       # 边框色
        'rise': '#e63946',         # 上涨色（红色）
        'fall': '#2a9d8f',         # 下跌色（绿色）
        'neutral': '#457b9d',      # 中性色
        'highlight': '#f8f9d7',    # 高亮色
        'header_bg': '#f1f3f5',    # 表头背景色
        'grid_line': '#e9ecef',    # 网格线颜色
        'sidebar_bg': '#f1f3f5',   # 侧边栏背景色
        'primary': '#1a73e8',      # 主要按钮色
        'success': '#2e7d32',      # 成功状态色
        'warning': '#ed6c02',      # 警告状态色
        'error': '#d32f2f',        # 错误状态色
        'info': '#0288d1',         # 信息状态色
    }
    
    # 字体设置 - 适应高分辨率
    FONTS = {
        'default': QFont("微软雅黑", 10),
        'title': QFont("微软雅黑", 14, QFont.Weight.Bold),
        'subtitle': QFont("微软雅黑", 12, QFont.Weight.Bold),
        'small': QFont("微软雅黑", 9),
        'data': QFont("微软雅黑", 11),
        'data_bold': QFont("微软雅黑", 11, QFont.Weight.Bold)
    }
    
    @staticmethod
    def get_color(name):
        """获取颜色对象"""
        return QColor(AppStyles.COLORS.get(name, '#000000'))
    
    @staticmethod
    def get_font(name):
        """获取字体对象"""
        return AppStyles.FONTS.get(name, AppStyles.FONTS['default'])
    
    @staticmethod
    def apply_app_style(app: QApplication):
        """应用全局样式到应用程序"""
        # 设置Fusion风格
        app.setStyle("Fusion")
        
        # 创建自定义调色板
        palette = QPalette()
        palette.setColor(QPalette.ColorRole.Window, AppStyles.get_color('bg'))
        palette.setColor(QPalette.ColorRole.WindowText, AppStyles.get_color('fg'))
        palette.setColor(QPalette.ColorRole.Base, AppStyles.get_color('card_bg'))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor('#f5f5f5'))
        palette.setColor(QPalette.ColorRole.ToolTipBase, AppStyles.get_color('card_bg'))
        palette.setColor(QPalette.ColorRole.ToolTipText, AppStyles.get_color('fg'))
        palette.setColor(QPalette.ColorRole.Text, AppStyles.get_color('fg'))
        palette.setColor(QPalette.ColorRole.Button, AppStyles.get_color('bg'))
        palette.setColor(QPalette.ColorRole.ButtonText, AppStyles.get_color('fg'))
        palette.setColor(QPalette.ColorRole.Highlight, AppStyles.get_color('accent'))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor('#ffffff'))
        
        # 应用调色板
        app.setPalette(palette)
        
        # 设置全局样式表 - 优化了间距和样式以适应1600x900分辨率
        app.setStyleSheet("""
            QMainWindow, QDialog {
                background-color: #f8f9fa;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 20px;
                background-color: #ffffff;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: #ffffff;
            }
            
            QPushButton {
                background-color: #1a73e8;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 85px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #1765cc;
            }
            
            QPushButton:pressed {
                background-color: #1557b0;
            }
            
            QPushButton:disabled {
                background-color: #a0a0a0;
                color: #e0e0e0;
            }
            
            QTreeWidget, QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 2px;
                alternate-background-color: #f5f5f5;
                gridline-color: #e9ecef;
            }
            
            QTreeWidget::item, QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #f0f0f0;
            }
            
            QHeaderView::section {
                background-color: #f1f3f5;
                padding: 8px;
                border: 1px solid #dee2e6;
                border-top: 0px;
                border-left: 0px;
                font-weight: bold;
            }
            
            QTextEdit, QLineEdit {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 8px;
                background-color: white;
                selection-background-color: #1a73e8;
            }
            
            QComboBox {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 6px;
                min-width: 8em;
                background-color: white;
            }
            
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #dee2e6;
            }
            
            QStatusBar {
                background-color: #f8f9fa;
                color: #6c757d;
                border-top: 1px solid #dee2e6;
                padding: 5px;
            }
            
            QSplitter::handle {
                background-color: #dee2e6;
            }
            
            QSplitter::handle:horizontal {
                width: 2px;
            }
            
            QSplitter::handle:vertical {
                height: 2px;
            }
            
            QScrollBar:vertical {
                border: none;
                background: #f8f9fa;
                width: 12px;
                margin: 0px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background: #adb5bd;
                min-height: 30px;
                border-radius: 6px;
            }
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            
            QScrollBar:horizontal {
                border: none;
                background: #f8f9fa;
                height: 12px;
                margin: 0px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:horizontal {
                background: #adb5bd;
                min-width: 30px;
                border-radius: 6px;
            }
            
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
            
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 5px;
                background-color: white;
            }
            
            QTabBar::tab {
                background-color: #f1f3f5;
                border: 1px solid #dee2e6;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 8px 16px;
                margin-right: 2px;
            }
            
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
            
            QTabBar::tab:hover:!selected {
                background-color: #e9ecef;
            }
            
            QProgressBar {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                background-color: #f5f5f5;
                text-align: center;
                height: 16px;
            }
            
            QProgressBar::chunk {
                background-color: #1a73e8;
                border-radius: 5px;
            }
            
            QCheckBox {
                spacing: 8px;
            }
            
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)