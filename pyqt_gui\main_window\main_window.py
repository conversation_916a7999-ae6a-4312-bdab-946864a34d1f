import sys
import os
from datetime import datetime
from PyQt6.QtWidgets import QApplication, QMainWindow
from PyQt6.QtCore import Qt, QTimer, QSize
from utils.logging_config import get_logger

# 将项目根目录添加到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入重构的组件
from pyqt_gui.components.ui_setup import UISetup
from pyqt_gui.components.data_handlers import DataHandlers
from pyqt_gui.components.auto_refresh import AutoRefresh
from pyqt_gui.components.status_manager import StatusManager
from pyqt_gui.components.data_loaders import DataLoaders
from pyqt_gui.components.responsive_layout import ResponsiveLayoutManager

# 导入基本面分析和技术分析模块
from fundamental_analysis.sector_rank import SectorRankAnalyzer
from fundamental_analysis.concept_rank import ConceptRankAnalyzer
from fundamental_analysis.stock_selection import StockSelector
from technical_analysis.stock_analyzer import StockAnalyzer
from capital_flow import MoneyEffectScore


class StockAnalysisApp(QMainWindow):
    """AI股票分析系统主窗口 - 重构版"""

    def __init__(self):
        try:
            super().__init__()

            # 设置窗口属性
            self.setWindowTitle("AI股票分析系统 (重构版)")
            self.setMinimumSize(1200, 700)  # 设置最小窗口大小
            self.resize(1600, 900)  # 默认窗口大小

            # 初始化状态变量
            self.data_loaded = False
            self.data_loading = False
            self.models_loaded = False
            self.models_loading = False

            # 界面初始化完成

            # 初始化数据模块（延迟初始化）
            self.sector_analyzer = None
            self.concept_analyzer = None
            self.stock_selector = None
            self.stock_analyzer = None
            self.money_effect = None

            # 数据加载状态
            self.loading_progress = 0

            # 自动刷新控制变量
            self.auto_refresh_enabled = False
            self.refresh_interval = 60  # 默认1分钟
            self.money_effect_refresh_interval = 60  # 默认1分钟

            # 窗口居中
            self.center_window()

            # 设置UI
            UISetup.setup_ui(self)

            # 设置状态栏
            UISetup.setup_status_bar(self)

            # 连接信号和槽
            self.connect_signals()

            # 初始化响应式布局管理器
            self.responsive_layout = ResponsiveLayoutManager(self)
            
            # 延迟初始化数据模块
            QTimer.singleShot(500, self.safe_init_data_modules)
            
            # 延迟注册响应式布局组件
            QTimer.singleShot(600, self.setup_responsive_layout)
    

            logger = get_logger(__name__)
            logger.info("StockAnalysisApp 初始化完成")
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"StockAnalysisApp 初始化失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # 显示错误对话框
            from PyQt6.QtWidgets import QMessageBox
            error_box = QMessageBox()
            error_box.setIcon(QMessageBox.Icon.Critical)
            error_box.setWindowTitle("错误")
            error_box.setText("StockAnalysisApp 初始化失败")
            error_box.setDetailedText(f"{str(e)}\n{traceback.format_exc()}")
            error_box.exec()
            # 异常向上传递
            raise

    def safe_init_data_modules(self):
        """安全地初始化数据模块"""
        try:
            DataLoaders.init_data_modules(self)
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"初始化数据模块失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # 更新状态栏
            StatusManager.update_status(self, f"初始化数据模块失败: {str(e)}", "error")



    def center_window(self):
        """将窗口居中显示在屏幕上"""
        try:
            screen_geometry = QApplication.primaryScreen().geometry()
            x = (screen_geometry.width() - self.width()) // 2
            y = (screen_geometry.height() - self.height()) // 2
            self.move(x, y)
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"窗口居中失败: {str(e)}")

    def connect_signals(self):
        """连接所有信号和槽"""
        # 行业/概念切换按钮
        self.industry_button.clicked.connect(lambda: self.sector_stack.setCurrentIndex(0))
        self.concept_button.clicked.connect(lambda: self.sector_stack.setCurrentIndex(1))

        # 板块树形视图双击事件
        self.industry_tree.itemDoubleClicked.connect(self.on_sector_double_click)
        self.concept_tree.itemDoubleClicked.connect(self.on_sector_double_click)

        # 成分股表格双击事件
        self.constituent_table.itemDoubleClicked.connect(self.on_constituent_table_double_clicked)

        # 控制栏按钮和控件
        self.auto_refresh_checkbox.toggled.connect(lambda checked: AutoRefresh.toggle_auto_refresh(self, checked))
        self.interval_combo.currentTextChanged.connect(lambda text: AutoRefresh.update_refresh_interval(self, text))
        self.theme_btn.clicked.connect(lambda: StatusManager.toggle_theme(self))

        # 赚钱效应评分
        self.money_effect_auto_refresh.toggled.connect(lambda checked: AutoRefresh.toggle_money_effect_auto_refresh(self, checked))
        self.money_effect_interval_combo.currentTextChanged.connect(lambda text: AutoRefresh.update_money_effect_refresh_interval(self, text))

        # 数据刷新完成

        # 个股分析
        self.stock_code_input.returnPressed.connect(self.analyze_stock)
        
        # 响应式布局信号连接
        if hasattr(self, 'responsive_layout'):
            self.responsive_layout.layout_changed.connect(self.on_layout_mode_changed)

    # 槽函数实现
    def load_initial_data(self):
        """加载初始数据"""
        DataLoaders.load_initial_data(self)

    def update_money_effect_score(self):
        """更新赚钱效应评分"""
        DataLoaders.update_money_effect_score(self)

    def on_sector_double_click(self, item, column):
        """板块双击事件处理"""
        # 获取选中的板块名称
        sector_name = item.text(1)
        if not sector_name:
            return

        # 更新选中板块标签
        self.selected_sector_label.setText(f"当前板块: {sector_name}")

        # 根据当前活动的栈部件确定板块类型
        sector_type = "industry" if self.sector_stack.currentIndex() == 0 else "concept"

        # 加载成分股数据
        DataLoaders.load_constituents(self, sector_name, sector_type)

    def on_constituent_table_double_clicked(self, item):
        """成分股表格双击事件处理"""
        try:
            # 验证item是否有效
            if item is None:
                return

            # 获取选中行
            row = item.row()
            if row < 0:
                return

            # 检查表格大小
            if self.constituent_table.rowCount() <= row or self.constituent_table.columnCount() < 2:
                return

            # 获取股票代码和名称
            code_item = self.constituent_table.item(row, 0)
            name_item = self.constituent_table.item(row, 1)

            if code_item and name_item:
                stock_code = code_item.text().strip()
                stock_name = name_item.text().strip()

                # 验证股票代码不为空
                if not stock_code:
                    logger = get_logger(__name__)
                    logger.warning("尝试加载空股票代码")
                    return

                # 显示加载状态
                self.analysis_text.setPlainText(f"正在加载 {stock_name}({stock_code}) 的数据，请稍候...")

                # 加载股票数据
                DataLoaders.load_stock_data(self, stock_code, stock_name)
        except Exception as e:
            import traceback
            logger = get_logger(__name__)
            logger.error(f"处理成分股双击事件异常: {str(e)}\n{traceback.format_exc()}")
            # 更新状态栏显示错误
            from pyqt_gui.components.status_manager import StatusManager
            StatusManager.update_status(self, f"处理成分股双击事件失败: {str(e)}", "error")

    def analyze_stock(self):
        """分析股票按钮回调"""
        DataLoaders.analyze_stock(self)
    
    def setup_responsive_layout(self):
        """设置响应式布局组件"""
        try:
            # 注册需要管理的组件
            if hasattr(self, 'main_splitter') and hasattr(self, 'constituent_table'):
                # 获取面板组件
                left_panel = getattr(self, 'left_panel', None)
                middle_panel = getattr(self, 'middle_panel', None) 
                right_panel = getattr(self, 'right_panel', None)
                
                if left_panel and middle_panel and right_panel:
                    self.responsive_layout.register_components(
                        self.main_splitter,
                        self.constituent_table,
                        left_panel,
                        middle_panel,
                        right_panel
                    )
                    logger = get_logger(__name__)
                    logger.info("响应式布局组件注册完成")
                else:
                    logger = get_logger(__name__)
                    logger.warning("部分面板组件未找到，响应式布局可能无法正常工作")
            else:
                logger = get_logger(__name__)
                logger.warning("主分割器或成分股表格未找到，响应式布局初始化失败")
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"设置响应式布局失败: {str(e)}")
    
    def resizeEvent(self, event):
        """窗口大小变化事件处理"""
        super().resizeEvent(event)
        if hasattr(self, 'responsive_layout'):
            self.responsive_layout.on_window_resize(event.size())
    
    def on_layout_mode_changed(self, mode: str):
        """布局模式变化处理"""
        try:
            logger = get_logger(__name__)
            logger.info(f"布局模式已切换到: {mode}")
            
            # 更新状态栏显示当前布局模式
            if hasattr(self, 'status_label'):
                self.status_label.setText(f"布局模式: {mode}")
                
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"处理布局模式变化失败: {str(e)}")
    
    def closeEvent(self, event):
        """窗口关闭事件处理"""
        try:
            # 保存响应式布局状态
            if hasattr(self, 'responsive_layout'):
                self.responsive_layout.save_layout_state()
            
            # 调用父类的关闭事件
            super().closeEvent(event)
            
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"保存布局状态失败: {str(e)}")
            super().closeEvent(event)




def main():
    """启动应用程序"""
    # 使用统一的日志配置
    from utils.logging_config import get_logger
    logger = get_logger(__name__)
    logger.info("启动PyQt应用程序")

    # 创建应用
    app = QApplication(sys.argv)

    # 初始化样式
    from pyqt_gui.style_manager import StyleManager
    StyleManager.initialize()
    StyleManager.apply_fusion_style(app)

    # 创建主窗口
    window = StockAnalysisApp()
    window.show()

    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()