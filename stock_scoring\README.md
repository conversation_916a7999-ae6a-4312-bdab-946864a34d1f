# 个股综合评分模块

本模块实现了A股个股的综合评分功能，通过整合技术面、资金面、非线性动力学和拓扑数据四个维度的数据，为每只股票提供一个全面的评分，用于短线交易参考。

## 模块架构

```
stock_scoring/
  ├── models/                   # 评分模型
  │   ├── stock_scoring_model_v4_1.py  # V4.1综合评分模型（当前版本）
  │   ├── old models/           # 旧版模型（已弃用）
  │   └── __init__.py
  ├── factors/                  # 因子计算
  │   ├── technical_factors.py  # 技术面因子
  │   ├── capital_factors.py    # 资金面因子
  │   ├── nonlinear_factors.py  # 非线性动力学因子
  │   └── topology_factors.py   # 拓扑数据因子

  ├── utils/                    # 工具函数
  ├── tests/                    # 测试代码
  └── __init__.py
```

## 主要功能

1. **综合评分模型**：整合多个维度的数据，提供0-100分制的综合评分和相应的评级。
2. **技术面分析**：提取价格和成交量数据，计算趋势、波动、动量等技术指标。
3. **资金面分析**：分析资金流向，包括主力资金、市场赚钱效应等。
4. **非线性动力学分析**：利用混沌理论和复杂系统方法分析股价行为。
5. **拓扑数据分析**：应用计算拓扑学方法识别价格形态的深层结构。
6. **批量评分**：支持对多只股票进行批量评分，快速筛选出评分较高的潜力股。


## 高级评分系统

最新的股票评分系统融合了非线性动力学和拓扑数据分析方法，引入了以下高级功能：

### 1. 非线性动力学因子

引入基于混沌理论和复杂系统的先进因子：

- **相空间重构熵** - 度量价格序列的复杂度，低熵值对应趋势行情，高熵值对应震荡
- **李雅普诺夫指数** - 测量系统对初始条件的敏感性，正值表示混沌状态
- **赫斯特指数修正版** - 精确区分趋势持续与均值回归
- **分形维数** - 衡量价格时间序列的分形特性
- **多重分形谱宽度** - 分析价格波动的多重分形结构

### 2. 拓扑数据因子

应用计算拓扑学方法识别价格形态的深层结构：

- **持续性同调分析** - 发现价格序列中的拓扑特征
- **沃瑟斯坦距离** - 计算K线形态与典型模式的匹配度



## 股票多因子评分模型 V4

V4评分模型是当前最新版本，主要特点包括：

1. **多维度综合评分**：整合技术面、资金面、非线性和拓扑因子
2. **详细的投资建议**：提供具体的投资行动建议、风险评估和市场洞察
3. **增强的信号系统**：生成多种投资信号，包括技术、资金、系统和发散信号



**当前权重配置**：固定权重模式（技术面：25%，资金面：25%，非线性因子：25%，拓扑因子：25%）

### V4模型使用示例

```python
from stock_scoring.models import StockScoringModelV4_1
import akshare as ak
import pandas as pd
from datetime import datetime, timedelta

# 初始化模型
model = StockScoringModelV4_1()



# 获取股票数据
stock_code = "000001"  # 平安银行
end_date = datetime.now().strftime("%Y%m%d")
start_date = (datetime.now() - timedelta(days=365)).strftime("%Y%m%d")

# 获取股票历史数据
stock_data = ak.stock_zh_a_hist(
    symbol=stock_code,
    period="daily",
    start_date=start_date,
    end_date=end_date,
    adjust="qfq"
)

# 重命名列
column_mapping = {
    "日期": "date", "开盘": "open", "收盘": "close",
    "最高": "high", "最低": "low", "成交量": "volume"
}
stock_data = stock_data.rename(columns=column_mapping)

# 对股票进行评分
score_result = model.score_stock(
    stock_data=stock_data,
    symbol=f"sz{stock_code}",
    start_date=start_date,
    end_date=end_date
)

print(f"总评分: {score_result['total_score']:.2f}")
print(f"技术面评分: {score_result['technical_score']:.2f}")
print(f"资金面评分: {score_result['capital_score']:.2f}")
print(f"非线性评分: {score_result['nonlinear_score']:.2f}")
print(f"拓扑评分: {score_result['topology_score']:.2f}")

print(f"权重配置: {score_result['weights']}")
```

### 评分标准

V4模型评分标准如下：

- 90-100分：S+（极其优秀，极强推荐，具有卓越的综合表现）
- 85-89分：S（非常优秀，强烈推荐，各项指标表现突出）
- 80-84分：S-（优秀，推荐，综合表现很好）
- 75-79分：A+（很好，建议关注，各方面表现良好）
- 70-74分：A（良好，值得关注，整体表现不错）
- 65-69分：A-（较好，可以关注，大部分指标表现良好）
- 60-64分：B+（中上，可考虑，有一定投资价值）
- 55-59分：B（中等，观望，投资价值一般）
- 50-54分：B-（中下，谨慎，有一些不确定因素）
- 45-49分：C+（略差，暂不建议，存在一定风险）
- 40-44分：C（不佳，不建议，风险较大）
- 35-39分：C-（较差，建议回避，风险大）
- 30-34分：D+（差，明确不建议，高风险）
- 25-29分：D（很差，坚决不建议，非常高风险）
- 20-24分：D-（极差，强烈不建议，极高风险）
- 0-19分：E（最差，绝对避开，风险极高）

## 示例代码

详细的使用示例请参考 `examples/stock_scoring_v4_example.py`。

## 备注

1. 本模块仅提供技术参考，不构成投资建议。
2. 评分结果会缓存，默认缓存有效期为4小时。
3. 由于A股T+1交易制度，建议在交易时间使用。
4. 评分模型可以根据需要自定义各维度的权重。

## 高级系统依赖

高级评分系统需要额外安装以下依赖：

```
scipy>=1.7.0
scikit-learn>=0.24.0
akshare>=1.8.0
```

可以通过以下命令安装：

```bash
pip install scipy scikit-learn akshare
```