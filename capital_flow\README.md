# 资金流向分析模块

本模块提供了股票市场资金流向的全面分析功能，包括市场整体资金流向、个股资金流向和资金效应评分等。

## 模块结构

```
capital_flow/
├── money_effect_score.py              # 资金效应评分（完整版）
├── money_effect_score_simplified.py   # 资金效应评分（精简版）
├── market_fund_flow.py                # 市场资金流向分析
├── capital_flow_rank.py               # 资金流向排名
├── individual_fund_flow.py            # 个股资金流向分析
└── __init__.py
```

## 主要功能

### 1. 资金效应评分 (money_effect_score.py)

评估资金流向对股价的影响效应，并提供量化评分。

**主要功能**：
- 主力资金评分：评估主力资金流入流出情况及影响
- 资金动量评分：分析资金流入流出的动量变化
- 资金效应指标：计算不同周期资金流对股价的效应指标
- 资金效应预测：预测未来短期资金流向变化趋势
- 资金异动监测：监测资金流向的异常变动

### 2. 市场资金流向 (market_fund_flow.py)

分析整体市场的资金流向情况。

**主要功能**：
- 大盘资金流向：分析上证/深证主要指数资金流向
- 板块资金流向：计算各行业板块资金净流入流出
- 北向资金监测：分析北向资金流入流出趋势
- 融资融券分析：分析融资融券余额变化趋势
- 资金流向热力图：生成市场资金流向热力图

### 3. 资金流向排名 (capital_flow_rank.py)

提供各类资金流向的排名统计。

**主要功能**：
- 主力资金净流入排名：个股主力资金净流入排名
- 行业资金流向排名：行业板块资金流向排名
- 概念板块资金排名：概念板块资金流向排名
- 连续流入/流出排名：连续多日资金流入/流出排名
- 资金转向监测：资金流向发生转向的个股排名

### 4. 个股资金流向 (individual_fund_flow.py)

分析个股的资金流向详情。

**主要功能**：
- 多周期资金流向：分析日/周/月/季度资金流向
- 大中小单资金流：区分大单/中单/小单资金流向
- 主力成本分析：估算主力持仓成本区间
- 资金流向历史：展示历史资金流向变化趋势
- 筹码分布分析：结合资金流向分析筹码分布

## 使用示例

### 获取资金效应评分

```python
from capital_flow import MoneyEffectScorer

# 创建资金效应评分器
scorer = MoneyEffectScorer()

# 获取单只股票的资金效应评分
score = scorer.calculate_score("600519")
print(f"股票代码: {score['stock_code']}")
print(f"资金效应总分: {score['total_score']}")
print(f"主力资金评分: {score['main_force_score']}")
print(f"资金动量评分: {score['momentum_score']}")
print(f"资金持续性评分: {score['persistence_score']}")
print(f"资金异动评分: {score['abnormal_score']}")

# 批量计算多只股票的资金效应评分
stock_codes = ["600519", "000858", "601318", "600036", "000333"]
scores = scorer.batch_calculate_scores(stock_codes)

# 获取资金效应评分排名前3的股票
top_scores = scorer.get_top_money_effect_stocks(top_n=3)
for i, stock in enumerate(top_scores, 1):
    print(f"{i}. {stock['name']}({stock['code']}): {stock['total_score']}")
```

### 分析市场资金流向

```python
from capital_flow import MarketFundFlow

# 创建市场资金流向分析器
market_flow = MarketFundFlow()

# 获取今日大盘资金流向
market_summary = market_flow.get_market_summary()
print(f"沪深两市资金净流入: {market_summary['total_net_inflow']}亿元")
print(f"上证指数资金净流入: {market_summary['sh_net_inflow']}亿元")
print(f"深证指数资金净流入: {market_summary['sz_net_inflow']}亿元")
print(f"创业板资金净流入: {market_summary['cyb_net_inflow']}亿元")
print(f"科创板资金净流入: {market_summary['kcb_net_inflow']}亿元")

# 获取行业板块资金流向
industry_flows = market_flow.get_industry_fund_flows()
print("行业资金流向排名:")
for i, industry in enumerate(industry_flows[:5], 1):
    print(f"{i}. {industry['name']}: {industry['net_inflow']}亿元 ({industry['net_inflow_percentage']}%)")

# 获取北向资金流向
northbound = market_flow.get_northbound_flow()
print(f"今日北向资金净流入: {northbound['today_net_inflow']}亿元")
print(f"本周北向资金净流入: {northbound['week_net_inflow']}亿元")
print(f"本月北向资金净流入: {northbound['month_net_inflow']}亿元")
```

### 查看资金流向排名

```python
from capital_flow import CapitalFlowRank

# 创建资金流向排名器
rank = CapitalFlowRank()

# 获取主力资金净流入前10名
top_inflow = rank.get_top_main_inflow(top_n=10)
print("主力资金净流入排名:")
for i, stock in enumerate(top_inflow, 1):
    print(f"{i}. {stock['name']}({stock['code']}): {stock['net_inflow']}万元")

# 获取主力资金净流出前10名
top_outflow = rank.get_top_main_outflow(top_n=10)
print("\n主力资金净流出排名:")
for i, stock in enumerate(top_outflow, 1):
    print(f"{i}. {stock['name']}({stock['code']}): {stock['net_outflow']}万元")

# 获取连续5日资金净流入的股票
continuous_inflow = rank.get_continuous_inflow_stocks(days=5)
print(f"\n连续{continuous_inflow[0]['continuous_days']}日资金净流入的股票:")
for i, stock in enumerate(continuous_inflow[:5], 1):
    print(f"{i}. {stock['name']}({stock['code']}): 累计净流入 {stock['accumulated_inflow']}万元")

# 获取今日资金流向转为净流入的股票
flow_direction_changed = rank.get_flow_direction_changed_stocks("inflow")
print("\n今日资金流向转为净流入的股票:")
for i, stock in enumerate(flow_direction_changed[:5], 1):
    print(f"{i}. {stock['name']}({stock['code']}): {stock['net_inflow']}万元")
```

### 分析个股资金流向

```python
from capital_flow import IndividualFundFlow

# 创建个股资金流向分析器
fund_flow = IndividualFundFlow()

# 获取个股资金流向详情
stock_code = "600519"
flow_detail = fund_flow.get_stock_fund_flow(stock_code)

print(f"{flow_detail['name']}({stock_code})资金流向详情:")
print(f"今日资金净流入: {flow_detail['today_net_inflow']}万元")
print(f"主力资金净流入: {flow_detail['main_force_net_inflow']}万元")
print(f"超大单净流入: {flow_detail['super_large_net_inflow']}万元")
print(f"大单净流入: {flow_detail['large_net_inflow']}万元")
print(f"中单净流入: {flow_detail['medium_net_inflow']}万元")
print(f"小单净流入: {flow_detail['small_net_inflow']}万元")

# 获取个股多周期资金流向
multi_period = fund_flow.get_multi_period_flow(stock_code)
print("\n多周期资金流向:")
for period, inflow in multi_period.items():
    print(f"{period}: {inflow}万元")

# 获取个股主力资金流向历史
history = fund_flow.get_fund_flow_history(stock_code, days=5)
print("\n近5日资金流向历史:")
for day in history:
    print(f"{day['date']}: 净流入 {day['net_inflow']}万元, 主力净流入 {day['main_force_net_inflow']}万元")
```

## 高级功能

### 1. 使用精简版资金效应评分

针对需要更快速度的场景，提供了精简版的资金效应评分。

```python
from capital_flow import SimpleMoneyEffectScorer

# 创建精简版评分器
simple_scorer = SimpleMoneyEffectScorer()

# 快速计算资金效应评分
score = simple_scorer.calculate_score("600519")
print(f"资金效应评分: {score['total_score']}")

# 批量快速评分
stock_codes = ["600519", "000858", "601318", "600036", "000333"]
quick_scores = simple_scorer.batch_calculate_scores(stock_codes)
```

### 2. 资金流向异动预警

监测资金流向异常变动并发出预警。

```python
from capital_flow import FundFlowAlert

# 创建资金流向预警器
alert = FundFlowAlert()

# 设置预警条件
alert.set_alert_conditions(
    min_inflow_percentage=5,      # 净流入占成交额比例大于5%
    min_main_force_percentage=3,  # 主力净流入占成交额比例大于3%
    volume_times=2,               # 成交量是5日均量的2倍以上
    price_change_min=2            # 股价涨幅大于2%
)

# 获取今日符合预警条件的股票
alert_stocks = alert.get_alert_stocks()
print("资金异动预警股票:")
for i, stock in enumerate(alert_stocks, 1):
    print(f"{i}. {stock['name']}({stock['code']})")
    print(f"   净流入: {stock['net_inflow']}万元 ({stock['net_inflow_percentage']}%)")
    print(f"   主力净流入: {stock['main_force_net_inflow']}万元 ({stock['main_force_percentage']}%)")
    print(f"   成交量/均量: {stock['volume_ratio']}")
    print(f"   涨跌幅: {stock['price_change']}%")
```

### 3. 资金流向归因分析

分析资金流向的归因，辨别资金来源和去向。

```python
from capital_flow import FundFlowAttribution

# 创建资金流向归因分析器
attribution = FundFlowAttribution()

# 获取个股资金流向归因
stock_code = "600519"
result = attribution.analyze_fund_flow_attribution(stock_code)

print(f"{result['name']}({stock_code})资金流向归因:")
print("资金来源:")
for source in result['inflow_sources']:
    print(f"- {source['name']}: {source['percentage']}%")

print("资金去向:")
for target in result['outflow_targets']:
    print(f"- {target['name']}: {target['percentage']}%")

# 获取资金转移矩阵（从哪些行业流入哪些行业）
transfer_matrix = attribution.get_industry_fund_transfer_matrix()
print("\n行业间资金转移前5名:")
for i, transfer in enumerate(transfer_matrix[:5], 1):
    print(f"{i}. {transfer['from']} -> {transfer['to']}: {transfer['amount']}亿元")
```

## 性能优化

- **数据缓存**: 实现了基于时间的缓存机制，减少重复请求
- **并行计算**: 支持多线程并行计算多只股票的资金流向
- **增量更新**: 只更新最新的资金流向数据，减少数据处理量
- **内存优化**: 针对大量数据的处理进行了内存使用优化

## 注意事项

1. 资金流向数据仅供参考，不构成投资建议
2. 资金流向数据存在T+1延迟，部分数据无法实时获取
3. 主力资金定义可能因数据源不同而有所差异
4. 建议结合技术面和基本面等其他因素综合分析
5. 北向资金数据存在延迟，请留意数据实时性说明

## 依赖项

本模块依赖以下主要库：

```
pandas>=1.3.0
numpy>=1.20.0
matplotlib>=3.4.0
requests>=2.25.0
scipy>=1.7.0
```

可以通过以下命令安装依赖：

```bash
pip install pandas numpy matplotlib requests scipy
``` 