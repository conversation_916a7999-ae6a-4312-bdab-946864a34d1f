#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
股票多因子评分模型 V4

该模型基于市场状态自适应调整各类因子的权重，实现智能化的股票评分系统。
评分流程：
1. 从市场情绪面因子模块获取当前市场状态
2. 从各因子模块获取股票评分数据
3. 根据市场状态自适应调整各模块评分比重
4. 输出最终评分

作者: LilyBullRider团队
日期: 2025-04-16
版本: 4.0.0
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from datetime import datetime, timedelta

# 使用统一的日志配置系统
from utils.logging_config import get_logger

from ..factors.capital_factors import CapitalFactors
from ..factors.topology_factors import TopologyFactors
from ..factors.nonlinear_factors import NonlinearFactors
from ..factors.technical_factors import TechnicalFactors
from ..factors.volume_price_factors import VolumePriceFactors
from ..data.real_time_data_fetcher import RealTimeDataFetcher

# 获取配置好的logger实例
logger = get_logger('stock_scoring_model_v4_1')


class StockScoringModelV4_1:
    """股票多因子评分模型 V4.1"""
    
    # 市场状态映射
    MARKET_STATES = {
        1: "牛市",
        0.5: "震荡偏牛",
        0: "中性市场",
        -0.5: "震荡偏熊",
        -1: "熊市"
    }
    
    # 权重配置（专为短线模式优化，集成量价关系因子）
    WEIGHT_CONFIG = {
        # 牛市短线 - 技术面和资金面更重要，量价关系因子权重最高
        "牛市": {
            "technical": 0.40,      # 传统技术面权重
            "volume_price": 0.25,   # 量价关系因子权重
            "capital": 0.35,        # 资金面权重
            "nonlinear": 0.00,      # 移除非线性因子
            "topology": 0.00        # 移除拓扑因子
        },
        # 震荡偏牛短线
        "震荡偏牛": {
            "technical": 0.38,
            "volume_price": 0.22,
            "capital": 0.35,
            "nonlinear": 0.03,
            "topology": 0.02
        },
        # 中性市场短线
        "中性市场": {
            "technical": 0.35,
            "volume_price": 0.20,
            "capital": 0.35,
            "nonlinear": 0.05,
            "topology": 0.05
        },
        # 震荡偏熊短线
        "震荡偏熊": {
            "technical": 0.33,
            "volume_price": 0.22,
            "capital": 0.35,
            "nonlinear": 0.05,
            "topology": 0.05
        },
        # 熊市短线
        "熊市": {
            "technical": 0.30,
            "volume_price": 0.25,   # 熊市中量价关系更重要
            "capital": 0.35,
            "nonlinear": 0.05,
            "topology": 0.05
        }
    }
    
    def __init__(self, market_index_symbol: str = "sh000001", use_cache: bool = True):
        """
        初始化股票多因子评分模型
        
        Parameters
        ----------
        market_index_symbol : str, optional
            市场指数代码，默认为"sh000001"（上证指数）
        use_cache : bool, optional
            是否使用缓存的市场状态，默认为True
        """
        self.market_index_symbol = market_index_symbol
        self.use_cache = use_cache
        self.market_state = None
        self.market_state_name = None
        self.last_update_time = None
        self.cache_duration = timedelta(minutes=30)  # 短线模式缓存时间
        
        # 初始化市场状态
        self._init_market_state()
        
        logger.info(f"股票多因子评分模型 V4.1 初始化完成，使用市场指数: {market_index_symbol}")
        
    def _init_market_state(self, start_date: str = None, end_date: str = None, realtime_data: dict = None):
        """
        新增参数:
        realtime_data - 实时行情数据(包含最新成交价、成交量、波动率等)
        """
        """
        初始化市场状态
        从赚钱效应模块获取真实的市场状态
        """
        try:
            # 尝试从赚钱效应模块获取市场状态
            from capital_flow.money_effect_score import MoneyEffectScore
            money_effect = MoneyEffectScore()
            report = money_effect.get_money_effect_report()
            
            # 从赚钱效应报告中获取市场状态
            market_state_from_report = report.get('market_state', '震荡')
            
            # 将赚钱效应的市场状态映射到股票评分模型的状态
            state_mapping = {
                '牛': ('牛市', 1),
                '震荡': ('中性市场', 0), 
                '熊': ('熊市', -1)
            }
            
            # 获取映射结果
            mapping_result = state_mapping.get(market_state_from_report, ('中性市场', 0))
            self.market_state_name = mapping_result[0]
            self.market_state = mapping_result[1]
            
            self.last_update_time = datetime.now()
            logger.info(f"从赚钱效应模块获取市场状态: {self.market_state_name} ({self.market_state})")
            
        except Exception as e:
            logger.warning(f"无法从赚钱效应模块获取市场状态，使用默认状态: {str(e)}")
            # 设置默认市场状态为中性市场
            self.market_state = 0
            self.market_state_name = "中性市场"
            self.last_update_time = datetime.now()
            logger.info(f"使用默认市场状态: {self.market_state_name} ({self.market_state})")
    
    def get_market_state(self, update: bool = False, window: int = 30, realtime_monitor: bool = False) -> Tuple[float, str]:
        """
        新增实时监控参数:
        realtime_monitor - 是否开启盘中实时监控模式 (默认关闭)
        """
        """
        获取当前市场状态
        
        Parameters
        ----------
        update : bool, optional
            是否强制更新市场状态，默认为False
        window : int, optional
            计算窗口大小，默认为30
            
        Returns
        -------
        Tuple[float, str]
            市场状态值和市场状态名称
        """
        # 实时监控模式处理逻辑
        if realtime_monitor:
            rt_data = RealTimeDataFetcher.get_index_realtime_data("沪深重要指数")
            volatility = self._calculate_realtime_volatility(rt_data)
            
            # 根据实时波动率调整市场状态
            if volatility > 0.05:
                self.market_state = max(-1, min(1, self.market_state * 1.2))
            elif volatility < 0.02:
                self.market_state = max(-1, min(1, self.market_state * 0.8))
        if (update or self.market_state is None or 
            not self.use_cache or 
            (self.last_update_time is not None and 
             datetime.now() - self.last_update_time >= self.cache_duration)):
            
            # 重新初始化市场状态（从赚钱效应模块获取）
            self._init_market_state()
            logger.info(f"更新市场状态: {self.market_state_name} ({self.market_state})")
                
        return self.market_state, self.market_state_name
    
    def get_weights(self, market_state_name: Optional[str] = None) -> Dict[str, float]:
        """
        获取当前市场状态下各因子的权重配置
        
        Parameters
        ----------
        market_state_name : str, optional
            市场状态名称，如果为None则自动获取当前市场状态
            
        Returns
        -------
        Dict[str, float]
            各因子的权重配置
        """
        # 如果未提供市场状态名称，则获取当前市场状态
        if market_state_name is None:
            _, market_state_name = self.get_market_state()
            
        # 获取权重配置
        weights = self.WEIGHT_CONFIG.get(market_state_name, self.WEIGHT_CONFIG["中性市场"])
        logger.info(f"当前市场状态 '{market_state_name}' 下的因子权重配置: {weights}")
        
        return weights
    
    def score_stock(self, stock_data: pd.DataFrame, symbol: str, 
                   start_date: Optional[str] = None, end_date: Optional[str] = None, 
                   window: int = 30) -> Dict[str, Any]:
        """
        对单只股票进行评分
        
        Parameters
        ----------
        stock_data : pd.DataFrame
            股票行情数据，必须包含OHLCV数据
        symbol : str
            股票代码
        start_date : str, optional
            开始日期，格式为"YYYYMMDD"
        end_date : str, optional
            结束日期，格式为"YYYYMMDD"
        window : int, optional
            计算窗口大小，默认为30

            
        Returns
        -------
        Dict[str, Any]
            评分结果字典，包含:
            - total_score: 总评分 (0-100)
            - technical_score: 技术面评分 (0-100)
            - capital_score: 资金面评分 (0-100)
            - nonlinear_score: 非线性因子评分 (0-100)
            - topology_score: 拓扑因子评分 (0-100)
            - market_state: 市场状态
            - weights: 权重配置
            - detail_scores: 详细评分
        """
        # 获取市场状态
        market_state, market_state_name = self.get_market_state()
        logger.debug(f"股票 {symbol} - 当前市场状态: {market_state_name} ({market_state})")
        
        # 获取权重配置
        weights = self.get_weights(market_state_name)
        logger.debug(f"股票 {symbol} - 权重配置: {weights}")
        
        # 初始化评分字典
        scores = {}
        detail_scores = {}
        
        # 计算技术面评分
        try:
            logger.info(f"计算股票 {symbol} 的技术面因子...")
            # 计算技术面因子
            tf = TechnicalFactors(data=stock_data)
            technical_factors = tf.calculate_all_factors(window=window)
            technical_score = self._calculate_technical_score(technical_factors)
            scores['technical_score'] = technical_score
            detail_scores['technical'] = technical_factors
            logger.info(f"技术面评分: {technical_score:.2f}")
        except Exception as e:
            logger.error(f"计算技术面因子失败: {e}")
            scores['technical_score'] = 50.0  # 发生错误时使用中性分数
            detail_scores['technical'] = {}
        
        # 计算资金面评分
        try:
            logger.info(f"计算股票 {symbol} 的资金面因子...")
            # 获取资金流数据
            capital_data = CapitalFactors.get_fund_flow_data(stock_code=symbol.replace('sh', '').replace('sz', ''),
                                                           market='sh' if symbol.startswith('sh') else 'sz',
                                                           start_date=start_date, end_date=end_date)
            if capital_data.empty:
                raise ValueError("获取资金流数据失败")
                
            cf = CapitalFactors(data=capital_data)
            capital_factors = cf.calculate_all_factors()
            capital_score = self._calculate_capital_score(capital_factors)
            scores['capital_score'] = capital_score
            detail_scores['capital'] = capital_factors
            logger.info(f"资金面评分: {capital_score:.2f}")
        except Exception as e:
            logger.error(f"计算资金面因子失败: {e}")
            scores['capital_score'] = 50.0  # 发生错误时使用中性分数
            detail_scores['capital'] = {}
        
        # 计算非线性因子评分
        try:
            logger.info(f"计算股票 {symbol} 的非线性因子...")
            nf = NonlinearFactors(data=stock_data)
            nonlinear_factors = nf.calculate_all_factors(window=window)
            nonlinear_score = self._calculate_nonlinear_score(nonlinear_factors)
            scores['nonlinear_score'] = nonlinear_score
            detail_scores['nonlinear'] = nonlinear_factors
            logger.info(f"非线性因子评分: {nonlinear_score:.2f}")
        except Exception as e:
            logger.error(f"计算非线性因子失败: {e}")
            scores['nonlinear_score'] = 50.0  # 发生错误时使用中性分数
            detail_scores['nonlinear'] = {}
        
        # 计算拓扑因子评分
        try:
            logger.info(f"计算股票 {symbol} 的拓扑因子...")
            tp = TopologyFactors(data=stock_data)
            topology_factors = tp.calculate_all_factors(window=window)
            topology_score = self._calculate_topology_score(topology_factors)
            scores['topology_score'] = topology_score
            detail_scores['topology'] = topology_factors
            logger.info(f"拓扑因子评分: {topology_score:.2f}")
        except Exception as e:
            logger.error(f"计算拓扑因子失败: {e}")
            scores['topology_score'] = 50.0  # 发生错误时使用中性分数
            detail_scores['topology'] = {}
        
        # 计算量价关系因子评分
        try:
            logger.info(f"计算股票 {symbol} 的量价关系因子...")
            vpf = VolumePriceFactors(data=stock_data)
            volume_price_factors = vpf.calculate_all_factors(window=window)
            
            # 将Series转换为标量值
            volume_price_scalar_factors = {}
            for name, series in volume_price_factors.items():
                if isinstance(series, pd.Series) and not series.empty:
                    volume_price_scalar_factors[name] = series.iloc[-1]
                else:
                    volume_price_scalar_factors[name] = 0.0
            
            volume_price_score = self._calculate_volume_price_score(volume_price_scalar_factors)
            scores['volume_price_score'] = volume_price_score
            detail_scores['volume_price'] = volume_price_factors
            logger.info(f"量价关系因子评分: {volume_price_score:.2f}")
        except Exception as e:
            logger.error(f"计算量价关系因子失败: {e}")
            scores['volume_price_score'] = 50.0  # 发生错误时使用中性分数
            detail_scores['volume_price'] = {}
        
        # 计算总评分
        logger.debug(f"股票 {symbol} - 各维度评分: 技术面={scores['technical_score']:.2f}, 资金面={scores['capital_score']:.2f}, 非线性={scores['nonlinear_score']:.2f}, 拓扑={scores['topology_score']:.2f}, 量价={scores['volume_price_score']:.2f}")
        
        total_score = (
            weights['technical'] * scores['technical_score'] +
            weights['capital'] * scores['capital_score'] +
            weights.get('nonlinear', 0) * scores['nonlinear_score'] +
            weights.get('topology', 0) * scores['topology_score'] +
            weights.get('volume_price', 0) * scores['volume_price_score']
        )
        
        logger.debug(f"股票 {symbol} - 加权计算: {weights['technical']:.2f}*{scores['technical_score']:.2f} + {weights['capital']:.2f}*{scores['capital_score']:.2f} + {weights.get('nonlinear', 0):.2f}*{scores['nonlinear_score']:.2f} + {weights.get('topology', 0):.2f}*{scores['topology_score']:.2f} + {weights.get('volume_price', 0):.2f}*{scores['volume_price_score']:.2f} = {total_score:.2f}")
        
        # 返回评分结果
        result = {
            'symbol': symbol,
            'total_score': float(total_score),
            'technical_score': float(scores['technical_score']),
            'capital_score': float(scores['capital_score']),
            'nonlinear_score': float(scores['nonlinear_score']),
            'topology_score': float(scores['topology_score']),
            'volume_price_score': float(scores['volume_price_score']),
            'market_state': market_state_name,
            'weights': weights,
            'detail_scores': detail_scores
        }
        
        logger.info(f"股票 {symbol} 总评分: {total_score:.2f}")
        return result
    
    def score_multiple_stocks(self, symbols: List[str], 
                            start_date: Optional[str] = None, 
                            end_date: Optional[str] = None,
                            window: int = 30) -> pd.DataFrame:
        """
        对多只股票进行评分
        
        Parameters
        ----------
        symbols : List[str]
            股票代码列表
        start_date : str, optional
            开始日期，格式为"YYYYMMDD"
        end_date : str, optional
            结束日期，格式为"YYYYMMDD"
        window : int, optional
            计算窗口大小，默认为30

            
        Returns
        -------
        pd.DataFrame
            多只股票的评分结果
        """
        logger.debug(f"开始对 {len(symbols)} 只股票进行批量评分，时间范围: {start_date} 到 {end_date}")
        
        # 获取市场状态
        market_state, market_state_name = self.get_market_state()
        logger.debug(f"批量评分 - 当前市场状态: {market_state_name} ({market_state})")
        
        # 初始化结果列表
        results = []
        
        # 对每只股票进行评分
        for i, symbol in enumerate(symbols, 1):
            try:
                logger.info(f"获取股票 {symbol} 的历史数据...")
                stock_data = TopologyFactors.fetch_stock_data(
                    symbol=symbol.replace('sh', '').replace('sz', ''),
                    start_date=start_date,
                    end_date=end_date,
                    period="daily",
                    adjust="qfq"
                )
                
                # 对股票进行评分
                score_result = self.score_stock(
                    stock_data=stock_data,
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    window=window
                )
                
                # 只保留基本评分信息
                basic_result = {
                    'symbol': symbol,
                    'total_score': score_result['total_score'],
                    'technical_score': score_result['technical_score'],
                    'capital_score': score_result['capital_score'],
                    'nonlinear_score': score_result['nonlinear_score'],
                    'topology_score': score_result['topology_score'],
                    'volume_price_score': score_result['volume_price_score'],
                    'market_state': score_result['market_state']
                }
                
                logger.debug(f"股票 {symbol} ({i}/{len(symbols)}) 评分完成 - 总分: {score_result['total_score']:.2f}")
                results.append(basic_result)
                
            except Exception as e:
                logger.error(f"对股票 {symbol} ({i}/{len(symbols)}) 评分失败: {e}")
                continue
        
        # 将结果转换为DataFrame
        if results:
            results_df = pd.DataFrame(results)
            # 按总评分排序
            results_df = results_df.sort_values('total_score', ascending=False)
            logger.debug(f"批量评分完成，成功评分 {len(results)} 只股票，最高分: {results_df['total_score'].max():.2f}，最低分: {results_df['total_score'].min():.2f}")
            return results_df
        else:
            logger.debug("批量评分完成，但没有成功评分的股票")
            return pd.DataFrame()
    
    def _calculate_realtime_volatility(self, rt_data: dict) -> float:
        """
        实时波动率计算（5分钟窗口）
        输入格式：{'prices': [最新5个报价], 'volumes': [最新5个成交量]}
        """
        try:
            prices = np.array(rt_data['prices'][-5:])
            returns = np.diff(prices) / prices[:-1]
            return np.std(returns) * np.sqrt(252*24*12)  # 5分钟数据年化
        except Exception as e:
            logger.error(f"实时波动率计算失败: {str(e)}")
            return 0.03  # 默认返回3%波动率

    def _calculate_technical_score(self, technical_factors: Dict[str, pd.Series]) -> float:
        """
        计算技术面评分
        
        Parameters
        ----------
        technical_factors : Dict[str, pd.Series]
            技术面因子字典
            
        Returns
        -------
        float
            技术面评分 (0-100)
        """
        # 提取最后一个交易日的因子值
        factor_values = {}
        for name, series in technical_factors.items():
            if isinstance(series, pd.Series) and not series.empty:
                factor_values[name] = series.iloc[-1]
        
        # 计算短线技术面子项得分
        sub_scores = {}
        
        # 5日移动平均线得分 (0-100)
        if 'ma_5' in factor_values:
            ma_5_dev = factor_values['ma_5']
            # 价格高于5日均线为正信号
            if ma_5_dev > 0.02:  # 高于2%
                sub_scores['ma_5'] = min(100, 70 + ma_5_dev * 300)
            elif ma_5_dev < -0.02:  # 低于2%
                sub_scores['ma_5'] = max(0, 30 + ma_5_dev * 300)
            else:
                sub_scores['ma_5'] = 50 + ma_5_dev * 500
        else:
            sub_scores['ma_5'] = 50
        
        # 10日移动平均线得分 (0-100)
        if 'ma_10' in factor_values:
            ma_10_dev = factor_values['ma_10']
            if ma_10_dev > 0.03:  # 高于3%
                sub_scores['ma_10'] = min(100, 65 + ma_10_dev * 250)
            elif ma_10_dev < -0.03:  # 低于3%
                sub_scores['ma_10'] = max(0, 35 + ma_10_dev * 250)
            else:
                sub_scores['ma_10'] = 50 + ma_10_dev * 400
        else:
            sub_scores['ma_10'] = 50
        
        # 短期RSI(5期)得分 (0-100)
        if 'rsi_5' in factor_values:
            rsi_5 = factor_values['rsi_5']
            if rsi_5 > 80:  # 超买
                sub_scores['rsi_5'] = max(20, 100 - (rsi_5 - 80) * 3)
            elif rsi_5 < 20:  # 超卖
                sub_scores['rsi_5'] = min(80, 20 + (20 - rsi_5) * 2)
            else:
                sub_scores['rsi_5'] = 50 + (rsi_5 - 50) * 0.8
        else:
            sub_scores['rsi_5'] = 50
        
        # 短期RSI(9期)得分 (0-100)
        if 'rsi_9' in factor_values:
            rsi_9 = factor_values['rsi_9']
            if rsi_9 > 75:  # 超买
                sub_scores['rsi_9'] = max(25, 100 - (rsi_9 - 75) * 2.5)
            elif rsi_9 < 25:  # 超卖
                sub_scores['rsi_9'] = min(75, 25 + (25 - rsi_9) * 1.5)
            else:
                sub_scores['rsi_9'] = 50 + (rsi_9 - 50) * 0.6
        else:
            sub_scores['rsi_9'] = 50
        
        # 快速MACD得分 (0-100)
        if 'fast_macd' in factor_values:
            fast_macd = factor_values['fast_macd']
            sub_scores['fast_macd'] = min(100, max(0, 50 + fast_macd * 100))
        else:
            sub_scores['fast_macd'] = 50
        
        # 短期布林带得分 (0-100)
        if 'short_bollinger' in factor_values:
            bb_pos = factor_values['short_bollinger']
            if bb_pos > 1.0:  # 突破上轨
                sub_scores['bollinger'] = min(100, 80 + (bb_pos - 1.0) * 100)
            elif bb_pos < 0.0:  # 跌破下轨
                sub_scores['bollinger'] = max(0, 20 + bb_pos * 100)
            else:
                sub_scores['bollinger'] = 20 + bb_pos * 60
        else:
            sub_scores['bollinger'] = 50
        
        # 价格偏离率得分 (0-100)
        if 'price_deviation' in factor_values:
            deviation = factor_values['price_deviation']
            if abs(deviation) > 5:  # 偏离超过5%
                sub_scores['deviation'] = 50 - min(30, abs(deviation) - 5) * 2
            else:
                sub_scores['deviation'] = 50 + deviation * 2
        else:
            sub_scores['deviation'] = 50
        
        # 权重配置
        weights = {
            'ma_5': 0.20,        # 5日均线权重20%
            'ma_10': 0.15,       # 10日均线权重15%
            'rsi_5': 0.15,       # 短期RSI(5期)权重15%
            'rsi_9': 0.10,       # 短期RSI(9期)权重10%
            'fast_macd': 0.20,   # 快速MACD权重20%
            'bollinger': 0.15,   # 布林带权重15%
            'deviation': 0.05    # 价格偏离率权重5%
        }
        
        technical_score = sum(score * weights[name] for name, score in sub_scores.items())
        return technical_score
    
    def _calculate_capital_score(self, capital_factors: pd.DataFrame) -> float:
        """
        计算资金面评分
        
        Parameters
        ----------
        capital_factors : pd.DataFrame
            资金面因子DataFrame
            
        Returns
        -------
        float
            资金面评分 (0-100)
        """
        # 提取最后一个交易日的因子值
        factor_values = {}
        for column in capital_factors.columns:
            factor_values[column] = capital_factors[column].iloc[-1]
        
        # 计算资金面子项得分
        sub_scores = {}
        
        # 聪明资金流得分 (0-100)
        if 'smart_money_flow' in factor_values:
            smart_money = factor_values['smart_money_flow']
            # 标准化到0-100区间
            sub_scores['smart_money'] = min(100, max(0, 50 + smart_money * 25))
        else:
            sub_scores['smart_money'] = 50
        
        # 筹码集中度得分 (0-100)
        if 'accumulation_index' in factor_values:
            accumulation = factor_values['accumulation_index']
            sub_scores['accumulation'] = min(100, max(0, 50 + accumulation * 20))
        else:
            sub_scores['accumulation'] = 50
        
        # 资金流强度得分 (0-100)
        if 'fund_flow_strength' in factor_values:
            flow_strength = factor_values['fund_flow_strength']
            # 将资金流强度转换为评分
            magnitude = abs(flow_strength)
            direction = np.sign(flow_strength)
            sub_scores['flow_strength'] = min(100, max(0, 50 + direction * min(magnitude * 0.0001, 50)))
        else:
            sub_scores['flow_strength'] = 50
        
        # 资金流一致性得分 (0-100)
        if 'fund_flow_consistency' in factor_values:
            consistency = factor_values['fund_flow_consistency']
            sub_scores['consistency'] = min(100, max(0, consistency * 100))
        else:
            sub_scores['consistency'] = 50
        
        # 资金流背离得分 (0-100)
        if 'fund_flow_divergence' in factor_values:
            divergence = factor_values['fund_flow_divergence']
            # 背离为负值时表示资金流和价格正相关，为正值时表示背离
            sub_scores['divergence'] = min(100, max(0, 50 - divergence * 50))
        else:
            sub_scores['divergence'] = 50
        
        # 计算加权得分
        weights = {
            'smart_money': 0.30,
            'accumulation': 0.15,
            'flow_strength': 0.25,
            'consistency': 0.15,
            'divergence': 0.15
        }
        
        capital_score = sum(score * weights[name] for name, score in sub_scores.items())
        return capital_score
    
    def _calculate_nonlinear_score(self, nonlinear_factors: pd.Series) -> float:
        """
        计算非线性因子评分
        
        Parameters
        ----------
        nonlinear_factors : pd.Series
            非线性因子Series
            
        Returns
        -------
        float
            非线性因子评分 (0-100)
        """
        # 初始化评分
        if nonlinear_factors.empty:
            return 50.0
            
        # 计算非线性因子子项得分
        sub_scores = {}
        
        # 相空间熵得分 (0-100)
        if 'phase_space_entropy' in nonlinear_factors:
            entropy = nonlinear_factors['phase_space_entropy']
            # 熵值越高，系统越混沌，得分越低
            sub_scores['entropy'] = min(100, max(0, 100 - entropy * 100))
        else:
            sub_scores['entropy'] = 50
        
        # Lyapunov指数得分 (0-100)
        if 'lyapunov_exponent' in nonlinear_factors:
            lyapunov = nonlinear_factors['lyapunov_exponent']
            # Lyapunov指数为正表示混沌，为负表示稳定
            if lyapunov > 0:
                sub_scores['lyapunov'] = max(0, 50 - lyapunov * 50)
            else:
                sub_scores['lyapunov'] = min(100, 50 + abs(lyapunov) * 50)
        else:
            sub_scores['lyapunov'] = 50
        
        # 多重分形谱宽度得分 (0-100)
        if 'multifractal_spectrum_width' in nonlinear_factors:
            mf_width = nonlinear_factors['multifractal_spectrum_width']
            # 宽度越大，系统越复杂，得分越低
            sub_scores['multifractal'] = min(100, max(0, 100 - mf_width * 50))
        else:
            sub_scores['multifractal'] = 50
        
        # Hurst指数得分 (0-100)
        if 'hurst_exponent' in nonlinear_factors:
            hurst = nonlinear_factors['hurst_exponent']
            # Hurst > 0.5: 趋势持续性，得分高
            # Hurst < 0.5: 反趋势，得分低
            # Hurst = 0.5: 随机游走，得分中等
            if hurst > 0.5:
                sub_scores['hurst'] = min(100, 50 + (hurst - 0.5) * 100)
            else:
                sub_scores['hurst'] = max(0, 50 - (0.5 - hurst) * 100)
        else:
            sub_scores['hurst'] = 50
        
        # 持续性同调得分 (0-100)
        if 'persistent_homology' in nonlinear_factors:
            homology = nonlinear_factors['persistent_homology']
            # 归一化到0-100
            sub_scores['homology'] = min(100, max(0, 50 + homology * 50))
        else:
            sub_scores['homology'] = 50
        
        # 计算加权得分
        weights = {
            'entropy': 0.20,
            'lyapunov': 0.20,
            'multifractal': 0.15,
            'hurst': 0.25,
            'homology': 0.20
        }
        
        nonlinear_score = sum(score * weights[name] for name, score in sub_scores.items())
        return nonlinear_score
    
    def _calculate_topology_score(self, topology_factors: pd.Series) -> float:
        """
        计算拓扑因子评分
        
        Parameters
        ----------
        topology_factors : pd.Series
            拓扑因子Series
            
        Returns
        -------
        float
            拓扑因子评分 (0-100)
        """
        # 初始化评分
        if topology_factors.empty:
            return 50.0
            
        # 计算拓扑因子子项得分
        sub_scores = {}
        
        # 持续性同调得分 (0-100)
        if 'persistent_homology' in topology_factors:
            homology = topology_factors['persistent_homology']
            sub_scores['homology'] = min(100, max(0, 50 + homology * 50))
        else:
            sub_scores['homology'] = 50
        
        # 沃瑟斯坦距离得分 (0-100)
        if 'wasserstein_distance' in topology_factors:
            distance = topology_factors['wasserstein_distance']
            # 距离越小，匹配越好，得分越高
            sub_scores['wasserstein'] = min(100, max(0, 100 - distance * 100))
        else:
            sub_scores['wasserstein'] = 50
        
        # 拓扑特征得分 (0-100)
        if 'topological_features' in topology_factors:
            features = topology_factors['topological_features']
            sub_scores['features'] = min(100, max(0, features * 100))
        else:
            sub_scores['features'] = 50
        
        # 同伦不变量得分 (0-100)
        if 'homotopy_invariants' in topology_factors:
            homotopy = topology_factors['homotopy_invariants']
            sub_scores['homotopy'] = min(100, max(0, 50 + homotopy * 30))
        else:
            sub_scores['homotopy'] = 50
        
        # 拓扑熵得分 (0-100)
        if 'topological_entropy' in topology_factors:
            entropy = topology_factors['topological_entropy']
            # 熵值越高，混沌性越强，稳定性越差
            sub_scores['entropy'] = min(100, max(0, 100 - entropy * 80))
        else:
            sub_scores['entropy'] = 50
        
        # 拓扑复杂度得分 (0-100)
        if 'topological_complexity' in topology_factors:
            complexity = topology_factors['topological_complexity']
            # 复杂度越高，系统越不稳定
            sub_scores['complexity'] = min(100, max(0, 100 - complexity * 70))
        else:
            sub_scores['complexity'] = 50
        
        # 拓扑稳定性得分 (0-100)
        if 'topological_stability' in topology_factors:
            stability = topology_factors['topological_stability']
            sub_scores['stability'] = min(100, max(0, stability * 100))
        else:
            sub_scores['stability'] = 50
        
        # 拓扑相变检测得分 (0-100)
        if 'phase_transition_detection' in topology_factors:
            phase_transition = topology_factors['phase_transition_detection']
            # 相变检测值越高，系统越不稳定
            sub_scores['phase_transition'] = min(100, max(0, 100 - phase_transition * 80))
        else:
            sub_scores['phase_transition'] = 50
        
        # 计算加权得分
        weights = {
            'homology': 0.15,
            'wasserstein': 0.10,
            'features': 0.10,
            'homotopy': 0.10,
            'entropy': 0.15,
            'complexity': 0.10,
            'stability': 0.15,
            'phase_transition': 0.15
        }
        
        topology_score = sum(score * weights[name] for name, score in sub_scores.items())
        return topology_score
    
    def _calculate_volume_price_score(self, volume_price_factors: Dict[str, float]) -> float:
        """
        计算量价关系因子评分
        
        Parameters
        ----------
        volume_price_factors : Dict[str, float]
            量价关系因子字典
            
        Returns
        -------
        float
            量价关系因子评分 (0-100)
        """
        # 初始化评分
        if not volume_price_factors:
            return 50.0
            
        # 计算量价关系因子子项得分
        sub_scores = {}
        
        # 抗分歧强度评分 (DRS) (0-100)
        if 'drs_score' in volume_price_factors:
            drs = volume_price_factors['drs_score']
            # DRS已经是0-100的评分，直接使用
            sub_scores['drs'] = min(100, max(0, drs))
        else:
            sub_scores['drs'] = 50
        
        # 空方占优风险评分 (BDR) (0-100)
        if 'bdr_score' in volume_price_factors:
            bdr = volume_price_factors['bdr_score']
            # BDR是风险评分，需要反转：风险越高，得分越低
            sub_scores['bdr'] = min(100, max(0, 100 - bdr))
        else:
            sub_scores['bdr'] = 50
        
        # 最终量价关系总分 (FPVS) (0-100)
        if 'fpvs_score' in volume_price_factors:
            fpvs = volume_price_factors['fpvs_score']
            # FPVS已经是0-100的评分，直接使用
            sub_scores['fpvs'] = min(100, max(0, fpvs))
        else:
            sub_scores['fpvs'] = 50
        
        # 成交量比率因子得分 (0-100)
        if 'volume_ratio_factor' in volume_price_factors:
            vol_ratio = volume_price_factors['volume_ratio_factor']
            # 成交量比率：>1.5为爆量(高分)，<0.5为缩量(低分)
            if vol_ratio > 1.5:
                sub_scores['volume_ratio'] = min(100, 70 + (vol_ratio - 1.5) * 20)
            elif vol_ratio < 0.5:
                sub_scores['volume_ratio'] = max(0, 30 + (vol_ratio - 0.5) * 40)
            else:
                sub_scores['volume_ratio'] = 30 + (vol_ratio - 0.5) * 40
        else:
            sub_scores['volume_ratio'] = 50
        
        # 价格动量因子得分 (0-100)
        if 'price_momentum_factor' in volume_price_factors:
            price_momentum = volume_price_factors['price_momentum_factor']
            # 价格动量：正值为上涨动量，负值为下跌动量
            sub_scores['price_momentum'] = min(100, max(0, 50 + price_momentum * 100))
        else:
            sub_scores['price_momentum'] = 50
        
        # 趋势一致性因子得分 (0-100)
        if 'trend_consistency_factor' in volume_price_factors:
            trend_consistency = volume_price_factors['trend_consistency_factor']
            # 趋势一致性：1为完全一致，-1为完全背离
            sub_scores['trend_consistency'] = min(100, max(0, 50 + trend_consistency * 50))
        else:
            sub_scores['trend_consistency'] = 50
        
        # 成交量爆发信号得分 (0-100)
        if 'volume_burst_signal' in volume_price_factors:
            vol_burst = volume_price_factors['volume_burst_signal']
            # 爆发信号：1为强烈爆发，0为无爆发
            sub_scores['volume_burst'] = min(100, max(0, vol_burst * 100))
        else:
            sub_scores['volume_burst'] = 50
        
        # 成交量萎缩信号得分 (0-100)
        if 'volume_shrink_signal' in volume_price_factors:
            vol_shrink = volume_price_factors['volume_shrink_signal']
            # 萎缩信号：1为强烈萎缩(低分)，0为无萎缩
            sub_scores['volume_shrink'] = min(100, max(0, 100 - vol_shrink * 100))
        else:
            sub_scores['volume_shrink'] = 50
        
        # 价量背离信号得分 (0-100)
        if 'price_volume_divergence' in volume_price_factors:
            pv_divergence = volume_price_factors['price_volume_divergence']
            # 背离信号：1为强烈背离(低分)，0为无背离
            sub_scores['pv_divergence'] = min(100, max(0, 100 - abs(pv_divergence) * 100))
        else:
            sub_scores['pv_divergence'] = 50
        
        # 权重配置 - 核心因子权重更高
        weights = {
            'drs': 0.25,              # 抗分歧强度评分 - 核心因子
            'bdr': 0.20,              # 空方占优风险评分 - 核心因子
            'fpvs': 0.25,             # 最终量价关系总分 - 核心因子
            'volume_ratio': 0.10,     # 成交量比率因子
            'price_momentum': 0.08,   # 价格动量因子
            'trend_consistency': 0.05, # 趋势一致性因子
            'volume_burst': 0.03,     # 成交量爆发信号
            'volume_shrink': 0.02,    # 成交量萎缩信号
            'pv_divergence': 0.02     # 价量背离信号
        }
        
        volume_price_score = sum(score * weights[name] for name, score in sub_scores.items())
        return volume_price_score