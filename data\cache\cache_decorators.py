# cache_decorators.py
import functools
import logging
from datetime import datetime
from typing import Callable, Any, Optional

from data.cache.cache_manager import <PERSON>ache<PERSON>anager


def cached_data(cache_name: str, file_format: str = "json", max_age_minutes: int = 60):
    """
    数据缓存装饰器，用于自动缓存函数返回的DataFrame数据
    
    :param cache_name: 缓存名称
    :param file_format: 缓存文件格式，支持json和csv
    :param max_age_minutes: 缓存有效期（分钟）
    :return: 装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取实例的logger，如果没有则创建一个新的logger
            instance = args[0] if args else None
            logger = getattr(instance, 'logger', logging.getLogger(__name__))
            
            # 获取缓存管理器，如果实例有cache_manager属性则使用，否则创建新的
            cache_manager = getattr(instance, 'cache_manager', CacheManager())
            
            # 检查是否有有效缓存
            use_cache = kwargs.pop('use_cache', True)  # 默认使用缓存
            force_refresh = kwargs.pop('force_refresh', False)  # 默认不强制刷新
            
            if use_cache and not force_refresh and cache_manager.is_cache_valid(
                    cache_name, max_age_minutes, file_format):
                # 从缓存加载数据
                logger.info(f"从缓存加载数据: {cache_name}.{file_format}")
                df = cache_manager.load_dataframe_from_cache(cache_name, file_format)
                if df is not None:
                    return df
            
            # 如果没有有效缓存或强制刷新，则调用原函数获取数据
            logger.info(f"从API获取新数据: {func.__name__}")
            df = func(*args, **kwargs)
            
            # 将结果保存到缓存
            if df is not None and use_cache:
                cache_manager.save_dataframe_to_cache(df, cache_name, file_format)
            
            return df
        
        return wrapper
    
    return decorator
