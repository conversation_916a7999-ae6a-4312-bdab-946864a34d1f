#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票评分任务

封装单只股票的并行评分逻辑，支持因子并行计算

作者: LilyBullRider团队
创建时间: 2024
"""

from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, Optional, Callable
import pandas as pd
from stock_scoring.models import StockScoringModelV4_1
from stock_scoring.factors.technical_factors import TechnicalFactors
from stock_scoring.factors.capital_factors import CapitalFactors
from stock_scoring.factors.nonlinear_factors import NonlinearFactors
from stock_scoring.factors.topology_factors import TopologyFactors
from utils.logging_config import get_logger


class StockScoringTask:
    """股票评分任务类"""
    
    def __init__(self, stock_code: str, stock_name: str, stock_data: pd.DataFrame,
                 scoring_model: StockScoringModelV4_1, start_date: str, end_date: str,
                 window: int = 30):
        """
        初始化股票评分任务
        
        Parameters
        ----------
        stock_code : str
            股票代码
        stock_name : str
            股票名称
        stock_data : pd.DataFrame
            股票历史数据
        scoring_model : StockScoringModelV4_1
            评分模型实例
        start_date : str
            开始日期
        end_date : str
            结束日期
        window : int
            计算窗口大小
        """
        self.stock_code = stock_code
        self.stock_name = stock_name
        self.stock_data = stock_data
        self.scoring_model = scoring_model
        self.start_date = start_date
        self.end_date = end_date
        self.window = window
        self.logger = get_logger(__name__)
        
    def calculate_technical_factors(self) -> Dict[str, Any]:
        """
        计算技术面因子
        
        Returns
        -------
        Dict[str, Any]
            技术面因子结果
        """
        try:
            tf = TechnicalFactors(data=self.stock_data)
            technical_factors = tf.calculate_all_factors(window=self.window)
            technical_score = self.scoring_model._calculate_technical_score(technical_factors)
            return {
                'score': technical_score,
                'factors': technical_factors,
                'error': None
            }
        except Exception as e:
            error_msg = f"计算股票 {self.stock_code} 技术面因子失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                'score': 50.0,
                'factors': {},
                'error': error_msg
            }
    
    def calculate_capital_factors(self) -> Dict[str, Any]:
        """
        计算资金面因子
        
        Returns
        -------
        Dict[str, Any]
            资金面因子结果
        """
        try:
            # 获取资金流数据
            symbol = f"sh{self.stock_code}" if self.stock_code.startswith('6') else f"sz{self.stock_code}"
            capital_data = CapitalFactors.get_fund_flow_data(
                stock_code=self.stock_code,
                market='sh' if self.stock_code.startswith('6') else 'sz',
                start_date=self.start_date,
                end_date=self.end_date
            )
            
            if capital_data.empty:
                raise ValueError("获取资金流数据失败")
            
            cf = CapitalFactors(data=capital_data)
            capital_factors = cf.calculate_all_factors()
            capital_score = self.scoring_model._calculate_capital_score(capital_factors)
            
            return {
                'score': capital_score,
                'factors': capital_factors,
                'error': None
            }
        except Exception as e:
            error_msg = f"计算股票 {self.stock_code} 资金面因子失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                'score': 50.0,
                'factors': {},
                'error': error_msg
            }
    
    def calculate_nonlinear_factors(self) -> Dict[str, Any]:
        """
        计算非线性因子
        
        Returns
        -------
        Dict[str, Any]
            非线性因子结果
        """
        try:
            nf = NonlinearFactors(data=self.stock_data)
            nonlinear_factors = nf.calculate_all_factors(window=self.window)
            nonlinear_score = self.scoring_model._calculate_nonlinear_score(nonlinear_factors)
            
            return {
                'score': nonlinear_score,
                'factors': nonlinear_factors,
                'error': None
            }
        except Exception as e:
            error_msg = f"计算股票 {self.stock_code} 非线性因子失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                'score': 50.0,
                'factors': {},
                'error': error_msg
            }
    
    def calculate_topology_factors(self) -> Dict[str, Any]:
        """
        计算拓扑因子
        
        Returns
        -------
        Dict[str, Any]
            拓扑因子结果
        """
        try:
            tp = TopologyFactors(data=self.stock_data)
            topology_factors = tp.calculate_all_factors(window=self.window)
            topology_score = self.scoring_model._calculate_topology_score(topology_factors)
            
            return {
                'score': topology_score,
                'factors': topology_factors,
                'error': None
            }
        except Exception as e:
            error_msg = f"计算股票 {self.stock_code} 拓扑因子失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                'score': 50.0,
                'factors': {},
                'error': error_msg
            }
    
    def calculate_factors_parallel(self, max_workers: int = 4) -> Dict[str, Any]:
        """
        并行计算所有因子
        
        Parameters
        ----------
        max_workers : int
            最大线程数
            
        Returns
        -------
        Dict[str, Any]
            所有因子的计算结果
        """
        # 定义因子计算任务
        factor_tasks = {
            'technical': self.calculate_technical_factors,
            'capital': self.calculate_capital_factors,
            'nonlinear': self.calculate_nonlinear_factors,
            'topology': self.calculate_topology_factors
        }
        
        results = {}
        
        # 并行执行因子计算
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_factor = {
                executor.submit(task_func): factor_name
                for factor_name, task_func in factor_tasks.items()
            }
            
            # 收集结果
            for future in as_completed(future_to_factor):
                factor_name = future_to_factor[future]
                try:
                    result = future.result()
                    results[factor_name] = result
                except Exception as e:
                    self.logger.error(f"因子 {factor_name} 计算异常: {str(e)}")
                    results[factor_name] = {
                        'score': 50.0,
                        'factors': {},
                        'error': str(e)
                    }
        
        return results
    
    def get_final_score(self, parallel_factors: bool = True) -> Dict[str, Any]:
        """
        获取最终评分
        
        Parameters
        ----------
        parallel_factors : bool
            是否并行计算因子
            
        Returns
        -------
        Dict[str, Any]
            最终评分结果
        """
        try:
            # 获取市场状态和权重
            market_state, market_state_name = self.scoring_model.get_market_state()
            weights = self.scoring_model.get_weights(market_state_name)
            
            # 计算各因子评分
            if parallel_factors:
                factor_results = self.calculate_factors_parallel()
            else:
                # 串行计算（作为fallback）
                factor_results = {
                    'technical': self.calculate_technical_factors(),
                    'capital': self.calculate_capital_factors(),
                    'nonlinear': self.calculate_nonlinear_factors(),
                    'topology': self.calculate_topology_factors()
                }
            
            # 提取各因子评分
            scores = {
                'technical_score': factor_results['technical']['score'],
                'capital_score': factor_results['capital']['score'],
                'nonlinear_score': factor_results['nonlinear']['score'],
                'topology_score': factor_results['topology']['score']
            }
            
            # 计算总评分
            total_score = (
                weights['technical'] * scores['technical_score'] +
                weights['capital'] * scores['capital_score'] +
                weights['nonlinear'] * scores['nonlinear_score'] +
                weights['topology'] * scores['topology_score']
            )
            
            # 收集详细因子数据
            detail_scores = {
                'technical': factor_results['technical']['factors'],
                'capital': factor_results['capital']['factors'],
                'nonlinear': factor_results['nonlinear']['factors'],
                'topology': factor_results['topology']['factors']
            }
            
            # 收集错误信息
            errors = []
            for factor_name, result in factor_results.items():
                if result['error']:
                    errors.append(f"{factor_name}: {result['error']}")
            
            return {
                'stock_code': self.stock_code,
                'stock_name': self.stock_name,
                'total_score': float(total_score),
                'technical_score': float(scores['technical_score']),
                'capital_score': float(scores['capital_score']),
                'nonlinear_score': float(scores['nonlinear_score']),
                'topology_score': float(scores['topology_score']),
                'market_state': market_state_name,
                'weights': weights,
                'detail_scores': detail_scores,
                'errors': errors
            }
            
        except Exception as e:
            error_msg = f"股票 {self.stock_code} 最终评分计算失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                'stock_code': self.stock_code,
                'stock_name': self.stock_name,
                'total_score': 50.0,
                'technical_score': 50.0,
                'capital_score': 50.0,
                'nonlinear_score': 50.0,
                'topology_score': 50.0,
                'market_state': 'unknown',
                'weights': {},
                'detail_scores': {},
                'errors': [error_msg]
            }