# -*- coding: utf-8 -*-
"""
响应式布局管理器
实现基于窗口大小的自适应布局调整
"""

import logging
from typing import Dict, List, Tuple, Optional
from PyQt6.QtCore import QTimer, QSettings, QSize, pyqtSignal, QObject
from PyQt6.QtWidgets import (
    QWidget, QSplitter, QTableWidget, QHeaderView, 
    QApplication, QMainWindow
)
from PyQt6.QtCore import Qt

logger = logging.getLogger(__name__)

class ResponsiveLayoutManager(QObject):
    """
    响应式布局管理器
    根据窗口大小自动调整布局比例和组件可见性
    """
    
    # 信号定义
    layout_changed = pyqtSignal(str)  # 布局模式变化信号
    
    # 断点定义
    BREAKPOINTS = {
        'small': 1200,   # 小屏幕
        'medium': 1600,  # 中等屏幕
        'large': 2000    # 大屏幕
    }
    
    # 布局配置
    LAYOUT_CONFIGS = {
        'small': {
            'splitter_ratios': [35, 65],  # 两列布局，增加左侧比例
            'min_widths': [250, 300],
            'hide_panels': ['right'],  # 隐藏右侧面板
            'table_columns': ['essential']  # 只显示核心列
        },
        'medium': {
            'splitter_ratios': [30, 40, 30],  # 三列布局，平衡分配
            'min_widths': [250, 300, 250],
            'hide_panels': [],
            'table_columns': ['essential', 'important']
        },
        'large': {
            'splitter_ratios': [30, 40, 30],  # 三列布局，平衡分配
            'min_widths': [300, 400, 300],
            'hide_panels': [],
            'table_columns': ['all']
        }
    }
    
    # 表格列配置
    TABLE_COLUMN_CONFIG = {
        'essential': ['股票代码', '股票名称', '最新价', '涨跌幅'],
        'important': ['成交额', '分数'],
        'all': ['股票代码', '股票名称', '最新价', '成交额', '涨跌幅', '分数']
    }
    
    def __init__(self, main_window: QMainWindow):
        super().__init__()
        self.main_window = main_window
        self.current_mode = 'medium'
        self.resize_timer = QTimer()
        self.resize_timer.setSingleShot(True)
        self.resize_timer.timeout.connect(self._apply_layout)
        
        # 组件引用
        self.main_splitter: Optional[QSplitter] = None
        self.constituent_table: Optional[QTableWidget] = None
        self.left_panel: Optional[QWidget] = None
        self.middle_panel: Optional[QWidget] = None
        self.right_panel: Optional[QWidget] = None
        
        # 设置管理
        self.settings = QSettings('LilyBullRider', 'ResponsiveLayout')
        
        logger.info("响应式布局管理器初始化完成")
    
    def register_components(self, 
                          main_splitter: QSplitter,
                          constituent_table: QTableWidget,
                          left_panel: QWidget,
                          middle_panel: QWidget,
                          right_panel: QWidget):
        """
        注册需要管理的组件
        """
        self.main_splitter = main_splitter
        self.constituent_table = constituent_table
        self.left_panel = left_panel
        self.middle_panel = middle_panel
        self.right_panel = right_panel
        
        # 设置最小尺寸
        self._setup_minimum_sizes()
        
        # 恢复保存的布局状态
        self._restore_layout_state()
        
        logger.info("组件注册完成")
    
    def on_window_resize(self, size: QSize):
        """
        窗口大小变化处理
        使用防抖机制避免频繁调整
        """
        self.resize_timer.stop()
        self.resize_timer.start(100)  # 100ms防抖
        
    def _apply_layout(self):
        """
        应用布局调整
        """
        if not self.main_window:
            return
            
        window_width = self.main_window.width()
        new_mode = self._get_layout_mode(window_width)
        
        if new_mode != self.current_mode:
            logger.info(f"布局模式切换: {self.current_mode} -> {new_mode}")
            self.current_mode = new_mode
            self.layout_changed.emit(new_mode)
        
        self._adjust_splitter_ratios()
        self._adjust_table_columns()
        self._adjust_panel_visibility()
        
    def _get_layout_mode(self, width: int) -> str:
        """
        根据窗口宽度确定布局模式
        """
        if width < self.BREAKPOINTS['small']:
            return 'small'
        elif width < self.BREAKPOINTS['medium']:
            return 'medium'
        else:
            return 'large'
    
    def _adjust_splitter_ratios(self):
        """
        调整分割器比例
        """
        if not self.main_splitter:
            return
            
        config = self.LAYOUT_CONFIGS[self.current_mode]
        ratios = config['splitter_ratios']
        min_widths = config['min_widths']
        
        # 计算总宽度
        total_width = self.main_splitter.width()
        if total_width <= 0:
            return
        
        # 计算实际尺寸
        sizes = []
        for i, ratio in enumerate(ratios):
            size = max(int(total_width * ratio / 100), min_widths[i])
            sizes.append(size)
        
        # 应用尺寸
        self.main_splitter.setSizes(sizes)
        
        logger.debug(f"分割器比例调整: {ratios} -> {sizes}")
    
    def _adjust_table_columns(self):
        """
        调整表格列显示
        """
        if not self.constituent_table:
            return
            
        config = self.LAYOUT_CONFIGS[self.current_mode]
        column_mode = config['table_columns'][0] if config['table_columns'] else 'all'
        
        # 获取要显示的列
        if column_mode == 'essential':
            visible_columns = self.TABLE_COLUMN_CONFIG['essential']
        elif column_mode == 'important':
            visible_columns = self.TABLE_COLUMN_CONFIG['essential'] + self.TABLE_COLUMN_CONFIG['important']
        else:
            visible_columns = self.TABLE_COLUMN_CONFIG['all']
        
        # 调整列可见性
        header = self.constituent_table.horizontalHeader()
        for i in range(self.constituent_table.columnCount()):
            header_text = self.constituent_table.horizontalHeaderItem(i)
            if header_text:
                column_name = header_text.text()
                is_visible = column_name in visible_columns
                self.constituent_table.setColumnHidden(i, not is_visible)
        
        # 调整列宽
        self._adjust_column_widths()
        
        logger.debug(f"表格列调整: {column_mode} -> {visible_columns}")
    
    def _adjust_column_widths(self):
        """
        动态调整列宽
        """
        if not self.constituent_table:
            return
            
        header = self.constituent_table.horizontalHeader()
        table_width = self.constituent_table.width()
        
        # 获取可见列数量
        visible_columns = []
        for i in range(self.constituent_table.columnCount()):
            if not self.constituent_table.isColumnHidden(i):
                visible_columns.append(i)
        
        if not visible_columns:
            return
        
        # 列宽分配策略
        column_weights = {
            '股票代码': 1.0,
            '股票名称': 1.5,
            '最新价': 1.0,
            '成交额': 1.2,
            '涨跌幅': 1.0,
            '分数': 0.8
        }
        
        # 计算总权重
        total_weight = 0
        for col_idx in visible_columns:
            header_item = self.constituent_table.horizontalHeaderItem(col_idx)
            if header_item:
                column_name = header_item.text()
                total_weight += column_weights.get(column_name, 1.0)
        
        # 分配列宽
        available_width = table_width - 20  # 预留滚动条空间
        for col_idx in visible_columns:
            header_item = self.constituent_table.horizontalHeaderItem(col_idx)
            if header_item:
                column_name = header_item.text()
                weight = column_weights.get(column_name, 1.0)
                width = int(available_width * weight / total_weight)
                self.constituent_table.setColumnWidth(col_idx, width)
    
    def _adjust_panel_visibility(self):
        """
        调整面板可见性
        """
        config = self.LAYOUT_CONFIGS[self.current_mode]
        hide_panels = config.get('hide_panels', [])
        
        # 调整右侧面板可见性
        if self.right_panel:
            should_hide = 'right' in hide_panels
            self.right_panel.setVisible(not should_hide)
            
        logger.debug(f"面板可见性调整: 隐藏 {hide_panels}")
    
    def _setup_minimum_sizes(self):
        """
        设置组件最小尺寸
        """
        if self.left_panel:
            self.left_panel.setMinimumWidth(200)
        if self.middle_panel:
            self.middle_panel.setMinimumWidth(300)
        if self.right_panel:
            self.right_panel.setMinimumWidth(250)
            
        logger.debug("最小尺寸设置完成")
    
    def save_layout_state(self):
        """
        保存布局状态
        """
        if self.main_splitter:
            self.settings.setValue('splitter_state', self.main_splitter.saveState())
            self.settings.setValue('splitter_sizes', self.main_splitter.sizes())
        
        self.settings.setValue('current_mode', self.current_mode)
        logger.debug("布局状态已保存")
    
    def _restore_layout_state(self):
        """
        恢复布局状态
        """
        if self.main_splitter:
            state = self.settings.value('splitter_state')
            if state:
                self.main_splitter.restoreState(state)
        
        saved_mode = self.settings.value('current_mode', 'medium')
        if saved_mode in self.LAYOUT_CONFIGS:
            self.current_mode = saved_mode
            
        logger.debug(f"布局状态已恢复: {self.current_mode}")
    
    def get_current_mode(self) -> str:
        """
        获取当前布局模式
        """
        return self.current_mode
    
    def force_layout_mode(self, mode: str):
        """
        强制设置布局模式
        """
        if mode in self.LAYOUT_CONFIGS:
            self.current_mode = mode
            self._apply_layout()
            logger.info(f"强制设置布局模式: {mode}")
    
    def get_optimal_window_size(self) -> QSize:
        """
        获取当前模式下的最优窗口大小
        """
        config = self.LAYOUT_CONFIGS[self.current_mode]
        min_widths = config['min_widths']
        
        optimal_width = sum(min_widths) + 50  # 预留边距
        optimal_height = 800  # 默认高度
        
        return QSize(optimal_width, optimal_height)