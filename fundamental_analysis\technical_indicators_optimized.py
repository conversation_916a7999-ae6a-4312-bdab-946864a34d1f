# -*- coding: utf-8 -*-
"""
技术指标短线化优化模块
针对短线和超短线交易优化的技术指标计算

主要改进：
1. RSI参数从14日优化为6日，提高敏感度
2. 引入3日和5日双动量系统
3. 基于EWMA模型预测次日波动率
4. 支持向量化计算和异常处理
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from scipy import stats
import warnings
from utils.logging_config import get_logger

warnings.filterwarnings('ignore')
logger = get_logger(__name__)

class TechnicalIndicatorsOptimized:
    """
    优化的技术指标计算器
    专为短线交易设计的技术指标系统
    """
    
    def __init__(self, historical_data: Optional[Dict] = None):
        """
        初始化技术指标计算器
        
        Parameters:
        -----------
        historical_data : dict, optional
            历史数据字典，格式: {sector_name: [price_list]}
        """
        self.historical_data = historical_data or {}
        self.rsi_period = 6  # 短线RSI周期
        self.momentum_periods = [3, 5]  # 双动量周期
        self.volatility_lambda = 0.94  # EWMA衰减因子
        
        # 缓存计算结果
        self._rsi_cache = {}
        self._momentum_cache = {}
        self._volatility_cache = {}
        
        logger.info(f"技术指标优化器初始化完成 - RSI周期:{self.rsi_period}, 动量周期:{self.momentum_periods}")
    
    def calculate_optimized_rsi(self, sector_name: str, period: int = None) -> float:
        """
        计算优化的6日RSI指标
        
        Parameters:
        -----------
        sector_name : str
            板块名称
        period : int, optional
            RSI计算周期，默认使用6日
            
        Returns:
        --------
        float
            RSI值，范围0-100
        """
        try:
            period = period or self.rsi_period
            cache_key = f"{sector_name}_{period}"
            
            # 检查缓存
            if cache_key in self._rsi_cache:
                return self._rsi_cache[cache_key]
            
            # 检查历史数据
            if sector_name not in self.historical_data:
                # 基于板块名称生成确定性的中性值
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_rsi6", 1000)
                rsi_value = 50 + (hash_val - 500) * 0.02  # 在40-60之间
                self._rsi_cache[cache_key] = max(0, min(100, rsi_value))
                return self._rsi_cache[cache_key]
            
            history = self.historical_data[sector_name]
            if len(history) < 3:  # 最少需要3个数据点
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_rsi6_short", 1000)
                rsi_value = 50 + (hash_val - 500) * 0.02
                self._rsi_cache[cache_key] = max(0, min(100, rsi_value))
                return self._rsi_cache[cache_key]
            
            # 使用有效周期
            effective_period = min(period, len(history) - 1)
            if effective_period < 2:
                effective_period = len(history) - 1
            
            # 计算价格变化
            price_changes = np.diff(history[-effective_period-1:])
            
            # 分离涨跌
            gains = np.where(price_changes > 0, price_changes, 0)
            losses = np.where(price_changes < 0, -price_changes, 0)
            
            # 使用指数移动平均计算平均涨跌幅（更适合短线）
            if len(gains) > 0:
                # 对于短周期，使用简单平均而非EMA，提高敏感度
                avg_gain = np.mean(gains) if np.sum(gains) > 0 else 0
                avg_loss = np.mean(losses) if np.sum(losses) > 0 else 0
            else:
                avg_gain = avg_loss = 0
            
            # 处理边界情况
            if avg_gain == 0 and avg_loss == 0:
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_history_{len(history)}", 1000)
                rsi_value = 50 + (hash_val - 500) * 0.02
                self._rsi_cache[cache_key] = max(0, min(100, rsi_value))
                return self._rsi_cache[cache_key]
            
            if avg_loss == 0:
                # 全部上涨，RSI接近100但根据涨幅区分
                gain_strength = min(np.sum(gains) * 10, 20)  # 涨幅强度
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_gain", 100)
                rsi_value = 80 + gain_strength - hash_val * 0.1
                self._rsi_cache[cache_key] = max(70, min(100, rsi_value))
                return self._rsi_cache[cache_key]
            
            if avg_gain == 0:
                # 全部下跌，RSI接近0但根据跌幅区分
                loss_strength = min(np.sum(losses) * 10, 20)  # 跌幅强度
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_loss", 100)
                rsi_value = 20 - loss_strength + hash_val * 0.1
                self._rsi_cache[cache_key] = max(0, min(30, rsi_value))
                return self._rsi_cache[cache_key]
            
            # 计算RSI
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            # 短线敏感度增强：放大RSI的变化幅度
            if rsi > 50:
                rsi = 50 + (rsi - 50) * 1.2  # 上涨时放大
            else:
                rsi = 50 - (50 - rsi) * 1.2  # 下跌时放大
            
            # 添加板块差异化
            from .deterministic_hash import deterministic_hash
            hash_val = deterministic_hash(f"{sector_name}_adjustment", 100)
            rsi_adjustment = (hash_val - 50) * 0.05  # ±2.5的调整
            rsi = rsi + rsi_adjustment
            
            # 限制范围
            rsi = max(0, min(100, rsi))
            
            # 缓存结果
            self._rsi_cache[cache_key] = rsi
            return rsi
            
        except Exception as e:
            logger.warning(f"优化RSI计算异常 {sector_name}: {e}")
            # 异常时返回基于板块名称的确定性值
            from .deterministic_hash import deterministic_hash
            hash_val = deterministic_hash(f"{sector_name}_rsi6_error", 1000)
            return 50 + (hash_val - 500) * 0.02
    
    def calculate_dual_momentum(self, sector_name: str) -> Dict[str, float]:
        """
        计算3日和5日双动量系统
        
        Parameters:
        -----------
        sector_name : str
            板块名称
            
        Returns:
        --------
        dict
            包含momentum_3d, momentum_5d, momentum_signal的字典
        """
        try:
            cache_key = sector_name
            
            # 检查缓存
            if cache_key in self._momentum_cache:
                return self._momentum_cache[cache_key]
            
            # 检查历史数据
            if sector_name not in self.historical_data:
                # 无历史数据时的处理
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_momentum", 1000)
                momentum_3d = (hash_val - 500) * 0.0002  # ±0.1%
                momentum_5d = momentum_3d * 0.8  # 5日动量通常更平缓
                
                result = {
                    'momentum_3d': momentum_3d,
                    'momentum_5d': momentum_5d,
                    'momentum_signal': self._generate_momentum_signal(momentum_3d, momentum_5d),
                    'momentum_score': 50 + momentum_3d * 500  # 转换为0-100分
                }
                self._momentum_cache[cache_key] = result
                return result
            
            history = self.historical_data[sector_name]
            if len(history) < 4:  # 至少需要4个数据点计算3日动量
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_momentum_short", 1000)
                momentum_3d = (hash_val - 500) * 0.0002
                momentum_5d = momentum_3d * 0.8
                
                result = {
                    'momentum_3d': momentum_3d,
                    'momentum_5d': momentum_5d,
                    'momentum_signal': self._generate_momentum_signal(momentum_3d, momentum_5d),
                    'momentum_score': 50 + momentum_3d * 500
                }
                self._momentum_cache[cache_key] = result
                return result
            
            # 计算3日动量
            if len(history) >= 4:
                momentum_3d = (history[-1] / history[-4] - 1) if history[-4] != 0 else 0
            else:
                momentum_3d = 0
            
            # 计算5日动量
            if len(history) >= 6:
                momentum_5d = (history[-1] / history[-6] - 1) if history[-6] != 0 else 0
            elif len(history) >= 4:
                # 数据不足时用3日动量估算5日动量
                momentum_5d = momentum_3d * 0.8
            else:
                momentum_5d = 0
            
            # 动量信号生成
            momentum_signal = self._generate_momentum_signal(momentum_3d, momentum_5d)
            
            # 动量评分（0-100）
            momentum_score = self._calculate_momentum_score(momentum_3d, momentum_5d, sector_name)
            
            result = {
                'momentum_3d': momentum_3d,
                'momentum_5d': momentum_5d,
                'momentum_signal': momentum_signal,
                'momentum_score': momentum_score
            }
            
            # 缓存结果
            self._momentum_cache[cache_key] = result
            return result
            
        except Exception as e:
            logger.warning(f"双动量计算异常 {sector_name}: {e}")
            # 异常时返回中性值
            return {
                'momentum_3d': 0,
                'momentum_5d': 0,
                'momentum_signal': 'neutral',
                'momentum_score': 50
            }
    
    def _generate_momentum_signal(self, momentum_3d: float, momentum_5d: float) -> str:
        """
        生成动量交易信号
        
        Parameters:
        -----------
        momentum_3d : float
            3日动量
        momentum_5d : float
            5日动量
            
        Returns:
        --------
        str
            交易信号：'strong_buy', 'buy', 'neutral', 'sell', 'strong_sell'
        """
        # 双线交叉策略
        if momentum_3d > momentum_5d and momentum_3d > 0.02:  # 3日线上穿5日线且强势
            return 'strong_buy'
        elif momentum_3d > momentum_5d and momentum_3d > 0:  # 3日线上穿5日线
            return 'buy'
        elif momentum_3d < momentum_5d and momentum_3d < -0.02:  # 3日线下穿5日线且弱势
            return 'strong_sell'
        elif momentum_3d < momentum_5d and momentum_3d < 0:  # 3日线下穿5日线
            return 'sell'
        else:
            return 'neutral'
    
    def _calculate_momentum_score(self, momentum_3d: float, momentum_5d: float, sector_name: str) -> float:
        """
        计算动量综合评分
        
        Parameters:
        -----------
        momentum_3d : float
            3日动量
        momentum_5d : float
            5日动量
        sector_name : str
            板块名称
            
        Returns:
        --------
        float
            动量评分，范围0-100
        """
        try:
            # 基础评分：基于动量大小
            base_score = 50  # 中性分
            
            # 3日动量权重70%
            momentum_3d_score = min(max(momentum_3d * 1000, -50), 50)  # 限制在±50
            
            # 5日动量权重30%
            momentum_5d_score = min(max(momentum_5d * 1000, -50), 50)  # 限制在±50
            
            # 综合评分
            combined_score = base_score + 0.7 * momentum_3d_score + 0.3 * momentum_5d_score
            
            # 趋势一致性加分
            if (momentum_3d > 0 and momentum_5d > 0) or (momentum_3d < 0 and momentum_5d < 0):
                consistency_bonus = min(abs(momentum_3d + momentum_5d) * 500, 10)  # 最多加10分
                combined_score += consistency_bonus
            
            # 板块差异化
            from .deterministic_hash import deterministic_hash
            hash_val = deterministic_hash(f"{sector_name}_momentum_score", 100)
            sector_adjustment = (hash_val - 50) * 0.1  # ±5分调整
            combined_score += sector_adjustment
            
            return max(0, min(100, combined_score))
            
        except Exception as e:
            logger.warning(f"动量评分计算异常: {e}")
            return 50
    
    def predict_next_day_volatility(self, sector_name: str) -> Dict[str, float]:
        """
        基于EWMA模型预测次日波动率
        
        Parameters:
        -----------
        sector_name : str
            板块名称
            
        Returns:
        --------
        dict
            包含predicted_volatility, volatility_score, risk_level的字典
        """
        try:
            cache_key = sector_name
            
            # 检查缓存
            if cache_key in self._volatility_cache:
                return self._volatility_cache[cache_key]
            
            # 检查历史数据
            if sector_name not in self.historical_data:
                # 无历史数据时的处理
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_volatility", 1000)
                predicted_vol = 0.02 + (hash_val / 1000) * 0.03  # 2%-5%的波动率
                
                result = {
                    'predicted_volatility': predicted_vol,
                    'volatility_score': self._volatility_to_score(predicted_vol),
                    'risk_level': self._get_risk_level(predicted_vol)
                }
                self._volatility_cache[cache_key] = result
                return result
            
            history = self.historical_data[sector_name]
            if len(history) < 3:
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_volatility_short", 1000)
                predicted_vol = 0.02 + (hash_val / 1000) * 0.03
                
                result = {
                    'predicted_volatility': predicted_vol,
                    'volatility_score': self._volatility_to_score(predicted_vol),
                    'risk_level': self._get_risk_level(predicted_vol)
                }
                self._volatility_cache[cache_key] = result
                return result
            
            # 计算收益率序列
            returns = np.diff(np.log(history))
            
            # 使用EWMA计算波动率
            if len(returns) >= 5:
                # 标准EWMA模型
                predicted_vol = self._ewma_volatility(returns)
            else:
                # 数据不足时使用简化计算
                predicted_vol = np.std(returns) if len(returns) > 1 else 0.03
            
            # 波动率评分
            volatility_score = self._volatility_to_score(predicted_vol)
            
            # 风险等级
            risk_level = self._get_risk_level(predicted_vol)
            
            result = {
                'predicted_volatility': predicted_vol,
                'volatility_score': volatility_score,
                'risk_level': risk_level
            }
            
            # 缓存结果
            self._volatility_cache[cache_key] = result
            return result
            
        except Exception as e:
            logger.warning(f"波动率预测异常 {sector_name}: {e}")
            return {
                'predicted_volatility': 0.03,
                'volatility_score': 50,
                'risk_level': 'medium'
            }
    
    def _ewma_volatility(self, returns: np.ndarray) -> float:
        """
        使用EWMA计算波动率
        
        Parameters:
        -----------
        returns : np.ndarray
            收益率序列
            
        Returns:
        --------
        float
            预测波动率
        """
        try:
            # EWMA权重
            lambda_param = self.volatility_lambda
            
            # 初始方差（使用前几期的样本方差）
            if len(returns) >= 3:
                initial_var = np.var(returns[:3])
            else:
                initial_var = np.var(returns)
            
            # EWMA递推计算
            ewma_var = initial_var
            for ret in returns:
                ewma_var = lambda_param * ewma_var + (1 - lambda_param) * (ret ** 2)
            
            # 返回波动率（标准差）
            return np.sqrt(ewma_var)
            
        except Exception as e:
            logger.warning(f"EWMA计算异常: {e}")
            return np.std(returns) if len(returns) > 1 else 0.03
    
    def _volatility_to_score(self, volatility: float) -> float:
        """
        将波动率转换为评分
        
        Parameters:
        -----------
        volatility : float
            波动率
            
        Returns:
        --------
        float
            波动率评分，范围0-100
        """
        # 适中波动率（2-4%）得高分，过高或过低都扣分
        if 0.02 <= volatility <= 0.04:
            # 最优区间
            score = 100 - abs(volatility - 0.03) * 1000  # 3%为最优点
        elif volatility < 0.02:
            # 波动率过低
            score = 50 + (volatility - 0.01) * 2500  # 1%以下大幅扣分
        else:
            # 波动率过高
            score = 100 - (volatility - 0.04) * 1000  # 4%以上线性扣分
        
        return max(0, min(100, score))
    
    def _get_risk_level(self, volatility: float) -> str:
        """
        根据波动率确定风险等级
        
        Parameters:
        -----------
        volatility : float
            波动率
            
        Returns:
        --------
        str
            风险等级：'low', 'medium', 'high', 'extreme'
        """
        if volatility < 0.015:
            return 'low'
        elif volatility < 0.03:
            return 'medium'
        elif volatility < 0.06:
            return 'high'
        else:
            return 'extreme'
    
    def calculate_comprehensive_technical_score(self, sector_name: str) -> Dict[str, float]:
        """
        计算综合技术指标评分
        
        Parameters:
        -----------
        sector_name : str
            板块名称
            
        Returns:
        --------
        dict
            综合技术指标评分结果
        """
        try:
            logger.debug(f"开始计算板块 {sector_name} 的综合技术指标评分")
            
            # 计算各项指标
            rsi_score = self.calculate_optimized_rsi(sector_name)
            momentum_data = self.calculate_dual_momentum(sector_name)
            volatility_data = self.predict_next_day_volatility(sector_name)
            
            logger.debug(f"板块 {sector_name} - RSI评分: {rsi_score:.2f}")
            logger.debug(f"板块 {sector_name} - 动量评分: {momentum_data['momentum_score']:.2f}, 信号: {momentum_data['momentum_signal']}")
            logger.debug(f"板块 {sector_name} - 波动率评分: {volatility_data['volatility_score']:.2f}, 风险等级: {volatility_data['risk_level']}")
            
            # 权重分配（针对短线交易优化）
            weights = {
                'rsi': 0.35,        # RSI权重35%（短线重要）
                'momentum': 0.45,   # 动量权重45%（短线最重要）
                'volatility': 0.20  # 波动率权重20%（风险控制）
            }
            
            logger.debug(f"板块 {sector_name} - 权重配置: {weights}")
            
            # 计算综合评分
            comprehensive_score = (
                weights['rsi'] * rsi_score +
                weights['momentum'] * momentum_data['momentum_score'] +
                weights['volatility'] * volatility_data['volatility_score']
            )
            
            logger.debug(f"板块 {sector_name} - 基础综合评分: {comprehensive_score:.2f}")
            
            # 信号强度调整
            signal_adjustment = self._get_signal_adjustment(momentum_data['momentum_signal'])
            comprehensive_score += signal_adjustment
            
            logger.debug(f"板块 {sector_name} - 信号调整值: {signal_adjustment}, 调整后评分: {comprehensive_score:.2f}")
            
            # 限制范围
            comprehensive_score = max(0, min(100, comprehensive_score))
            
            logger.debug(f"板块 {sector_name} - 最终综合技术评分: {comprehensive_score:.2f}")
            
            return {
                'comprehensive_score': comprehensive_score,
                'rsi_score': rsi_score,
                'momentum_score': momentum_data['momentum_score'],
                'volatility_score': volatility_data['volatility_score'],
                'momentum_signal': momentum_data['momentum_signal'],
                'risk_level': volatility_data['risk_level'],
                'predicted_volatility': volatility_data['predicted_volatility']
            }
            
        except Exception as e:
            logger.error(f"综合技术指标计算异常 {sector_name}: {e}")
            return {
                'comprehensive_score': 50,
                'rsi_score': 50,
                'momentum_score': 50,
                'volatility_score': 50,
                'momentum_signal': 'neutral',
                'risk_level': 'medium',
                'predicted_volatility': 0.03
            }
    
    def _get_signal_adjustment(self, signal: str) -> float:
        """
        根据交易信号调整评分
        
        Parameters:
        -----------
        signal : str
            交易信号
            
        Returns:
        --------
        float
            评分调整值
        """
        signal_adjustments = {
            'strong_buy': 10,
            'buy': 5,
            'neutral': 0,
            'sell': -5,
            'strong_sell': -10
        }
        return signal_adjustments.get(signal, 0)
    
    def clear_cache(self):
        """清空缓存"""
        self._rsi_cache.clear()
        self._momentum_cache.clear()
        self._volatility_cache.clear()
        logger.info("技术指标缓存已清空")
    
    def update_historical_data(self, new_data: Dict):
        """
        更新历史数据并清空相关缓存
        
        Parameters:
        -----------
        new_data : dict
            新的历史数据
        """
        self.historical_data.update(new_data)
        self.clear_cache()
        logger.info(f"历史数据已更新，涉及{len(new_data)}个板块")