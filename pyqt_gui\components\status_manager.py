from PyQt6.QtCore import QTimer

class StatusManager:
    """管理应用状态和状态栏更新"""
    
    @staticmethod
    def update_status(self, message, status="idle", progress=None):
        """更新状态栏信息
        :param message: 状态消息
        :param status: 状态类型 (idle, loading, success, error)
        :param progress: 进度值 (0-100)，None表示不显示进度条
        """
        self.status_label.setText(message)
        self.status_indicator.set_status(status)
        
        if progress is not None:
            self.status_progress_bar.setValue(progress)
            self.status_progress_bar.setVisible(True)
        else:
            self.status_progress_bar.setVisible(False)
    
    @staticmethod
    def toggle_theme(self):
        """切换应用程序主题"""
        from pyqt_gui.style_manager import StyleManager
        StyleManager.toggle_theme()
        
        # 获取当前主题名称
        theme_name = StyleManager.current_theme
        theme_display_names = {
            'light': '亮色',
            'dark': '暗黑',
            'blue': '青蓝',
            'volcano': '火山'
        }
        display_name = theme_display_names.get(theme_name, theme_name)
        
        # 显示主题切换提示信息
        StatusManager.update_status(self, f"已切换到{display_name}主题", "success", 100)
        # 3秒后隐藏进度条
        QTimer.singleShot(3000, lambda: StatusManager.update_status(self, f"已切换到{display_name}主题", "success"))
    
    @staticmethod
    def on_init_progress(self, progress, message):
        """初始化进度更新回调"""
        self.loading_progress = progress
        StatusManager.update_status(self, message, "loading", progress)
        self.progress_dialog.set_progress(progress, message)
    
    @staticmethod
    def on_data_load_progress(self, progress, message):
        """数据加载进度更新回调"""
        StatusManager.update_status(self, message, "loading", progress)
        if hasattr(self, 'data_progress_dialog'):
            self.data_progress_dialog.set_progress(progress, message)
    
    @staticmethod
    def on_money_effect_progress(self, progress, message):
        """赚钱效应评分进度更新回调"""
        StatusManager.update_status(self, message, "loading", progress)
    
    @staticmethod
    def on_constituent_progress(self, progress, message):
        """成分股数据加载进度更新回调"""
        StatusManager.update_status(self, message, "loading", progress)
    
    @staticmethod
    def on_history_data_progress(self, progress, message):
        """历史数据加载进度更新回调"""
        StatusManager.update_status(self, message, "loading", progress)
    
    @staticmethod
    def on_stock_data_progress(self, progress, message):
        """股票数据加载进度更新回调"""
        StatusManager.update_status(self, message, "loading", progress)