# fast_csv_reader.py
import pandas as pd
import os
import logging
import time
import functools
import hashlib
from concurrent.futures import ThreadPoolExecutor
import numpy as np
from typing import Dict, Optional, List, Tuple, Union


# 全局缓存字典，用于存储最近读取的数据
_DATA_CACHE = {}
_CACHE_SIZE_LIMIT = 20  # 最多缓存20个股票的数据
_CACHE_LAST_USED = {}  # 记录缓存的最后使用时间


def _get_cache_key(file_path: str, days: Optional[int] = None) -> str:
    """生成缓存键"""
    if days is None:
        return f"{file_path}_all"
    return f"{file_path}_{days}"


def _update_cache(key: str, df: pd.DataFrame) -> None:
    """更新缓存"""
    global _DATA_CACHE, _CACHE_LAST_USED
    
    # 如果缓存已满，删除最久未使用的项
    if len(_DATA_CACHE) >= _CACHE_SIZE_LIMIT and key not in _DATA_CACHE:
        oldest_key = min(_CACHE_LAST_USED.items(), key=lambda x: x[1])[0]
        if oldest_key in _DATA_CACHE:
            del _DATA_CACHE[oldest_key]
            del _CACHE_LAST_USED[oldest_key]
    
    # 更新缓存
    _DATA_CACHE[key] = df
    _CACHE_LAST_USED[key] = time.time()


def _get_from_cache(key: str) -> Optional[pd.DataFrame]:
    """从缓存中获取数据"""
    global _DATA_CACHE, _CACHE_LAST_USED
    
    if key in _DATA_CACHE:
        _CACHE_LAST_USED[key] = time.time()  # 更新最后使用时间
        return _DATA_CACHE[key]
    return None


def try_install_packages():
    """尝试安装高性能数据处理包"""
    try:
        import pyarrow
        import pyarrow.csv
        import pyarrow.parquet
        return True
    except ImportError:
        try:
            import subprocess
            import sys
            logging.info("尝试安装pyarrow以提高读取速度...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyarrow"])
            import pyarrow
            import pyarrow.csv
            import pyarrow.parquet
            logging.info("成功安装pyarrow!")
            return True
        except:
            logging.warning("安装pyarrow失败，将使用pandas读取")
            return False
            

def has_arrow():
    """检查是否安装了pyarrow"""
    try:
        import pyarrow
        return True
    except ImportError:
        return False


def read_with_arrow(file_path: str, encoding: str = "utf-8") -> pd.DataFrame:
    """使用pyarrow高速读取CSV文件"""
    try:
        import pyarrow.csv as pa_csv
        import pyarrow as pa
        
        # 使用pyarrow的高性能读取器
        start_time = time.time()
        table = pa_csv.read_csv(file_path)
        df = table.to_pandas()
        end_time = time.time()
        
        logging.info(f"PyArrow读取完成，共{len(df)}行，耗时: {end_time - start_time:.2f}秒")
        return df
    except Exception as e:
        logging.error(f"PyArrow读取失败: {str(e)}")
        # 回退到pandas
        return pd.read_csv(file_path, encoding=encoding)


def convert_to_parquet(csv_path: str) -> str:
    """将CSV文件转换为Parquet格式以加速后续读取"""
    try:
        if not has_arrow():
            return csv_path
        
        import pyarrow as pa
        import pyarrow.csv as pa_csv
        import pyarrow.parquet as pq
        
        # 生成parquet文件路径
        parquet_path = csv_path.replace('.csv', '.parquet')
        
        # 如果parquet文件已存在且比csv文件新，直接返回
        if os.path.exists(parquet_path) and os.path.getmtime(parquet_path) >= os.path.getmtime(csv_path):
            logging.info(f"使用已存在的Parquet文件: {parquet_path}")
            return parquet_path
        
        # 转换CSV到Parquet
        logging.info(f"正在将CSV转换为Parquet: {csv_path}")
        start_time = time.time()
        table = pa_csv.read_csv(csv_path)
        pq.write_table(table, parquet_path)
        end_time = time.time()
        
        logging.info(f"CSV转换为Parquet完成，耗时: {end_time - start_time:.2f}秒")
        return parquet_path
    except Exception as e:
        logging.error(f"转换为Parquet失败: {str(e)}")
        return csv_path


def read_parquet(parquet_path: str) -> pd.DataFrame:
    """读取Parquet文件"""
    try:
        import pyarrow.parquet as pq
        
        start_time = time.time()
        table = pq.read_table(parquet_path)
        df = table.to_pandas()
        end_time = time.time()
        
        logging.info(f"Parquet读取完成，共{len(df)}行，耗时: {end_time - start_time:.2f}秒")
        return df
    except Exception as e:
        logging.error(f"Parquet读取失败: {str(e)}")
        # 如果parquet路径实际是CSV，尝试用pandas读取
        if parquet_path.endswith('.csv'):
            return pd.read_csv(parquet_path)
        return pd.DataFrame()


def read_csv_optimized(file_path: str, encoding: str = "utf-8") -> pd.DataFrame:
    """优化的CSV读取函数，根据环境选择最快的读取方式"""
    # 首先尝试使用pyarrow
    if has_arrow():
        # 尝试转换为parquet并读取
        try:
            parquet_path = convert_to_parquet(file_path)
            if parquet_path.endswith('.parquet'):
                return read_parquet(parquet_path)
            # 如果转换失败，使用pyarrow直接读取CSV
            return read_with_arrow(file_path, encoding)
        except Exception as e:
            logging.error(f"高性能读取失败: {str(e)}")
    
    # 如果pyarrow不可用或失败，回退到pandas
    return pd.read_csv(file_path, encoding=encoding)


def read_csv_in_chunks(file_path, chunk_size=10000, max_workers=4, encoding="utf-8"):
    """
    使用多线程分段读取大型CSV文件以提高性能
    
    Args:
        file_path: CSV文件路径
        chunk_size: 每个分段的行数
        max_workers: 最大工作线程数
        encoding: 文件编码，默认为UTF-8
        
    Returns:
        pd.DataFrame: 合并后的DataFrame
    """
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logging.error(f"文件不存在: {file_path}")
        return pd.DataFrame()
    
    start_time = time.time()
    
    # 获取文件大小
    file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
    logging.info(f"文件大小: {file_size:.2f} MB")
    
    # 对于小文件，使用优化的整体读取
    if file_size < 10:  # 10MB以下直接整体读取
        logging.info(f"文件较小，使用优化读取: {file_path}")
        return read_csv_optimized(file_path, encoding)
    
    # 尝试使用pyarrow高性能读取
    if has_arrow():
        try:
            parquet_path = convert_to_parquet(file_path)
            if parquet_path.endswith('.parquet'):
                return read_parquet(parquet_path)
            return read_with_arrow(file_path, encoding)
        except Exception as e:
            logging.warning(f"高性能读取失败，回退到分块读取: {str(e)}")
    
    # 尝试检测处理器类型
    try:
        import platform
        cpu_info = platform.processor().lower()
        is_3d_vcache = any(vcache_model in cpu_info for vcache_model in ['7950x3d', '7900x3d', '7800x3d', 'x3d'])
        
        # 针对3D V-Cache优化读取参数
        if is_3d_vcache:
            try:
                # 尝试使用内存映射加速读取
                df = pd.read_csv(file_path, encoding=encoding, memory_map=True, low_memory=False)
                end_time = time.time()
                logging.info(f"内存映射读取完成，耗时: {end_time - start_time:.2f}秒")
                return df
            except Exception as e:
                logging.warning(f"内存映射读取失败: {str(e)}")
    except Exception:
        pass
    
    # 根据CPU和文件大小优化参数
    import multiprocessing
    cores = multiprocessing.cpu_count()
    
    # 进一步优化分块大小和工作线程数
    try:
        import platform
        cpu_info = platform.processor().lower()
        is_3d_vcache = any(vcache_model in cpu_info for vcache_model in ['7950x3d', '7900x3d', '7800x3d', 'x3d'])
        
        if is_3d_vcache:
            # 为3D V-Cache优化
            max_workers = min(cores, 12)  # 最多12个线程
            chunk_size = min(50000, max(10000, int(file_size * 2000)))  # 根据文件大小动态调整块大小
            logging.info(f"针对3D V-Cache优化: {max_workers}线程, 块大小{chunk_size}")
        else:
            max_workers = min(cores, 8)
            chunk_size = min(30000, max(10000, int(file_size * 1000)))
    except Exception:
        # 默认配置
        max_workers = min(cores, 6)
        chunk_size = 20000
    
    # 使用pandas的内置分块读取功能
    logging.info(f"使用优化的分块读取，{max_workers}线程, 块大小{chunk_size}")
    
    try:
        chunks = []
        
        # 读取总行数
        total_rows = 0
        with open(file_path, 'r', encoding=encoding) as f:
            for _ in f:
                total_rows += 1
        
        total_rows -= 1  # 减去标题行
        logging.info(f"文件总行数: {total_rows}")
        
        # 划分读取区间
        ranges = []
        for start in range(0, total_rows, chunk_size):
            end = min(start + chunk_size, total_rows)
            ranges.append((start, end))
        
        # 定义读取函数
        def read_range(start, end):
            try:
                if start == 0:
                    # 包含标题行
                    df = pd.read_csv(file_path, nrows=end+1, encoding=encoding)
                else:
                    # 跳过标题行和之前的行，但需要包含标题
                    header = pd.read_csv(file_path, nrows=1, encoding=encoding).columns
                    df = pd.read_csv(file_path, skiprows=range(1, start+1), nrows=end-start, 
                                    names=header, encoding=encoding)
                return df
            except Exception as e:
                logging.error(f"读取区间[{start}:{end}]失败: {str(e)}")
                return None
        
        # 并行读取
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(read_range, start, end): (start, end) for start, end in ranges}
            for future in futures:
                result = future.result()
                if result is not None:
                    chunks.append((futures[future][0], result))
        
        # 按顺序合并结果
        chunks.sort(key=lambda x: x[0])  # 按起始行排序
        dfs = [chunk for _, chunk in chunks]
        
        if dfs:
            combined_df = pd.concat(dfs, ignore_index=True)
            end_time = time.time()
            logging.info(f"分块读取完成，共{len(combined_df)}行，耗时: {end_time - start_time:.2f}秒")
            return combined_df
        else:
            logging.error("所有分块读取失败")
            # 尝试使用基本方法
            return pd.read_csv(file_path, encoding=encoding)
            
    except Exception as e:
        logging.error(f"分块读取失败: {str(e)}")
        try:
            # 尝试直接读取
            return pd.read_csv(file_path, encoding=encoding)
        except Exception as e2:
            logging.error(f"直接读取也失败: {str(e2)}")
            return pd.DataFrame()


@functools.lru_cache(maxsize=10)  # 使用Python内置的LRU缓存
def get_local_history_data(stock_code, data_dir="data/historical", days=None):
    """
    从本地CSV文件快速读取股票历史数据，带缓存机制
    
    Args:
        stock_code: 股票代码
        data_dir: 数据目录
        days: 需要的天数，如果为None则返回全部数据
        
    Returns:
        pd.DataFrame: 历史数据DataFrame
    """
    file_path = os.path.join(data_dir, f"{stock_code}_historical_data.csv")
    
    if not os.path.exists(file_path):
        logging.warning(f"历史数据文件不存在: {file_path}")
        return pd.DataFrame()
    
    # 检查文件修改时间
    file_mtime = os.path.getmtime(file_path)
    
    # 生成缓存键
    cache_key = _get_cache_key(file_path, days)
    
    # 检查缓存是否有效
    cached_df = _get_from_cache(cache_key)
    if cached_df is not None:
        logging.info(f"从缓存读取{stock_code}历史数据，共{len(cached_df)}行")
        return cached_df
    
    try:
        logging.info(f"开始读取历史数据: {file_path}")
        
        # 检测CPU类型
        import platform
        cpu_info = platform.processor().lower()
        is_amd_cpu = 'amd' in cpu_info
        is_3d_vcache = any(vcache_model in cpu_info for vcache_model in ['7950x3d', '7900x3d', '7800x3d', 'x3d'])
        
        # 获取文件大小
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        logging.info(f"文件大小: {file_size:.2f} MB")
        
        # 根据文件大小和CPU类型选择读取策略
        if file_size < 5:  # 小于5MB直接读取
            logging.info(f"文件较小，直接读取")
            df = pd.read_csv(file_path, encoding="utf-8")
        elif has_arrow():
            # 使用高性能读取
            try:
                parquet_path = convert_to_parquet(file_path)
                if parquet_path.endswith('.parquet'):
                    df = read_parquet(parquet_path)
                else:
                    df = read_with_arrow(file_path)
            except Exception as e:
                logging.error(f"高性能读取失败: {str(e)}")
                df = read_csv_in_chunks(file_path, encoding="utf-8")
        elif is_3d_vcache:
            # 针对3D V-Cache的优化读取
            try:
                # 首先尝试内存映射
                logging.info("使用内存映射读取")
                df = pd.read_csv(file_path, encoding="utf-8", memory_map=True, low_memory=False)
            except Exception as e:
                logging.warning(f"内存映射读取失败: {str(e)}，使用分块读取")
                df = read_csv_in_chunks(file_path, encoding="utf-8")
        else:
            # 其他情况使用分块读取
            df = read_csv_in_chunks(file_path, encoding="utf-8")
        
        # 如果指定了天数，只返回最近的N天数据
        if days is not None and not df.empty:
            df = df.tail(days)
        
        # 更新缓存
        _update_cache(cache_key, df)
        
        logging.info(f"成功读取{stock_code}历史数据，共{len(df)}行")
        return df
    except Exception as e:
        logging.error(f"读取历史数据失败: {str(e)}")
        try:
            # 最基本的读取尝试
            df = pd.read_csv(file_path, encoding="utf-8")
            if days is not None and not df.empty:
                df = df.tail(days)
            return df
        except Exception as e2:
            logging.error(f"基本读取也失败: {str(e2)}")
            return pd.DataFrame()


# 初始化时尝试安装高性能包
try_install_packages()