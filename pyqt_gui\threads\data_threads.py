import time
import pandas as pd
from PyQt6.QtCore import QThread, pyqtSignal
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from datetime import datetime, timedelta
import multiprocessing
from pyqt_gui.utils import ParallelDataFetcher, StockScoringTask
from utils.logging_config import get_logger

class InitThread(QThread):
    """初始化数据模块线程"""
    finished_signal = pyqtSignal()
    progress_signal = pyqtSignal(int, str)

    def __init__(self, parent):
        super().__init__()
        self.parent = parent

    def run(self):
        from fundamental_analysis.sector_rank import SectorRankAnalyzer
        from fundamental_analysis.concept_rank import ConceptRankAnalyzer
        from fundamental_analysis.stock_selection import StockSelector
        from technical_analysis.stock_analyzer import StockAnalyzer
        from capital_flow import MoneyEffectScore

        # 初始化数据模块，并报告进度
        self.progress_signal.emit(10, "正在初始化行业板块分析器...")
        self.parent.sector_analyzer = SectorRankAnalyzer()

        self.progress_signal.emit(30, "正在初始化概念板块分析器...")
        self.parent.concept_analyzer = ConceptRankAnalyzer()

        self.progress_signal.emit(50, "正在初始化选股模块...")
        self.parent.stock_selector = StockSelector()

        self.progress_signal.emit(70, "正在初始化技术分析模块...")
        self.parent.stock_analyzer = StockAnalyzer()

        self.progress_signal.emit(90, "正在初始化赚钱效应评分模块...")
        self.parent.money_effect = MoneyEffectScore()

        self.progress_signal.emit(100, "基础数据初始化完成")
        self.finished_signal.emit()


class LoadDataThread(QThread):
    """加载板块排名数据线程"""
    finished_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, str)

    def __init__(self, parent):
        super().__init__()
        self.parent = parent

    def run(self):
        try:
            # 获取行业板块排名
            self.progress_signal.emit(10, "正在获取行业板块排名...")
            industry_df = self.parent.stock_selector.get_top_sectors('industry', 10)

            # 获取概念板块排名
            self.progress_signal.emit(50, "正在获取概念板块排名...")
            concept_df = self.parent.stock_selector.get_top_sectors('concept', 10)

            # 完成数据加载
            self.progress_signal.emit(90, "数据加载完成，正在更新界面...")

            # 返回结果
            self.finished_signal.emit({
                'industry_df': industry_df,
                'concept_df': concept_df
            })
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"数据加载失败: {str(e)}")
            self.error_signal.emit(str(e))


class MoneyEffectThread(QThread):
    """赚钱效应评分线程"""
    finished_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, str)

    def __init__(self, parent):
        super().__init__()
        self.parent = parent

    def run(self):
        try:
            # 获取赚钱效应评分报告，并报告进度
            self.progress_signal.emit(20, "正在获取市场指标数据...")
            time.sleep(0.5)  # 模拟耗时操作

            self.progress_signal.emit(50, "正在计算赚钱效应评分...")
            time.sleep(0.5)  # 模拟耗时操作

            self.progress_signal.emit(80, "正在生成评分报告...")
            report = self.parent.money_effect.get_money_effect_report()

            self.progress_signal.emit(100, "赚钱效应评分获取完成")
            self.finished_signal.emit(report)
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"更新赚钱效应评分失败: {str(e)}")
            self.error_signal.emit(str(e))


class ConstituentThread(QThread):
    """获取板块成分股线程"""
    finished_signal = pyqtSignal(pd.DataFrame)
    error_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, str)

    def __init__(self, parent, sector_name, sector_type):
        super().__init__()
        self.parent = parent
        self.sector_name = sector_name
        self.sector_type = sector_type

    def run(self):
        try:
            # 获取成分股数据，并报告进度
            self.progress_signal.emit(10, f"正在查询{self.sector_name}成分股列表...")

            # 获取成分股数据
            if self.sector_type == "industry":
                constituents = self.parent.stock_selector.get_constituents(self.sector_name, 'industry')
            else:
                constituents = self.parent.stock_selector.get_constituents(self.sector_name, 'concept')

            if constituents.empty:
                self.progress_signal.emit(100, f"未找到{self.sector_name}的成分股数据")
                self.finished_signal.emit(pd.DataFrame())
                return

            # 使用并行处理进行优化
            from stock_scoring.models import StockScoringModelV4_1
            
            # 初始化评分模型（共享实例）
            self.progress_signal.emit(15, f"正在初始化股票评分模型...")
            model = StockScoringModelV4_1(market_index_symbol="sh000001")

            # 设置日期范围
            end_date = datetime.now().strftime("%Y%m%d")
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y%m%d")

            # 第一阶段：并行获取股票历史数据
            data_fetcher = ParallelDataFetcher(progress_callback=self.progress_signal.emit)
            stock_data_dict = data_fetcher.batch_fetch_stock_data(constituents, start_date, end_date)
            
            if not stock_data_dict:
                self.progress_signal.emit(100, f"未能获取{self.sector_name}的股票数据")
                self.finished_signal.emit(pd.DataFrame())
                return

            # 第二阶段：并行计算股票评分
            self.progress_signal.emit(35, f"开始并行计算{len(stock_data_dict)}只股票的评分...")
            
            # 获取最优线程数
            cpu_count = multiprocessing.cpu_count()
            max_workers = min(cpu_count, len(stock_data_dict), 8)  # 限制最大线程数
            
            # 创建评分任务
            scoring_tasks = []
            for _, row in constituents.iterrows():
                stock_code = str(row['代码']).split('.')[0]
                if stock_code in stock_data_dict:
                    task = StockScoringTask(
                        stock_code=stock_code,
                        stock_name=row['名称'],
                        stock_data=stock_data_dict[stock_code],
                        scoring_model=model,
                        start_date=start_date,
                        end_date=end_date
                    )
                    scoring_tasks.append(task)
            
            # 并行执行评分任务
            score_results = {}
            completed_count = 0
            total_tasks = len(scoring_tasks)
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有评分任务
                future_to_task = {
                    executor.submit(task.get_final_score, parallel_factors=True): task
                    for task in scoring_tasks
                }
                
                # 处理完成的任务
                for future in as_completed(future_to_task):
                    task = future_to_task[future]
                    try:
                        result = future.result()
                        score_results[result['stock_code']] = result['total_score']
                        completed_count += 1
                        
                        # 更新进度
                        progress = 35 + int(55 * completed_count / total_tasks)
                        self.progress_signal.emit(progress, 
                                                f"评分进度: {completed_count}/{total_tasks} - {result['stock_name']}({result['stock_code']})")
                        
                        # 记录错误信息
                        if result['errors']:
                            logger = get_logger(__name__)
                            for error in result['errors']:
                                logger.warning(f"股票 {result['stock_code']} 评分警告: {error}")
                                
                    except Exception as e:
                        # 评分失败时给予默认分数
                        score_results[task.stock_code] = 50.0
                        completed_count += 1
                        logger = get_logger(__name__)
                        logger.error(f"股票 {task.stock_code} 评分任务失败: {str(e)}")
            
            # 第三阶段：整理结果
            self.progress_signal.emit(90, f"正在处理{self.sector_name}成分股评分结果...")
            
            # 将评分添加到DataFrame中
            constituents['score'] = constituents['代码'].apply(
                lambda x: score_results.get(str(x).split('.')[0], 50.0)
            )

            # 按评分降序排列
            constituents = constituents.sort_values(by='score', ascending=False)

            self.progress_signal.emit(100, f"{self.sector_name}成分股数据评分计算完成")
            self.finished_signal.emit(constituents)
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"获取成分股失败: {str(e)}")
            self.error_signal.emit(str(e))


class HistoryDataThread(QThread):
    """获取股票历史数据线程"""
    progress_signal = pyqtSignal(int, str)
    finished_signal = pyqtSignal()

    def __init__(self, parent, stock_codes):
        super().__init__()
        self.parent = parent
        self.stock_codes = stock_codes
        self.is_running = True

    def run(self):
        try:
            import platform
            cpu_info = platform.processor().lower()
            is_amd_cpu = 'amd' in cpu_info

            # 特别检测Ryzen 9 7950X3D和其他3D V-Cache型号
            is_3d_vcache = any(vcache_model in cpu_info for vcache_model in ['7950x3d', '7900x3d', '7800x3d', 'x3d'])

            # 报告开始获取历史数据，包含CPU信息
            if is_3d_vcache:
                self.progress_signal.emit(10, f"检测到AMD 3D V-Cache CPU ({cpu_info})，启用特别优化模式...")
            elif is_amd_cpu:
                self.progress_signal.emit(10, f"检测到AMD CPU ({cpu_info})，开始获取{len(self.stock_codes)}只股票的历史数据...")
            else:
                self.progress_signal.emit(10, f"开始获取{len(self.stock_codes)}只股票的历史数据...")

            # 确保历史数据目录存在
            import os
            data_dir = "data/historical"
            if not os.path.exists(data_dir):
                try:
                    os.makedirs(data_dir)
                    self.progress_signal.emit(15, f"创建历史数据目录: {os.path.abspath(data_dir)}")
                except Exception as e:
                    self.progress_signal.emit(100, f"创建历史数据目录失败: {str(e)}")
                    self.finished_signal.emit()
                    return

            # 验证目录写入权限
            try:
                test_file = os.path.join(data_dir, "test_write.txt")
                with open(test_file, "w") as f:
                    f.write("test")
                if os.path.exists(test_file):
                    os.remove(test_file)
                    self.progress_signal.emit(20, f"历史数据目录写入权限验证成功")
            except Exception as e:
                self.progress_signal.emit(100, f"历史数据目录写入权限验证失败: {str(e)}")
                self.finished_signal.emit()
                return

            # 实际调用batch_get_stock_history获取历史数据
            from technical_analysis.historical_data import batch_get_stock_history

            # 针对不同类型的CPU使用不同的优化策略
            if is_3d_vcache:
                # 对于7950X3D等3D V-Cache处理器，使用特别优化
                self.progress_signal.emit(30, f"针对AMD 3D V-Cache CPU优化，采用串行处理模式")
                result = batch_get_stock_history(self.stock_codes)  # 函数内部会检测3D V-Cache并使用优化
            elif is_amd_cpu:
                # 对于普通AMD CPU，明确指定较少的工作线程数
                import multiprocessing
                cpu_count = multiprocessing.cpu_count()
                # 使用更保守的线程数
                max_workers = min(cpu_count // 4, 3)  # 最多3个线程，且不超过CPU核心数的四分之一
                self.progress_signal.emit(30, f"针对AMD CPU优化，使用{max_workers}个工作线程")
                result = batch_get_stock_history(self.stock_codes, max_workers=max_workers)
            else:
                result = batch_get_stock_history(self.stock_codes)

            # 验证结果文件是否实际存在
            self.progress_signal.emit(90, f"验证历史数据文件...")
            file_count = 0
            for code in result['success']:
                file_path = os.path.join(data_dir, f"{code}_historical_data.csv")
                if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                    file_count += 1

            # 报告完成情况
            success_count = len(result['success'])
            failed_count = len(result['failed'])
            total = success_count + failed_count

            if file_count < success_count:
                self.progress_signal.emit(100, f"历史数据获取完成，但部分文件未正确保存。成功: {file_count}/{success_count}，失败: {failed_count}")
            elif failed_count > 0:
                self.progress_signal.emit(100, f"历史数据获取完成，成功: {success_count}，失败: {failed_count}")
            else:
                self.progress_signal.emit(100, f"历史数据获取完成，共{success_count}只股票")

            self.finished_signal.emit()
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"获取历史数据失败: {str(e)}")
            self.progress_signal.emit(100, f"获取历史数据失败: {str(e)}")
            self.finished_signal.emit()


class StockDataThread(QThread):
    """获取股票数据线程"""
    finished_signal = pyqtSignal(str, str, pd.DataFrame)
    error_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int, str)

    def __init__(self, parent=None, stock_code="", stock_name=""):
        super().__init__(parent)
        self.stock_code = stock_code
        self.stock_name = stock_name

    def run(self):
        try:
            import platform
            cpu_info = platform.processor().lower()
            is_3d_vcache = any(vcache_model in cpu_info for vcache_model in ['7950x3d', '7900x3d', '7800x3d', 'x3d'])

            # 发送进度信号
            self.progress_signal.emit(10, f"开始加载 {self.stock_name}({self.stock_code}) 历史数据...")

            # 引入优化的历史数据读取器
            from technical_analysis.fast_csv_reader import get_local_history_data
            from technical_analysis.stock_analyzer import StockAnalyzer

            # 使用优化的读取器获取数据
            self.progress_signal.emit(30, f"读取 {self.stock_name} 历史数据...")

            # 如果是3D V-Cache CPU，尝试预加载策略
            if is_3d_vcache:
                # 使用文件存在性检查来避免不必要的尝试
                import os
                file_path = os.path.join("data/historical", f"{self.stock_code}_historical_data.csv")
                if os.path.exists(file_path):
                    # 使用优化的读取器
                    self.progress_signal.emit(40, f"使用高性能读取器加载数据...")
                    df = get_local_history_data(self.stock_code)
                    self.progress_signal.emit(80, f"历史数据读取完成，共{len(df)}行")
                else:
                    # 文件不存在，使用标准分析器
                    self.progress_signal.emit(40, f"历史数据文件不存在，尝试从API获取...")
                    analyzer = StockAnalyzer()
                    df = analyzer.get_history_data(self.stock_code)
                    self.progress_signal.emit(80, f"从API获取历史数据完成，共{len(df)}行")
            else:
                # 对于其他CPU，直接使用标准流程
                analyzer = StockAnalyzer()
                df = analyzer.get_history_data(self.stock_code)
                self.progress_signal.emit(80, f"历史数据读取完成，共{len(df)}行")

            # 检查数据是否为空
            if df.empty:
                self.error_signal.emit(f"无法获取 {self.stock_name} 的历史数据")
                return

            # 发送完成信号，返回数据
            self.progress_signal.emit(100, f"数据加载完成")
            self.finished_signal.emit(self.stock_code, self.stock_name, df)

        except Exception as e:
            import traceback
            logger = get_logger(__name__)
            logger.error(f"加载股票数据异常: {str(e)}\n{traceback.format_exc()}")
            self.error_signal.emit(f"加载 {self.stock_name} 数据失败: {str(e)}")


class AnalyzeStockThread(QThread):
    """分析股票线程"""
    finished_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)

    def __init__(self, parent, code):
        super().__init__()
        self.parent = parent
        self.code = code

    def run(self):
        try:
            # 获取实时数据
            realtime_data = self.parent.stock_analyzer.get_realtime_data(self.code)

            # 获取历史数据
            df = self.parent.stock_analyzer.get_history_data(self.code, days=60)

            # 返回结果
            self.finished_signal.emit({
                'realtime_data': realtime_data,
                'history_data': df
            })
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"分析失败: {str(e)}")
            self.error_signal.emit(str(e))


class ModelLoadThread(QThread):
    """基础模块加载线程"""
    finished_signal = pyqtSignal()
    progress_signal = pyqtSignal(str)

    def __init__(self, parent):
        super().__init__()
        self.parent = parent

    def run(self):
        try:
            self.progress_signal.emit("正在初始化基础分析模块...")
            
            # 确保股票选择器使用基础分析模式
            if hasattr(self.parent, 'stock_selector') and self.parent.stock_selector:
                self.progress_signal.emit("正在配置股票选择器...")
                # 配置基础分析模式
            pass

            self.progress_signal.emit("基础分析模块加载完成")
            self.finished_signal.emit()

        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"模块加载失败: {str(e)}")
            self.progress_signal.emit(f"模块加载失败: {str(e)}")
            self.finished_signal.emit()


# 数据加载完成