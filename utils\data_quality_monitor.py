#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量监控模块

提供数据质量实时监控、报告生成和告警功能
支持多种数据源的质量监控和统计分析
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
import threading
import time
from collections import defaultdict, deque

try:
    from utils.logging_config import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)


class DataQualityMetrics:
    """
    数据质量指标计算器
    """
    
    @staticmethod
    def calculate_completeness(df: pd.DataFrame) -> float:
        """
        计算数据完整性（非空值比例）
        
        Args:
            df: 要分析的DataFrame
            
        Returns:
            完整性评分 (0-1)
        """
        if df is None or df.empty:
            return 0.0
        
        total_cells = df.size
        non_null_cells = df.count().sum()
        return non_null_cells / total_cells if total_cells > 0 else 0.0
    
    @staticmethod
    def calculate_validity(df: pd.DataFrame, numeric_columns: List[str] = None) -> float:
        """
        计算数据有效性（数值列的有效数值比例）
        
        Args:
            df: 要分析的DataFrame
            numeric_columns: 数值列名列表
            
        Returns:
            有效性评分 (0-1)
        """
        if df is None or df.empty:
            return 0.0
        
        if numeric_columns is None:
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        
        if not numeric_columns:
            return 1.0  # 没有数值列时认为完全有效
        
        total_numeric_cells = 0
        valid_numeric_cells = 0
        
        for col in numeric_columns:
            if col in df.columns:
                col_data = df[col]
                total_numeric_cells += len(col_data)
                # 检查是否为有效数值（非NaN、非无穷大）
                valid_numeric_cells += col_data.apply(
                    lambda x: pd.notna(x) and np.isfinite(x) if pd.api.types.is_numeric_dtype(type(x)) else False
                ).sum()
        
        return valid_numeric_cells / total_numeric_cells if total_numeric_cells > 0 else 1.0
    
    @staticmethod
    def calculate_consistency(df: pd.DataFrame) -> float:
        """
        计算数据一致性（基于数据类型一致性）
        
        Args:
            df: 要分析的DataFrame
            
        Returns:
            一致性评分 (0-1)
        """
        if df is None or df.empty:
            return 0.0
        
        consistency_scores = []
        
        for col in df.columns:
            col_data = df[col].dropna()
            if len(col_data) == 0:
                continue
            
            # 检查数据类型一致性
            if pd.api.types.is_numeric_dtype(col_data):
                # 数值列：检查是否有异常值
                q1 = col_data.quantile(0.25)
                q3 = col_data.quantile(0.75)
                iqr = q3 - q1
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr
                
                outliers = col_data[(col_data < lower_bound) | (col_data > upper_bound)]
                consistency_score = 1 - (len(outliers) / len(col_data))
            else:
                # 非数值列：检查格式一致性
                consistency_score = 1.0  # 简化处理
            
            consistency_scores.append(consistency_score)
        
        return np.mean(consistency_scores) if consistency_scores else 0.0
    
    @staticmethod
    def calculate_timeliness(timestamp: datetime, max_age_minutes: int = 60) -> float:
        """
        计算数据时效性
        
        Args:
            timestamp: 数据时间戳
            max_age_minutes: 最大允许年龄（分钟）
            
        Returns:
            时效性评分 (0-1)
        """
        if timestamp is None:
            return 0.0
        
        age_minutes = (datetime.now() - timestamp).total_seconds() / 60
        if age_minutes <= 0:
            return 1.0
        elif age_minutes >= max_age_minutes:
            return 0.0
        else:
            return 1 - (age_minutes / max_age_minutes)
    
    @staticmethod
    def calculate_overall_quality(completeness: float, validity: float, 
                                consistency: float, timeliness: float,
                                weights: Dict[str, float] = None) -> float:
        """
        计算综合数据质量评分
        
        Args:
            completeness: 完整性评分
            validity: 有效性评分
            consistency: 一致性评分
            timeliness: 时效性评分
            weights: 各指标权重
            
        Returns:
            综合质量评分 (0-1)
        """
        if weights is None:
            weights = {
                'completeness': 0.3,
                'validity': 0.3,
                'consistency': 0.2,
                'timeliness': 0.2
            }
        
        overall_score = (
            completeness * weights.get('completeness', 0.3) +
            validity * weights.get('validity', 0.3) +
            consistency * weights.get('consistency', 0.2) +
            timeliness * weights.get('timeliness', 0.2)
        )
        
        return max(0.0, min(1.0, overall_score))


class DataQualityMonitor:
    """
    数据质量监控器
    
    提供实时数据质量监控、历史记录和告警功能
    """
    
    def __init__(self, max_history_size: int = 1000):
        """
        初始化数据质量监控器
        
        Args:
            max_history_size: 最大历史记录数量
        """
        self.max_history_size = max_history_size
        self.quality_history = deque(maxlen=max_history_size)
        self.data_source_stats = defaultdict(lambda: {
            'total_records': 0,
            'quality_scores': deque(maxlen=100),
            'last_update': None,
            'alert_count': 0
        })
        
        self.alert_thresholds = {
            'completeness': 0.8,
            'validity': 0.9,
            'consistency': 0.7,
            'timeliness': 0.8,
            'overall': 0.8
        }
        
        self.alert_callbacks = []
        self.logger = get_logger(__name__)
        self._lock = threading.Lock()
    
    def add_alert_callback(self, callback):
        """
        添加告警回调函数
        
        Args:
            callback: 告警回调函数，接收 (alert_type, message, data) 参数
        """
        self.alert_callbacks.append(callback)
    
    def set_alert_thresholds(self, thresholds: Dict[str, float]):
        """
        设置告警阈值
        
        Args:
            thresholds: 告警阈值字典
        """
        self.alert_thresholds.update(thresholds)
        self.logger.info(f"告警阈值已更新: {self.alert_thresholds}")
    
    def monitor_data_quality(self, df: pd.DataFrame, data_source: str, 
                           timestamp: datetime = None,
                           numeric_columns: List[str] = None) -> Dict[str, Any]:
        """
        监控数据质量
        
        Args:
            df: 要监控的DataFrame
            data_source: 数据源标识
            timestamp: 数据时间戳
            numeric_columns: 数值列名列表
            
        Returns:
            质量监控结果字典
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        try:
            # 计算质量指标
            completeness = DataQualityMetrics.calculate_completeness(df)
            validity = DataQualityMetrics.calculate_validity(df, numeric_columns)
            consistency = DataQualityMetrics.calculate_consistency(df)
            timeliness = DataQualityMetrics.calculate_timeliness(timestamp)
            overall_quality = DataQualityMetrics.calculate_overall_quality(
                completeness, validity, consistency, timeliness
            )
            
            quality_result = {
                'timestamp': timestamp,
                'data_source': data_source,
                'record_count': len(df) if df is not None else 0,
                'completeness': completeness,
                'validity': validity,
                'consistency': consistency,
                'timeliness': timeliness,
                'overall_quality': overall_quality,
                'column_count': len(df.columns) if df is not None else 0
            }
            
            # 更新历史记录
            with self._lock:
                self.quality_history.append(quality_result)
                
                # 更新数据源统计
                source_stats = self.data_source_stats[data_source]
                source_stats['total_records'] += quality_result['record_count']
                source_stats['quality_scores'].append(overall_quality)
                source_stats['last_update'] = timestamp
            
            # 检查告警条件
            self._check_alerts(quality_result)
            
            self.logger.debug(f"数据质量监控完成 - {data_source}: 综合评分 {overall_quality:.3f}")
            return quality_result
            
        except Exception as e:
            self.logger.error(f"数据质量监控失败 - {data_source}: {str(e)}")
            return {
                'timestamp': timestamp,
                'data_source': data_source,
                'error': str(e),
                'overall_quality': 0.0
            }
    
    def _check_alerts(self, quality_result: Dict[str, Any]):
        """
        检查告警条件
        
        Args:
            quality_result: 质量监控结果
        """
        alerts = []
        
        for metric, threshold in self.alert_thresholds.items():
            if metric in quality_result:
                value = quality_result[metric]
                if value < threshold:
                    alert_message = f"数据质量告警: {quality_result['data_source']} - {metric} = {value:.3f} < {threshold}"
                    alerts.append({
                        'type': 'quality_threshold',
                        'metric': metric,
                        'value': value,
                        'threshold': threshold,
                        'message': alert_message
                    })
        
        # 触发告警回调
        for alert in alerts:
            self.data_source_stats[quality_result['data_source']]['alert_count'] += 1
            self.logger.warning(alert['message'])
            
            for callback in self.alert_callbacks:
                try:
                    callback(alert['type'], alert['message'], quality_result)
                except Exception as e:
                    self.logger.error(f"告警回调执行失败: {str(e)}")
    
    def get_quality_report(self, data_source: str = None, 
                          hours: int = 24) -> Dict[str, Any]:
        """
        生成数据质量报告
        
        Args:
            data_source: 数据源过滤（None表示所有数据源）
            hours: 报告时间范围（小时）
            
        Returns:
            质量报告字典
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # 过滤历史记录
        filtered_history = [
            record for record in self.quality_history
            if record['timestamp'] >= cutoff_time and
            (data_source is None or record['data_source'] == data_source)
        ]
        
        if not filtered_history:
            return {
                'data_source': data_source,
                'time_range_hours': hours,
                'total_records': 0,
                'message': '指定时间范围内无数据记录'
            }
        
        # 计算统计信息
        total_records = sum(record['record_count'] for record in filtered_history)
        quality_scores = [record['overall_quality'] for record in filtered_history if 'overall_quality' in record]
        
        report = {
            'data_source': data_source or 'all',
            'time_range_hours': hours,
            'total_monitoring_points': len(filtered_history),
            'total_records': total_records,
            'average_quality_score': np.mean(quality_scores) if quality_scores else 0.0,
            'min_quality_score': np.min(quality_scores) if quality_scores else 0.0,
            'max_quality_score': np.max(quality_scores) if quality_scores else 0.0,
            'quality_trend': self._calculate_quality_trend(filtered_history),
            'metric_averages': self._calculate_metric_averages(filtered_history)
        }
        
        # 添加数据源特定统计
        if data_source and data_source in self.data_source_stats:
            source_stats = self.data_source_stats[data_source]
            report['source_statistics'] = {
                'total_records_processed': source_stats['total_records'],
                'last_update': source_stats['last_update'],
                'alert_count': source_stats['alert_count'],
                'recent_quality_average': np.mean(list(source_stats['quality_scores'])) if source_stats['quality_scores'] else 0.0
            }
        
        return report
    
    def _calculate_quality_trend(self, history: List[Dict[str, Any]]) -> str:
        """
        计算质量趋势
        
        Args:
            history: 历史记录列表
            
        Returns:
            趋势描述字符串
        """
        if len(history) < 2:
            return 'insufficient_data'
        
        # 取最近的质量评分
        recent_scores = [record['overall_quality'] for record in history[-10:] if 'overall_quality' in record]
        
        if len(recent_scores) < 2:
            return 'insufficient_data'
        
        # 简单的线性趋势计算
        x = np.arange(len(recent_scores))
        slope = np.polyfit(x, recent_scores, 1)[0]
        
        if slope > 0.01:
            return 'improving'
        elif slope < -0.01:
            return 'declining'
        else:
            return 'stable'
    
    def _calculate_metric_averages(self, history: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        计算各指标的平均值
        
        Args:
            history: 历史记录列表
            
        Returns:
            指标平均值字典
        """
        metrics = ['completeness', 'validity', 'consistency', 'timeliness']
        averages = {}
        
        for metric in metrics:
            values = [record[metric] for record in history if metric in record]
            averages[metric] = np.mean(values) if values else 0.0
        
        return averages
    
    def get_data_source_summary(self) -> Dict[str, Any]:
        """
        获取所有数据源的摘要信息
        
        Returns:
            数据源摘要字典
        """
        summary = {
            'total_data_sources': len(self.data_source_stats),
            'total_monitoring_points': len(self.quality_history),
            'data_sources': {}
        }
        
        for source, stats in self.data_source_stats.items():
            summary['data_sources'][source] = {
                'total_records': stats['total_records'],
                'last_update': stats['last_update'],
                'alert_count': stats['alert_count'],
                'average_quality': np.mean(list(stats['quality_scores'])) if stats['quality_scores'] else 0.0,
                'monitoring_points': len(stats['quality_scores'])
            }
        
        return summary
    
    def clear_history(self, data_source: str = None):
        """
        清空历史记录
        
        Args:
            data_source: 要清空的数据源（None表示清空所有）
        """
        with self._lock:
            if data_source is None:
                self.quality_history.clear()
                self.data_source_stats.clear()
                self.logger.info("所有数据质量历史记录已清空")
            else:
                # 清空特定数据源的记录
                self.quality_history = deque(
                    [record for record in self.quality_history if record['data_source'] != data_source],
                    maxlen=self.max_history_size
                )
                if data_source in self.data_source_stats:
                    del self.data_source_stats[data_source]
                self.logger.info(f"数据源 {data_source} 的质量历史记录已清空")
    
    def export_report(self, filepath: str, data_source: str = None, hours: int = 24):
        """
        导出质量报告到文件
        
        Args:
            filepath: 导出文件路径
            data_source: 数据源过滤
            hours: 报告时间范围
        """
        try:
            report = self.get_quality_report(data_source, hours)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"数据质量报告已导出到: {filepath}")
            
        except Exception as e:
            self.logger.error(f"导出数据质量报告失败: {str(e)}")
            raise


# 全局数据质量监控器实例
_global_monitor = None


def get_global_monitor() -> DataQualityMonitor:
    """
    获取全局数据质量监控器实例
    
    Returns:
        全局监控器实例
    """
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = DataQualityMonitor()
    return _global_monitor


def monitor_dataframe_quality(df: pd.DataFrame, data_source: str, 
                             timestamp: datetime = None,
                             numeric_columns: List[str] = None) -> Dict[str, Any]:
    """
    便捷函数：监控DataFrame的数据质量
    
    Args:
        df: 要监控的DataFrame
        data_source: 数据源标识
        timestamp: 数据时间戳
        numeric_columns: 数值列名列表
        
    Returns:
        质量监控结果
    """
    monitor = get_global_monitor()
    return monitor.monitor_data_quality(df, data_source, timestamp, numeric_columns)


def get_quality_summary() -> Dict[str, Any]:
    """
    便捷函数：获取数据质量摘要
    
    Returns:
        质量摘要信息
    """
    monitor = get_global_monitor()
    return monitor.get_data_source_summary()