#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模块预加载器
用于预先加载和缓存常用的库和组件
"""

import os
import sys
import threading
import importlib
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Callable
from utils.logging_config import get_logger

# 配置日志
logger = get_logger(__name__)

class ModulePreloader:
    """模块预加载器，用于预先加载和初始化常用模块"""
    
    def __init__(self, max_workers: int = 4):
        """
        初始化预加载器
        
        Args:
            max_workers: 最大工作线程数
        """
        self.max_workers = max_workers
        self.loaded_modules = {}
        self.executor = None
        self.futures = []
        self.load_times = {}
    
    def start(self):
        """启动预加载器"""
        logger.info("启动模块预加载器")
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
    def shutdown(self, wait: bool = True):
        """关闭预加载器"""
        if self.executor:
            logger.info("关闭模块预加载器")
            self.executor.shutdown(wait=wait)
            self.executor = None
    
    def load_module(self, module_name: str, alias: Optional[str] = None, 
                  callback: Optional[Callable] = None) -> None:
        """
        加载单个模块
        
        Args:
            module_name: 模块名
            alias: 模块别名，用于引用加载的模块
            callback: 模块加载后的回调函数
        """
        if not self.executor:
            self.start()
            
        alias = alias or module_name.split('.')[-1]
        
        def _load_module():
            start_time = time.time()
            try:
                logger.info(f"开始加载模块: {module_name}")
                module = importlib.import_module(module_name)
                self.loaded_modules[alias] = module
                
                if callback and callable(callback):
                    callback(module)
                    
                elapsed = time.time() - start_time
                self.load_times[module_name] = elapsed
                logger.info(f"模块 {module_name} 加载完成，耗时 {elapsed:.2f} 秒")
                return module
            except Exception as e:
                logger.error(f"加载模块 {module_name} 失败: {str(e)}")
                return None
        
        future = self.executor.submit(_load_module)
        self.futures.append(future)
        return future
    
    def preload_common_modules(self):
        """预加载常用模块"""
        # 基础数据处理库
        self.load_module('pandas', 'pd')
        self.load_module('numpy', 'np')
        
        # PyQt相关
        self.load_module('PyQt6.QtWidgets')
        self.load_module('PyQt6.QtCore')
        self.load_module('PyQt6.QtGui')
        
        # 加载数据分析相关模块
        self.load_module('matplotlib.pyplot', 'plt')
        
        # 项目自定义模块
        self.load_module('pyqt_gui.style_manager')
        self.load_module('cache.cache_manager')
        
        logger.info("已提交所有常用模块的加载任务")
        
    def preload_financial_apis(self):
        """预加载金融API相关模块"""
        # 加载金融数据API (耗时较长的部分)
        self.load_module('akshare', 'ak')
        
        # 项目自定义金融模块
        self.load_module('fundamental_analysis.sector_rank')
        self.load_module('fundamental_analysis.concept_rank')
        
        # 基础分析模块已加载
        logger.info("基础分析模块已加载，跳过其他模块预加载")
        
        logger.info("已提交金融API模块的加载任务")
    
    def wait_for_modules(self, timeout: Optional[float] = None) -> Dict[str, Any]:
        """
        等待模块加载完成
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            Dict: 已加载的模块字典
        """
        try:
            for future in as_completed(self.futures, timeout=timeout):
                # 等待但不做任何处理，异常会在load_module中被捕获
                result = future.result()
        except Exception as e:
            logger.warning(f"等待模块加载超时或出错: {e}")
        
        return self.loaded_modules
    
    def get_module(self, alias: str) -> Any:
        """
        获取已加载的模块
        
        Args:
            alias: 模块别名
            
        Returns:
            已加载的模块对象
        """
        return self.loaded_modules.get(alias)
    
    def get_load_times(self) -> Dict[str, float]:
        """
        获取模块加载时间统计
        
        Returns:
            Dict: 模块加载时间统计
        """
        return self.load_times

# 全局预加载器实例
_preloader = None

def get_preloader() -> ModulePreloader:
    """
    获取全局预加载器实例
    
    Returns:
        ModulePreloader: 预加载器实例
    """
    global _preloader
    if _preloader is None:
        _preloader = ModulePreloader()
    return _preloader

def preload_all():
    """预加载所有模块"""
    preloader = get_preloader()
    preloader.start()
    preloader.preload_common_modules()
    # 财务API通常较慢，可选择性加载
    preloader.preload_financial_apis()
    
    # 返回预加载器以便后续获取模块
    return preloader