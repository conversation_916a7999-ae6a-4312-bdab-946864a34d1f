#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证和清洗模块

提供股票数据的验证、清洗和质量控制功能
支持实时数据、历史数据和成分股数据的处理
"""

import pandas as pd
import numpy as np
import re
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
from pathlib import Path

try:
    from utils.logging_config import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)


class DataValidator:
    """
    数据验证器
    
    提供各种数据验证功能，包括数值范围、格式、逻辑关系等验证
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化数据验证器
        
        Args:
            config: 验证配置字典
        """
        self.config = config or self._get_default_config()
        self.logger = get_logger(__name__)
        self.validation_stats = {
            'total_rows': 0,
            'valid_rows': 0,
            'invalid_rows': 0,
            'validation_rate': 0.0,
            'error_details': []
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认验证配置
        
        Returns:
            默认配置字典
        """
        return {
            'price_range': {'min': 0.01, 'max': 1000.0},
            'volume_range': {'min': 0, 'max': 1000000000},
            'market_cap_range': {'min': 1000000, 'max': 10000000000000},
            'required_columns': ['code', 'name', 'price'],
            'string_validation': {
                'code': {'pattern': r'^[0-9]{6}$'},
                'name': {'min_length': 1, 'max_length': 20}
            }
        }
    
    def validate_stock_code(self, code: Any) -> bool:
        """
        验证股票代码格式
        
        Args:
            code: 股票代码
            
        Returns:
            是否有效
        """
        if pd.isna(code) or code is None:
            return False
        
        code_str = str(code).strip()
        if not code_str:
            return False
        
        # 检查是否为6位数字
        pattern = self.config.get('string_validation', {}).get('code', {}).get('pattern', r'^[0-9]{6}$')
        return bool(re.match(pattern, code_str))
    
    def validate_price(self, price: Any) -> bool:
        """
        验证价格数据
        
        Args:
            price: 价格值
            
        Returns:
            是否有效
        """
        if pd.isna(price) or price is None:
            return False
        
        try:
            price_float = float(price)
            if not np.isfinite(price_float):
                return False
            
            price_range = self.config.get('price_range', {'min': 0.01, 'max': 1000.0})
            return price_range['min'] <= price_float <= price_range['max']
        except (ValueError, TypeError):
            return False
    
    def validate_volume(self, volume: Any) -> bool:
        """
        验证成交量数据
        
        Args:
            volume: 成交量值
            
        Returns:
            是否有效
        """
        if pd.isna(volume) or volume is None:
            return False  # 成交量不能为空
        
        # 特殊情况：允许成交量为0（停牌等情况）
        if volume == 0:
            return True
        
        try:
            volume_float = float(volume)
            if not np.isfinite(volume_float):
                return False
            
            volume_range = self.config.get('volume_range', {'min': 0, 'max': 1000000000})
            return volume_range['min'] <= volume_float <= volume_range['max']
        except (ValueError, TypeError):
            return False
    
    def validate_market_cap(self, market_cap: Any) -> bool:
        """
        验证市值数据
        
        Args:
            market_cap: 市值
            
        Returns:
            是否有效
        """
        if pd.isna(market_cap):
            return True  # 允许市值为空
        
        if market_cap is None:
            return False
        
        try:
            cap_float = float(market_cap)
            if not np.isfinite(cap_float):
                return False
            
            cap_range = self.config.get('market_cap_range', {'min': 1000000, 'max': 10000000000000})
            return cap_range['min'] <= cap_float <= cap_range['max']
        except (ValueError, TypeError):
            return False
    
    def validate_string_field(self, value: Any, field_name: str) -> bool:
        """
        验证字符串字段
        
        Args:
            value: 字段值
            field_name: 字段名
            
        Returns:
            是否有效
        """
        if pd.isna(value) or value is None:
            return False
        
        value_str = str(value).strip()
        if not value_str:
            return False
        
        string_config = self.config.get('string_validation', {}).get(field_name, {})
        
        # 检查长度
        min_length = string_config.get('min_length', 0)
        max_length = string_config.get('max_length', float('inf'))
        if not (min_length <= len(value_str) <= max_length):
            return False
        
        # 检查正则表达式
        pattern = string_config.get('pattern')
        if pattern and not re.match(pattern, value_str):
            return False
        
        return True
    
    def validate_ohlc_logic(self, row: pd.Series) -> bool:
        """
        验证OHLC价格逻辑关系
        
        Args:
            row: 包含OHLC数据的行
            
        Returns:
            是否符合逻辑
        """
        required_cols = ['open', 'high', 'low', 'close']
        if not all(col in row.index for col in required_cols):
            return True  # 如果没有完整的OHLC数据，跳过验证
        
        try:
            open_price = float(row['open'])
            high_price = float(row['high'])
            low_price = float(row['low'])
            close_price = float(row['close'])
            
            # 检查是否都是有效数值
            prices = [open_price, high_price, low_price, close_price]
            if not all(np.isfinite(p) for p in prices):
                return False
            
            # 检查高价是否为最高，低价是否为最低
            if high_price < max(open_price, close_price) or high_price < low_price:
                return False
            
            if low_price > min(open_price, close_price) or low_price > high_price:
                return False
            
            return True
            
        except (ValueError, TypeError):
            return False
    
    def validate_row(self, row: pd.Series) -> Tuple[bool, List[str]]:
        """
        验证单行数据
        
        Args:
            row: 数据行
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 检查必需列
        required_columns = self.config.get('required_columns', [])
        for col in required_columns:
            if col not in row.index or pd.isna(row[col]):
                errors.append(f"缺少必需字段: {col}")
        
        # 验证股票代码
        if 'code' in row.index and not self.validate_stock_code(row['code']):
            errors.append("无效的股票代码")
        
        # 验证价格相关字段
        price_fields = ['price', 'open', 'high', 'low', 'close']
        for field in price_fields:
            if field in row.index and not pd.isna(row[field]):
                if not self.validate_price(row[field]):
                    errors.append(f"无效的价格字段: {field}")
        
        # 验证成交量
        if 'volume' in row.index and not self.validate_volume(row['volume']):
            errors.append("无效的成交量")
        
        # 验证市值
        if 'market_cap' in row.index and not self.validate_market_cap(row['market_cap']):
            errors.append("无效的市值")
        
        # 验证字符串字段
        string_fields = ['name']
        for field in string_fields:
            if field in row.index and not self.validate_string_field(row[field], field):
                errors.append(f"无效的字符串字段: {field}")
        
        # 验证OHLC逻辑
        if not self.validate_ohlc_logic(row):
            errors.append("OHLC价格逻辑错误")
        
        return len(errors) == 0, errors
    
    def validate_dataframe(self, df: pd.DataFrame) -> pd.Series:
        """
        验证整个DataFrame
        
        Args:
            df: 要验证的DataFrame
            
        Returns:
            布尔Series，表示每行是否有效
        """
        if df is None or df.empty:
            self.logger.warning("输入DataFrame为空")
            return pd.Series([], dtype=bool)
        
        valid_mask = pd.Series(True, index=df.index)
        error_details = []
        
        for idx, row in df.iterrows():
            is_valid, errors = self.validate_row(row)
            valid_mask[idx] = is_valid
            
            if not is_valid:
                error_details.append({
                    'row_index': idx,
                    'errors': errors
                })
        
        # 更新统计信息
        self.validation_stats.update({
            'total_rows': len(df),
            'valid_rows': valid_mask.sum(),
            'invalid_rows': (~valid_mask).sum(),
            'validation_rate': valid_mask.mean(),
            'error_details': error_details
        })
        
        self.logger.info(f"数据验证完成: {self.validation_stats['valid_rows']}/{self.validation_stats['total_rows']} 行有效")
        
        return valid_mask
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """
        获取验证摘要信息
        
        Returns:
            验证摘要字典
        """
        return self.validation_stats.copy()
    
    def reset_stats(self):
        """
        重置验证统计信息
        """
        self.validation_stats = {
            'total_rows': 0,
            'valid_rows': 0,
            'invalid_rows': 0,
            'validation_rate': 0.0,
            'error_details': []
        }


class DataCleaner:
    """
    数据清洗器
    
    提供数据清洗功能，包括缺失值处理、异常值处理、格式标准化等
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化数据清洗器
        
        Args:
            config: 清洗配置字典
        """
        self.config = config or self._get_default_config()
        self.logger = get_logger(__name__)
        self.cleaning_stats = {
            'total_rows': 0,
            'cleaned_rows': 0,
            'removed_rows': 0,
            'cleaning_operations': []
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        获取默认清洗配置
        
        Returns:
            默认配置字典
        """
        return {
            'missing_value_strategy': {
                'numeric_columns': 'interpolate',
                'string_columns': 'drop'
            },
            'outlier_handling': {
                'method': 'cap',
                'percentile_range': [1, 99]
            },
            'string_cleaning': {
                'strip_whitespace': True,
                'convert_empty_to_nan': True,
                'normalize_encoding': True
            }
        }
    
    def clean_string_columns(self, df: pd.DataFrame, columns: List[str] = None) -> pd.DataFrame:
        """
        清洗字符串列
        
        Args:
            df: 要清洗的DataFrame
            columns: 要清洗的列名列表
            
        Returns:
            清洗后的DataFrame
        """
        df_cleaned = df.copy()
        
        if columns is None:
            columns = df_cleaned.select_dtypes(include=['object', 'string']).columns.tolist()
        
        string_config = self.config.get('string_cleaning', {})
        
        for col in columns:
            if col not in df_cleaned.columns:
                continue
            
            # 去除空白字符
            if string_config.get('strip_whitespace', True):
                df_cleaned[col] = df_cleaned[col].astype(str).str.strip()
            
            # 将空字符串转换为NaN
            if string_config.get('convert_empty_to_nan', True):
                df_cleaned[col] = df_cleaned[col].replace('', np.nan)
                df_cleaned[col] = df_cleaned[col].replace('nan', np.nan)
        
        self.cleaning_stats['cleaning_operations'].append(f"清洗字符串列: {columns}")
        return df_cleaned
    
    def fill_missing_values(self, df: pd.DataFrame, method: str = 'auto', 
                          columns: List[str] = None) -> pd.DataFrame:
        """
        填充缺失值
        
        Args:
            df: 要处理的DataFrame
            method: 填充方法 ('forward', 'backward', 'mean', 'median', 'interpolate', 'auto')
            columns: 要处理的列名列表
            
        Returns:
            填充后的DataFrame
        """
        df_filled = df.copy()
        
        if columns is None:
            columns = df_filled.columns.tolist()
        
        for col in columns:
            if col not in df_filled.columns:
                continue
            
            if df_filled[col].isna().sum() == 0:
                continue  # 没有缺失值，跳过
            
            try:
                if method == 'auto':
                    # 根据数据类型自动选择方法
                    if pd.api.types.is_numeric_dtype(df_filled[col]):
                        fill_method = 'interpolate'
                    else:
                        fill_method = 'forward'
                else:
                    fill_method = method
                
                if fill_method == 'forward':
                    df_filled[col] = df_filled[col].ffill()
                elif fill_method == 'backward':
                    df_filled[col] = df_filled[col].bfill()
                elif fill_method == 'mean' and pd.api.types.is_numeric_dtype(df_filled[col]):
                    df_filled[col] = df_filled[col].fillna(df_filled[col].mean())
                elif fill_method == 'median' and pd.api.types.is_numeric_dtype(df_filled[col]):
                    df_filled[col] = df_filled[col].fillna(df_filled[col].median())
                elif fill_method == 'interpolate' and pd.api.types.is_numeric_dtype(df_filled[col]):
                    df_filled[col] = df_filled[col].interpolate()
                
            except Exception as e:
                self.logger.warning(f"填充列 {col} 时出错: {str(e)}")
        
        self.cleaning_stats['cleaning_operations'].append(f"填充缺失值: {method}")
        return df_filled
    
    def remove_outliers(self, df: pd.DataFrame, columns: List[str] = None, 
                       method: str = 'iqr') -> pd.DataFrame:
        """
        移除异常值
        
        Args:
            df: 要处理的DataFrame
            columns: 要处理的列名列表
            method: 异常值检测方法 ('iqr', 'zscore')
            
        Returns:
            移除异常值后的DataFrame
        """
        df_cleaned = df.copy()
        
        if columns is None:
            columns = df_cleaned.select_dtypes(include=[np.number]).columns.tolist()
        
        outlier_mask = pd.Series(False, index=df_cleaned.index)
        
        for col in columns:
            if col not in df_cleaned.columns:
                continue
            
            col_data = df_cleaned[col].dropna()
            if len(col_data) == 0:
                continue
            
            try:
                if method == 'iqr':
                    Q1 = col_data.quantile(0.25)
                    Q3 = col_data.quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    
                    col_outliers = (df_cleaned[col] < lower_bound) | (df_cleaned[col] > upper_bound)
                    
                elif method == 'zscore':
                    z_scores = np.abs((df_cleaned[col] - col_data.mean()) / col_data.std())
                    col_outliers = z_scores > 3
                
                outlier_mask |= col_outliers.fillna(False)
                
            except Exception as e:
                self.logger.warning(f"处理列 {col} 的异常值时出错: {str(e)}")
        
        # 移除异常值
        df_cleaned = df_cleaned[~outlier_mask]
        
        removed_count = outlier_mask.sum()
        self.cleaning_stats['removed_rows'] += removed_count
        self.cleaning_stats['cleaning_operations'].append(f"移除异常值: {removed_count} 行")
        
        return df_cleaned
    
    def standardize_column_names(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化列名
        
        Args:
            df: 要处理的DataFrame
            
        Returns:
            标准化列名后的DataFrame
        """
        df_standardized = df.copy()
        
        # 标准化列名：小写、去空格、替换特殊字符
        new_columns = []
        for col in df_standardized.columns:
            new_col = str(col).lower().strip()
            new_col = re.sub(r'[^a-z0-9_]', '_', new_col)
            new_col = re.sub(r'_+', '_', new_col)
            new_col = new_col.strip('_')
            new_columns.append(new_col)
        
        df_standardized.columns = new_columns
        
        self.cleaning_stats['cleaning_operations'].append("标准化列名")
        return df_standardized
    
    def clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        执行完整的DataFrame清洗
        
        Args:
            df: 要清洗的DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        if df is None or df.empty:
            self.logger.warning("输入DataFrame为空")
            return df
        
        original_rows = len(df)
        self.cleaning_stats['total_rows'] = original_rows
        
        df_cleaned = df.copy()
        
        try:
            # 1. 清洗字符串列
            df_cleaned = self.clean_string_columns(df_cleaned)
            
            # 2. 填充缺失值
            df_cleaned = self.fill_missing_values(df_cleaned)
            
            # 3. 移除异常值
            outlier_config = self.config.get('outlier_handling', {})
            if outlier_config.get('method') == 'remove':
                df_cleaned = self.remove_outliers(df_cleaned)
            
            # 4. 标准化列名
            # df_cleaned = self.standardize_column_names(df_cleaned)
            
            self.cleaning_stats['cleaned_rows'] = len(df_cleaned)
            
            self.logger.info(f"数据清洗完成: {original_rows} -> {len(df_cleaned)} 行")
            
        except Exception as e:
            self.logger.error(f"数据清洗失败: {str(e)}")
            return df
        
        return df_cleaned
    
    def get_cleaning_summary(self) -> Dict[str, Any]:
        """
        获取清洗摘要信息
        
        Returns:
            清洗摘要字典
        """
        return self.cleaning_stats.copy()
    
    def reset_stats(self):
        """
        重置清洗统计信息
        """
        self.cleaning_stats = {
            'total_rows': 0,
            'cleaned_rows': 0,
            'removed_rows': 0,
            'cleaning_operations': []
        }


class StockDataValidatorFactory:
    """
    股票数据验证器工厂类
    
    根据不同的数据类型创建相应的验证器
    """
    
    @staticmethod
    def create_realtime_validator() -> DataValidator:
        """
        创建实时数据验证器
        
        Returns:
            实时数据验证器
        """
        config = {
            'price_range': {'min': 0.01, 'max': 1000.0},
            'volume_range': {'min': 0, 'max': 1000000000},
            'required_columns': ['code', 'name', 'price'],
            'string_validation': {
                'code': {'pattern': r'^[0-9]{6}$'},
                'name': {'min_length': 1, 'max_length': 20}
            }
        }
        return DataValidator(config)
    
    @staticmethod
    def create_historical_validator() -> DataValidator:
        """
        创建历史数据验证器
        
        Returns:
            历史数据验证器
        """
        config = {
            'price_range': {'min': 0.01, 'max': 2000.0},
            'volume_range': {'min': 0, 'max': 5000000000},
            'required_columns': ['code', 'date', 'open', 'high', 'low', 'close'],
            'string_validation': {
                'code': {'pattern': r'^[0-9]{6}$'}
            }
        }
        return DataValidator(config)
    
    @staticmethod
    def create_constituent_validator() -> DataValidator:
        """
        创建成分股数据验证器
        
        Returns:
            成分股数据验证器
        """
        config = {
            'price_range': {'min': 0.01, 'max': 1500.0},
            'volume_range': {'min': 0, 'max': 2000000000},
            'market_cap_range': {'min': 5000000, 'max': 20000000000000},
            'required_columns': ['code', 'name', 'price'],
            'string_validation': {
                'code': {'pattern': r'^[0-9]{6}$'},
                'name': {'min_length': 1, 'max_length': 15}
            }
        }
        return DataValidator(config)


class StockDataCleaningRules:
    """
    股票数据清洗规则类
    
    根据不同的数据类型创建相应的清洗器
    """
    
    @staticmethod
    def create_realtime_cleaner() -> DataCleaner:
        """
        创建实时数据清洗器
        
        Returns:
            实时数据清洗器
        """
        config = {
            'missing_value_strategy': {
                'numeric_columns': 'interpolate',
                'string_columns': 'drop'
            },
            'outlier_handling': {
                'method': 'cap',
                'percentile_range': [1, 99]
            },
            'string_cleaning': {
                'strip_whitespace': True,
                'convert_empty_to_nan': True,
                'normalize_encoding': True
            }
        }
        return DataCleaner(config)
    
    @staticmethod
    def create_historical_cleaner() -> DataCleaner:
        """
        创建历史数据清洗器
        
        Returns:
            历史数据清洗器
        """
        config = {
            'missing_value_strategy': {
                'numeric_columns': 'forward_fill',
                'string_columns': 'drop'
            },
            'outlier_handling': {
                'method': 'remove',
                'zscore_threshold': 3.0
            },
            'string_cleaning': {
                'strip_whitespace': True,
                'convert_empty_to_nan': True,
                'normalize_encoding': True
            }
        }
        return DataCleaner(config)
    
    @staticmethod
    def create_constituent_cleaner() -> DataCleaner:
        """
        创建成分股数据清洗器
        
        Returns:
            成分股数据清洗器
        """
        config = {
            'missing_value_strategy': {
                'numeric_columns': 'mean',
                'string_columns': 'drop'
            },
            'outlier_handling': {
                'method': 'cap',
                'percentile_range': [2, 98]
            },
            'string_cleaning': {
                'strip_whitespace': True,
                'convert_empty_to_nan': True,
                'normalize_encoding': True
            }
        }
        return DataCleaner(config)


class ValidationConfigManager:
    """
    验证配置管理器
    
    管理不同场景下的验证和清洗配置
    """
    
    def __init__(self, config_file: str = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.logger = get_logger(__name__)
        self.configs = self._load_default_configs()
        
        if config_file and Path(config_file).exists():
            try:
                self.load_config(config_file)
            except Exception as e:
                self.logger.warning(f"加载配置文件失败: {str(e)}，使用默认配置")
    
    def _load_default_configs(self) -> Dict[str, Any]:
        """
        加载默认配置
        
        Returns:
            默认配置字典
        """
        return {
            'realtime': {
                'price_range': {'min': 0.01, 'max': 1000.0},
                'volume_range': {'min': 0, 'max': 1000000000},
                'required_columns': ['code', 'name', 'price']
            },
            'historical': {
                'price_range': {'min': 0.01, 'max': 2000.0},
                'volume_range': {'min': 0, 'max': 5000000000},
                'required_columns': ['code', 'date', 'open', 'high', 'low', 'close']
            },
            'constituent': {
                'price_range': {'min': 0.01, 'max': 1500.0},
                'volume_range': {'min': 0, 'max': 2000000000},
                'required_columns': ['code', 'name', 'price']
            }
        }
    
    def get_validation_config(self, config_type: str) -> Dict[str, Any]:
        """
        获取验证配置
        
        Args:
            config_type: 配置类型
            
        Returns:
            验证配置字典
        """
        return self.configs.get(config_type, self.configs['realtime'])
    
    def update_validation_config(self, config_type: str, config: Dict[str, Any]):
        """
        更新验证配置
        
        Args:
            config_type: 配置类型
            config: 新的配置
        """
        self.configs[config_type] = config
        self.logger.info(f"配置 {config_type} 已更新")
    
    def save_config(self, config_file: str):
        """
        保存配置到文件
        
        Args:
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.configs, f, ensure_ascii=False, indent=2)
            self.logger.info(f"配置已保存到: {config_file}")
        except Exception as e:
            self.logger.error(f"保存配置失败: {str(e)}")
            raise
    
    def load_config(self, config_file: str):
        """
        从文件加载配置
        
        Args:
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                loaded_configs = json.load(f)
            
            # 合并配置（保留默认配置，更新加载的配置）
            for key, value in loaded_configs.get('validation_configs', {}).items():
                self.configs[key] = value
            
            self.logger.info(f"配置已从 {config_file} 加载")
        except Exception as e:
            self.logger.error(f"加载配置失败: {str(e)}")
            raise


# 便捷函数
def validate_stock_dataframe(df: pd.DataFrame, data_type: str = 'realtime') -> Tuple[pd.Series, Dict[str, Any]]:
    """
    便捷函数：验证股票DataFrame
    
    Args:
        df: 要验证的DataFrame
        data_type: 数据类型 ('realtime', 'historical', 'constituent')
        
    Returns:
        (验证结果掩码, 验证摘要)
    """
    if data_type == 'realtime':
        validator = StockDataValidatorFactory.create_realtime_validator()
    elif data_type == 'historical':
        validator = StockDataValidatorFactory.create_historical_validator()
    elif data_type == 'constituent':
        validator = StockDataValidatorFactory.create_constituent_validator()
    else:
        validator = StockDataValidatorFactory.create_realtime_validator()
    
    valid_mask = validator.validate_dataframe(df)
    summary = validator.get_validation_summary()
    
    return valid_mask, summary


def clean_stock_dataframe(df: pd.DataFrame, data_type: str = 'realtime') -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    便捷函数：清洗股票DataFrame
    
    Args:
        df: 要清洗的DataFrame
        data_type: 数据类型 ('realtime', 'historical', 'constituent')
        
    Returns:
        (清洗后的DataFrame, 清洗摘要)
    """
    if data_type == 'realtime':
        cleaner = StockDataCleaningRules.create_realtime_cleaner()
    elif data_type == 'historical':
        cleaner = StockDataCleaningRules.create_historical_cleaner()
    elif data_type == 'constituent':
        cleaner = StockDataCleaningRules.create_constituent_cleaner()
    else:
        cleaner = StockDataCleaningRules.create_realtime_cleaner()
    
    cleaned_df = cleaner.clean_dataframe(df)
    summary = cleaner.get_cleaning_summary()
    
    return cleaned_df, summary