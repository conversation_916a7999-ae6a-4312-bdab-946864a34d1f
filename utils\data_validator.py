#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证核心模块
提供统一的数据验证框架，支持多种验证规则和异常处理策略

作者: LilyBullRider Team
创建时间: 2024
"""

import re
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Callable, Tuple
from datetime import datetime, date
from enum import Enum
from dataclasses import dataclass

# 配置日志
logger = logging.getLogger(__name__)

class ValidationLevel(Enum):
    """验证级别枚举"""
    STRICT = "strict"      # 严格模式，任何验证失败都抛出异常
    WARNING = "warning"    # 警告模式，记录警告但继续执行
    LENIENT = "lenient"    # 宽松模式，尽可能修复数据

class ValidationResult(Enum):
    """验证结果枚举"""
    PASS = "pass"          # 验证通过
    WARNING = "warning"    # 验证警告
    FAIL = "fail"          # 验证失败
    FIXED = "fixed"        # 数据已修复

@dataclass
class ValidationError:
    """验证错误信息"""
    field: str
    value: Any
    rule: str
    message: str
    level: ValidationResult
    suggested_fix: Optional[Any] = None

@dataclass
class ValidationReport:
    """验证报告"""
    total_records: int
    passed: int
    warnings: int
    failures: int
    fixed: int
    errors: List[ValidationError]
    execution_time: float
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.total_records == 0:
            return 0.0
        return (self.passed + self.fixed) / self.total_records
    
    @property
    def quality_score(self) -> float:
        """计算数据质量评分 (0-100)"""
        if self.total_records == 0:
            return 100.0
        
        # 权重分配：通过=1.0, 修复=0.8, 警告=0.6, 失败=0.0
        weighted_score = (
            self.passed * 1.0 + 
            self.fixed * 0.8 + 
            self.warnings * 0.6 + 
            self.failures * 0.0
        ) / self.total_records
        
        return min(100.0, weighted_score * 100)

class BaseValidator(ABC):
    """验证器基类"""
    
    def __init__(self, name: str, level: ValidationLevel = ValidationLevel.WARNING):
        self.name = name
        self.level = level
        self.enabled = True
    
    @abstractmethod
    def validate(self, value: Any, context: Optional[Dict] = None) -> Tuple[ValidationResult, Any, Optional[str]]:
        """
        验证数据
        
        Args:
            value: 待验证的值
            context: 验证上下文信息
            
        Returns:
            Tuple[ValidationResult, Any, Optional[str]]: (验证结果, 处理后的值, 错误信息)
        """
        pass
    
    def is_enabled(self) -> bool:
        """检查验证器是否启用"""
        return self.enabled
    
    def enable(self):
        """启用验证器"""
        self.enabled = True
    
    def disable(self):
        """禁用验证器"""
        self.enabled = False

class DataTypeValidator(BaseValidator):
    """数据类型验证器"""
    
    def __init__(self, expected_type: type, allow_none: bool = False, 
                 level: ValidationLevel = ValidationLevel.WARNING):
        super().__init__(f"DataType_{expected_type.__name__}", level)
        self.expected_type = expected_type
        self.allow_none = allow_none
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> Tuple[ValidationResult, Any, Optional[str]]:
        if value is None:
            if self.allow_none:
                return ValidationResult.PASS, value, None
            else:
                return ValidationResult.FAIL, value, f"值不能为None"
        
        if isinstance(value, self.expected_type):
            return ValidationResult.PASS, value, None
        
        # 尝试类型转换
        try:
            if self.expected_type == int:
                converted_value = int(float(value))  # 支持从字符串和浮点数转换
                return ValidationResult.FIXED, converted_value, f"已将 {type(value).__name__} 转换为 int"
            elif self.expected_type == float:
                converted_value = float(value)
                return ValidationResult.FIXED, converted_value, f"已将 {type(value).__name__} 转换为 float"
            elif self.expected_type == str:
                converted_value = str(value)
                return ValidationResult.FIXED, converted_value, f"已将 {type(value).__name__} 转换为 str"
            else:
                converted_value = self.expected_type(value)
                return ValidationResult.FIXED, converted_value, f"已转换为 {self.expected_type.__name__}"
        except (ValueError, TypeError) as e:
            return ValidationResult.FAIL, value, f"无法转换为 {self.expected_type.__name__}: {str(e)}"

class RangeValidator(BaseValidator):
    """数值范围验证器"""
    
    def __init__(self, min_value: Optional[Union[int, float]] = None, 
                 max_value: Optional[Union[int, float]] = None,
                 level: ValidationLevel = ValidationLevel.WARNING):
        super().__init__("Range", level)
        self.min_value = min_value
        self.max_value = max_value
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> Tuple[ValidationResult, Any, Optional[str]]:
        if value is None:
            return ValidationResult.PASS, value, None
        
        try:
            num_value = float(value)
        except (ValueError, TypeError):
            return ValidationResult.FAIL, value, "无法转换为数值进行范围检查"
        
        if self.min_value is not None and num_value < self.min_value:
            if self.level == ValidationLevel.LENIENT:
                return ValidationResult.FIXED, self.min_value, f"值 {num_value} 小于最小值 {self.min_value}，已修正"
            else:
                return ValidationResult.FAIL, value, f"值 {num_value} 小于最小值 {self.min_value}"
        
        if self.max_value is not None and num_value > self.max_value:
            if self.level == ValidationLevel.LENIENT:
                return ValidationResult.FIXED, self.max_value, f"值 {num_value} 大于最大值 {self.max_value}，已修正"
            else:
                return ValidationResult.FAIL, value, f"值 {num_value} 大于最大值 {self.max_value}"
        
        return ValidationResult.PASS, value, None

class RegexValidator(BaseValidator):
    """正则表达式验证器"""
    
    def __init__(self, pattern: str, flags: int = 0, 
                 level: ValidationLevel = ValidationLevel.WARNING):
        super().__init__("Regex", level)
        self.pattern = pattern
        self.regex = re.compile(pattern, flags)
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> Tuple[ValidationResult, Any, Optional[str]]:
        if value is None:
            return ValidationResult.FAIL, value, "值不能为None"
        
        str_value = str(value)
        if self.regex.match(str_value):
            return ValidationResult.PASS, value, None
        else:
            return ValidationResult.FAIL, value, f"值 '{str_value}' 不匹配模式 '{self.pattern}'"

class DateValidator(BaseValidator):
    """日期验证器"""
    
    def __init__(self, date_format: str = "%Y-%m-%d", 
                 min_date: Optional[Union[str, date, datetime]] = None,
                 max_date: Optional[Union[str, date, datetime]] = None,
                 level: ValidationLevel = ValidationLevel.WARNING):
        super().__init__("Date", level)
        self.date_format = date_format
        self.min_date = self._parse_date(min_date) if min_date else None
        self.max_date = self._parse_date(max_date) if max_date else None
    
    def _parse_date(self, date_value: Union[str, date, datetime]) -> date:
        """解析日期"""
        if isinstance(date_value, datetime):
            return date_value.date()
        elif isinstance(date_value, date):
            return date_value
        elif isinstance(date_value, str):
            return datetime.strptime(date_value, self.date_format).date()
        else:
            raise ValueError(f"无法解析日期: {date_value}")
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> Tuple[ValidationResult, Any, Optional[str]]:
        if value is None:
            return ValidationResult.FAIL, value, "日期不能为None"
        
        try:
            if isinstance(value, (date, datetime)):
                date_value = value.date() if isinstance(value, datetime) else value
            else:
                # 尝试解析字符串日期
                date_value = datetime.strptime(str(value), self.date_format).date()
            
            # 检查日期范围
            if self.min_date and date_value < self.min_date:
                return ValidationResult.FAIL, value, f"日期 {date_value} 早于最小日期 {self.min_date}"
            
            if self.max_date and date_value > self.max_date:
                return ValidationResult.FAIL, value, f"日期 {date_value} 晚于最大日期 {self.max_date}"
            
            return ValidationResult.PASS, date_value, None
            
        except ValueError as e:
            return ValidationResult.FAIL, value, f"日期格式错误: {str(e)}"

class CompositeValidator:
    """复合验证器，支持多个验证规则的组合"""
    
    def __init__(self, validators: List[BaseValidator], 
                 stop_on_first_failure: bool = False):
        self.validators = validators
        self.stop_on_first_failure = stop_on_first_failure
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> Tuple[ValidationResult, Any, List[str]]:
        """执行复合验证"""
        current_value = value
        overall_result = ValidationResult.PASS
        messages = []
        
        for validator in self.validators:
            if not validator.is_enabled():
                continue
                
            result, new_value, message = validator.validate(current_value, context)
            
            if message:
                messages.append(f"[{validator.name}] {message}")
            
            # 更新值（如果验证器修复了数据）
            if result == ValidationResult.FIXED:
                current_value = new_value
                overall_result = ValidationResult.FIXED
            
            # 处理验证失败
            if result == ValidationResult.FAIL:
                overall_result = ValidationResult.FAIL
                if self.stop_on_first_failure:
                    break
            
            # 处理警告
            if result == ValidationResult.WARNING and overall_result == ValidationResult.PASS:
                overall_result = ValidationResult.WARNING
        
        return overall_result, current_value, messages

class DataValidator:
    """数据验证管理器"""
    
    def __init__(self, level: ValidationLevel = ValidationLevel.WARNING):
        self.level = level
        self.field_validators: Dict[str, CompositeValidator] = {}
        self.global_validators: List[BaseValidator] = []
        self.stats = {
            'total_validations': 0,
            'total_failures': 0,
            'total_fixes': 0,
            'validation_time': 0.0
        }
    
    def add_field_validator(self, field_name: str, validators: List[BaseValidator]):
        """为特定字段添加验证器"""
        self.field_validators[field_name] = CompositeValidator(validators)
    
    def add_global_validator(self, validator: BaseValidator):
        """添加全局验证器"""
        self.global_validators.append(validator)
    
    def validate_record(self, record: Dict[str, Any], 
                       context: Optional[Dict] = None) -> Tuple[Dict[str, Any], ValidationReport]:
        """验证单条记录"""
        start_time = datetime.now()
        validated_record = record.copy()
        errors = []
        
        # 验证各个字段
        for field_name, validator in self.field_validators.items():
            if field_name in record:
                result, new_value, messages = validator.validate(record[field_name], context)
                
                if messages:
                    for message in messages:
                        error = ValidationError(
                            field=field_name,
                            value=record[field_name],
                            rule=validator.__class__.__name__,
                            message=message,
                            level=result,
                            suggested_fix=new_value if result == ValidationResult.FIXED else None
                        )
                        errors.append(error)
                
                # 更新记录值
                if result in [ValidationResult.PASS, ValidationResult.FIXED, ValidationResult.WARNING]:
                    validated_record[field_name] = new_value
                elif result == ValidationResult.FAIL and self.level == ValidationLevel.STRICT:
                    # 严格模式下，验证失败则不更新值
                    pass
        
        # 执行全局验证
        for validator in self.global_validators:
            if validator.is_enabled():
                result, _, message = validator.validate(validated_record, context)
                if message:
                    error = ValidationError(
                        field="__global__",
                        value=validated_record,
                        rule=validator.name,
                        message=message,
                        level=result
                    )
                    errors.append(error)
        
        # 统计结果
        execution_time = (datetime.now() - start_time).total_seconds()
        passed = sum(1 for e in errors if e.level == ValidationResult.PASS)
        warnings = sum(1 for e in errors if e.level == ValidationResult.WARNING)
        failures = sum(1 for e in errors if e.level == ValidationResult.FAIL)
        fixed = sum(1 for e in errors if e.level == ValidationResult.FIXED)
        
        report = ValidationReport(
            total_records=1,
            passed=1 if not errors or all(e.level != ValidationResult.FAIL for e in errors) else 0,
            warnings=warnings,
            failures=failures,
            fixed=fixed,
            errors=errors,
            execution_time=execution_time
        )
        
        # 更新统计信息
        self.stats['total_validations'] += 1
        self.stats['total_failures'] += failures
        self.stats['total_fixes'] += fixed
        self.stats['validation_time'] += execution_time
        
        return validated_record, report
    
    def validate_batch(self, records: List[Dict[str, Any]], 
                      context: Optional[Dict] = None) -> Tuple[List[Dict[str, Any]], ValidationReport]:
        """批量验证记录"""
        start_time = datetime.now()
        validated_records = []
        all_errors = []
        total_passed = 0
        total_warnings = 0
        total_failures = 0
        total_fixed = 0
        
        for record in records:
            validated_record, report = self.validate_record(record, context)
            validated_records.append(validated_record)
            all_errors.extend(report.errors)
            
            if report.passed > 0:
                total_passed += 1
            total_warnings += report.warnings
            total_failures += report.failures
            total_fixed += report.fixed
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        batch_report = ValidationReport(
            total_records=len(records),
            passed=total_passed,
            warnings=total_warnings,
            failures=total_failures,
            fixed=total_fixed,
            errors=all_errors,
            execution_time=execution_time
        )
        
        return validated_records, batch_report
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        return self.stats.copy()
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'total_validations': 0,
            'total_failures': 0,
            'total_fixes': 0,
            'validation_time': 0.0
        }

# 验证装饰器
def validate_data(validator: DataValidator, context_key: str = None):
    """数据验证装饰器"""
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 如果结果是字典或字典列表，进行验证
            if isinstance(result, dict):
                context = {context_key: kwargs.get(context_key)} if context_key else None
                validated_result, report = validator.validate_record(result, context)
                
                # 记录验证结果
                if report.failures > 0:
                    logger.warning(f"函数 {func.__name__} 返回数据验证失败: {report.failures} 个错误")
                if report.fixed > 0:
                    logger.info(f"函数 {func.__name__} 返回数据已修复: {report.fixed} 个字段")
                
                return validated_result
            
            elif isinstance(result, list) and result and isinstance(result[0], dict):
                context = {context_key: kwargs.get(context_key)} if context_key else None
                validated_result, report = validator.validate_batch(result, context)
                
                # 记录验证结果
                logger.info(f"函数 {func.__name__} 批量验证完成: "
                          f"总计 {report.total_records} 条记录, "
                          f"通过 {report.passed} 条, "
                          f"警告 {report.warnings} 条, "
                          f"失败 {report.failures} 条, "
                          f"修复 {report.fixed} 条")
                
                return validated_result
            
            return result
        
        return wrapper
    return decorator