#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
非线性动力学因子计算模块

实现基于非线性动力学理论的因子计算，包括：
1. 相空间重构熵（Phase Space Entropy）
2. 李雅普诺夫指数（Lyapunov Exponent）
3. 多重分形谱宽度（Multifractal Spectrum Width）
4. 赫斯特指数修正（Hurst Exponent Correction）
5. 持续性同调（Persistent Homology）
6. 沃瑟斯坦距离（Wasserstein Distance）
7. 费舍尔信息矩阵（Fisher Information Matrix）
8. 指数族流形曲率（Exponential Family Curvature）
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from scipy import stats, signal, optimize
from scipy.spatial.distance import pdist, squareform
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import warnings
import akshare as ak
from utils.logging_config import get_logger
warnings.filterwarnings('ignore')

# 获取配置好的logger实例
logger = get_logger(__name__)

class NonlinearFactors:
    """非线性动力学因子计算类"""
    
    def __init__(self, data: Optional[pd.DataFrame] = None, symbol: str = None, 
                 start_date: str = None, end_date: str = None, adjust: str = "qfq"):
        """
        初始化非线性动力学因子计算类
        
        Parameters
        ----------
        data : pd.DataFrame, optional
            包含OHLCV数据的DataFrame，必须包含以下列：
            - close: 收盘价
            - high: 最高价
            - low: 最低价
            - open: 开盘价
            - volume: 成交量
            如果为None，则会使用symbol, start_date, end_date参数从AKShare获取数据
        symbol : str, optional
            股票代码，如"000001"，当data为None时必须提供
        start_date : str, optional
            开始日期，格式为"YYYYMMDD"，如"20240101"，当data为None时必须提供
        end_date : str, optional
            结束日期，格式为"YYYYMMDD"，如"20240531"，当data为None时必须提供
        adjust : str, optional
            复权方式，可选值为："qfq"（前复权）、"hfq"（后复权）、None（不复权）
            默认为"qfq"（前复权）
        """
        # 如果未提供数据，则从AKShare获取
        if data is None:
            if symbol is None or start_date is None:
                raise ValueError("当data为None时，必须提供symbol和start_date参数")
            
            # 如果未提供end_date，则使用当前日期
            if end_date is None:
                end_date = datetime.now().strftime("%Y%m%d")
                
            try:
                logger.info(f"从AKShare获取股票{symbol}的历史数据，时间范围: {start_date}至{end_date}")
                data = self._get_stock_data_from_akshare(symbol, start_date, end_date, adjust)
            except Exception as e:
                logger.error(f"从AKShare获取数据失败: {e}")
                raise
        
        self.data = data.copy()
        self._validate_data()
        
        # 预处理数据
        self._preprocess_data()
        
    def _get_stock_data_from_akshare(self, symbol: str, start_date: str, 
                                     end_date: str, adjust: str = "qfq") -> pd.DataFrame:
        """
        从AKShare获取股票历史数据
        
        Parameters
        ----------
        symbol : str
            股票代码，如"000001"
        start_date : str
            开始日期，格式为"YYYYMMDD"，如"20240101"
        end_date : str
            结束日期，格式为"YYYYMMDD"，如"20240531"
        adjust : str, optional
            复权方式，可选值为："qfq"（前复权）、"hfq"（后复权）、None（不复权）
            默认为"qfq"（前复权）
            
        Returns
        -------
        pd.DataFrame
            包含OHLCV数据的DataFrame
        """
        try:
            # 使用AKShare的stock_zh_a_hist函数获取A股历史数据
            stock_data = ak.stock_zh_a_hist(
                symbol=symbol,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                adjust=adjust
            )
            
            # 将字段名称转换为程序中使用的格式
            column_mapping = {
                "日期": "date",
                "开盘": "open",
                "收盘": "close",
                "最高": "high",
                "最低": "low",
                "成交量": "volume",
                "成交额": "amount",
                "振幅": "amplitude",
                "涨跌幅": "pct_change",
                "涨跌额": "change",
                "换手率": "turnover_rate"
            }
            
            # 重命名列
            stock_data = stock_data.rename(columns=column_mapping)
            
            # 将date字段转换为datetime类型
            stock_data['date'] = pd.to_datetime(stock_data['date'])
            
            # 确保数据按日期排序
            stock_data = stock_data.sort_values('date')
            
            # 将成交量转换为int类型（AKShare返回的成交量单位为手）
            stock_data['volume'] = stock_data['volume'].astype(int)
            
            return stock_data
            
        except Exception as e:
            logger.error(f"获取股票{symbol}历史数据失败: {e}")
            raise
        
    def _validate_data(self):
        """验证输入数据的完整性"""
        required_columns = ['close', 'high', 'low', 'open', 'volume']
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要的列: {missing_columns}")
            
    def _preprocess_data(self):
        """预处理数据"""
        # 确保数据按时间排序
        if 'date' in self.data.columns:
            self.data = self.data.sort_values('date')
        
        # 计算对数收益率
        self.data['log_return'] = np.log(self.data['close'] / self.data['close'].shift(1))
        
        # 计算波动率
        self.data['volatility'] = self.data['log_return'].rolling(window=20).std()
        
        # 标准化价格序列
        self.data['normalized_price'] = (self.data['close'] - self.data['close'].mean()) / self.data['close'].std()
        
    def calculate_all_factors(self, window: int = 30) -> pd.Series:
        """
        计算所有非线性动力学因子
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        pd.Series
            包含所有因子值的Series
        """
        factors = {}
        
        # 计算各类因子
        factors['phase_space_entropy'] = self.calculate_phase_space_entropy(window)
        factors['lyapunov_exponent'] = self.calculate_lyapunov_exponent(window)
        factors['multifractal_spectrum_width'] = self.calculate_multifractal_spectrum_width(window)
        factors['hurst_exponent'] = self.calculate_hurst_exponent(window)
        factors['persistent_homology'] = self.calculate_persistent_homology(window)
        factors['wasserstein_distance'] = self.calculate_wasserstein_distance(window)
        factors['fisher_information'] = self.calculate_fisher_information(window)
        factors['exponential_family_curvature'] = self.calculate_exponential_family_curvature(window)
        
        return pd.Series(factors)
    
    def calculate_phase_space_entropy(self, window: int = 30) -> float:
        """
        计算相空间重构熵（Phase Space Entropy）
        
        基于Takens嵌入定理，通过延迟坐标法重构相空间，计算熵值
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            相空间重构熵值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 确定嵌入维度（m=5）和延迟时间
        m = 5  # 嵌入维度
        tau = self._calculate_mutual_information(price_series)  # 延迟时间
        
        # 重构相空间
        N = len(price_series) - (m-1)*tau
        if N <= 0:
            return 0.0
            
        # 构建相空间轨迹矩阵
        X = np.zeros((N, m))
        for i in range(N):
            for j in range(m):
                X[i, j] = price_series[i + j*tau]
        
        # 计算状态概率密度
        # 使用网格划分方法
        grid_size = 10  # 网格大小
        min_val = np.min(X)
        max_val = np.max(X)
        bin_edges = np.linspace(min_val, max_val, grid_size+1)
        
        # 计算每个状态的概率
        hist, _ = np.histogramdd(X, bins=[bin_edges]*m)
        p = hist / np.sum(hist)
        
        # 计算熵值
        p = p[p > 0]  # 只考虑非零概率
        entropy = -np.sum(p * np.log(p))
        
        return float(entropy)
    
    def _calculate_mutual_information(self, series: np.ndarray, max_lag: int = 20) -> int:
        """
        计算互信息，用于确定延迟时间
        
        Parameters
        ----------
        series : np.ndarray
            时间序列
        max_lag : int, optional
            最大延迟时间，默认为20
            
        Returns
        -------
        int
            最优延迟时间
        """
        n = len(series)
        mi = np.zeros(max_lag)
        
        # 计算自互信息
        for lag in range(1, max_lag+1):
            if lag >= n:
                break
                
            # 构建延迟序列
            x = series[:-lag]
            y = series[lag:]
            
            # 计算联合分布
            hist_2d, x_edges, y_edges = np.histogram2d(x, y, bins=10)
            pxy = hist_2d / np.sum(hist_2d)
            
            # 计算边缘分布
            px = np.sum(pxy, axis=1)
            py = np.sum(pxy, axis=0)
            
            # 计算互信息
            mi[lag-1] = 0
            for i in range(len(x_edges)-1):
                for j in range(len(y_edges)-1):
                    if pxy[i,j] > 0 and px[i] > 0 and py[j] > 0:
                        mi[lag-1] += pxy[i,j] * np.log(pxy[i,j] / (px[i] * py[j]))
        
        # 找到第一个局部最小值
        for i in range(1, len(mi)-1):
            if mi[i] < mi[i-1] and mi[i] < mi[i+1]:
                return i+1
        
        # 如果没有找到局部最小值，返回第一个小于初始值的点
        for i in range(1, len(mi)):
            if mi[i] < mi[0]:
                return i+1
        
        return 1  # 默认返回1
    
    def calculate_lyapunov_exponent(self, window: int = 30) -> float:
        """
        计算李雅普诺夫指数（Lyapunov Exponent）
        
        使用Rosenstein算法估计李雅普诺夫指数
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            李雅普诺夫指数值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 确定嵌入维度和延迟时间
        m = 5  # 嵌入维度
        tau = self._calculate_mutual_information(price_series)  # 延迟时间
        
        # 重构相空间
        N = len(price_series) - (m-1)*tau
        if N <= 0:
            return 0.0
            
        # 构建相空间轨迹矩阵
        X = np.zeros((N, m))
        for i in range(N):
            for j in range(m):
                X[i, j] = price_series[i + j*tau]
        
        # Rosenstein算法
        # 1. 找到最近邻
        dist_matrix = squareform(pdist(X))
        np.fill_diagonal(dist_matrix, np.inf)  # 排除自身
        nearest_neighbors = np.argmin(dist_matrix, axis=1)
        
        # 2. 计算分离距离
        separation = np.zeros(N)
        for i in range(N):
            j = nearest_neighbors[i]
            separation[i] = np.linalg.norm(X[i] - X[j])
        
        # 3. 计算李雅普诺夫指数
        # 使用线性回归估计斜率
        time_steps = np.arange(N)
        valid_indices = separation > 0
        if np.sum(valid_indices) < 2:
            return 0.0
            
        slope, _, r_value, _, _ = stats.linregress(
            time_steps[valid_indices], 
            np.log(separation[valid_indices])
        )
        
        # 只有当拟合优度足够好时才返回斜率
        if r_value**2 > 0.5:
            return float(slope)
        else:
            return 0.0
    
    def calculate_multifractal_spectrum_width(self, window: int = 30) -> float:
        """
        计算多重分形谱宽度（Multifractal Spectrum Width）
        
        通过Legendre变换求取多重分形谱宽度
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            多重分形谱宽度值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 计算局部波动率
        local_volatility = np.abs(np.diff(price_series))
        
        # 定义q值范围
        q_values = np.arange(-5, 6, 0.5)
        
        # 计算τ(q)
        tau_q = np.zeros_like(q_values)
        for i, q in enumerate(q_values):
            # 计算q阶矩
            if q == 0:
                tau_q[i] = -np.sum(np.log(local_volatility + 1e-10))
            else:
                tau_q[i] = -np.log(np.sum(local_volatility**q) + 1e-10)
        
        # 通过数值微分计算α(q)
        alpha_q = np.gradient(tau_q, q_values)
        
        # 计算多重分形谱宽度
        delta_alpha = np.max(alpha_q) - np.min(alpha_q)
        
        return float(delta_alpha)
    
    def calculate_hurst_exponent(self, window: int = 30) -> float:
        """
        计算赫斯特指数修正（Hurst Exponent Correction）
        
        使用R/S分析并应用统计修正
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            修正后的赫斯特指数值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 计算对数收益率
        log_returns = np.log(price_series[1:] / price_series[:-1])
        
        # 计算不同时间尺度的R/S值
        scales = np.array([5, 10, 15, 20])
        rs_values = np.zeros_like(scales, dtype=float)
        
        for i, scale in enumerate(scales):
            if scale >= len(log_returns):
                continue
                
            # 将序列分成n个长度为scale的子序列
            n = len(log_returns) // scale
            if n == 0:
                continue
                
            # 计算每个子序列的R/S值
            rs_sub = np.zeros(n)
            for j in range(n):
                sub_series = log_returns[j*scale:(j+1)*scale]
                mean = np.mean(sub_series)
                # 计算累积离差
                cumsum = np.cumsum(sub_series - mean)
                # 计算R值（极差）
                R = np.max(cumsum) - np.min(cumsum)
                # 计算S值（标准差）
                S = np.std(sub_series)
                # 计算R/S值
                if S > 0:
                    rs_sub[j] = R / S
                else:
                    rs_sub[j] = 0
            
            # 计算平均R/S值
            rs_values[i] = np.mean(rs_sub)
        
        # 使用线性回归估计赫斯特指数
        valid_indices = rs_values > 0
        if np.sum(valid_indices) < 2:
            return 0.5  # 默认返回随机游走
            
        slope, _, r_value, _, _ = stats.linregress(
            np.log(scales[valid_indices]), 
            np.log(rs_values[valid_indices])
        )
        
        H_RS = slope  # 经典R/S分析结果
        
        # 应用统计修正
        # 计算赫斯特指数的标准差
        H_values = np.zeros(n)
        for j in range(n):
            sub_series = log_returns[j*scale:(j+1)*scale]
            mean = np.mean(sub_series)
            cumsum = np.cumsum(sub_series - mean)
            R = np.max(cumsum) - np.min(cumsum)
            S = np.std(sub_series)
            if S > 0:
                H_values[j] = np.log(R/S) / np.log(scale)
            else:
                H_values[j] = 0.5
                
        sigma_H = np.std(H_values)
        
        # 应用修正
        p = 0.05  # 显著性水平
        erf_inv = stats.norm.ppf(1-p)  # 近似erf^(-1)(1-2p)
        H_corr = H_RS + (sigma_H / np.sqrt(n)) * erf_inv
        
        return float(H_corr)
    
    def calculate_persistent_homology(self, window: int = 30) -> float:
        """
        计算持续性同调（Persistent Homology）
        
        使用简化版本计算一维同调群
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            持续性同调值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 将价格序列转换为点云
        time_points = np.arange(len(price_series))
        point_cloud = np.column_stack((time_points, price_series))
        
        # 计算点之间的距离矩阵
        dist_matrix = squareform(pdist(point_cloud))
        
        # 简化版本的持续性同调计算
        # 使用距离阈值构建图
        threshold = np.mean(dist_matrix)  # 使用平均距离作为阈值
        
        # 计算边的数量（一维同调）
        edges = np.sum(dist_matrix < threshold) - len(price_series)
        
        # 计算连通分量数量（零维同调）
        components = self._count_components(dist_matrix, threshold)
        
        # 计算一维同调群的秩
        beta_1 = edges - len(price_series) + components
        
        # 归一化
        beta_1_norm = beta_1 / len(price_series)
        
        return float(beta_1_norm)
    
    def _count_components(self, dist_matrix: np.ndarray, threshold: float) -> int:
        """
        计算图中的连通分量数量
        
        Parameters
        ----------
        dist_matrix : np.ndarray
            距离矩阵
        threshold : float
            距离阈值
            
        Returns
        -------
        int
            连通分量数量
        """
        n = len(dist_matrix)
        visited = np.zeros(n, dtype=bool)
        components = 0
        
        for i in range(n):
            if not visited[i]:
                # 使用深度优先搜索遍历连通分量
                self._dfs(dist_matrix, i, visited, threshold)
                components += 1
                
        return components
    
    def _dfs(self, dist_matrix: np.ndarray, v: int, visited: np.ndarray, threshold: float):
        """
        深度优先搜索
        
        Parameters
        ----------
        dist_matrix : np.ndarray
            距离矩阵
        v : int
            当前顶点
        visited : np.ndarray
            访问标记数组
        threshold : float
            距离阈值
        """
        visited[v] = True
        
        for i in range(len(dist_matrix)):
            if not visited[i] and dist_matrix[v, i] < threshold:
                self._dfs(dist_matrix, i, visited, threshold)
    
    def calculate_wasserstein_distance(self, window: int = 30) -> float:
        """
        计算沃瑟斯坦距离（Wasserstein Distance）
        
        计算与典型K线形态的距离
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            沃瑟斯坦距离值
        """
        # 获取窗口内的OHLC数据
        ohlc_data = self.data[['open', 'high', 'low', 'close']].tail(window).values
        
        # 定义典型K线形态
        patterns = {
            'hammer': np.array([0.0, 0.2, -0.3, 0.1]),  # 锤子线
            'shooting_star': np.array([0.0, 0.3, -0.2, -0.1]),  # 流星线
            'engulfing_bullish': np.array([-0.1, 0.2, -0.3, 0.1]),  # 看涨吞没
            'engulfing_bearish': np.array([0.1, -0.2, 0.3, -0.1])  # 看跌吞没
        }
        
        # 计算当前K线形态
        current_pattern = np.zeros(4)
        for i in range(4):
            if i == 0:  # 开盘价变化
                current_pattern[i] = (ohlc_data[-1, 0] - ohlc_data[-2, 3]) / ohlc_data[-2, 3]
            elif i == 1:  # 最高价变化
                current_pattern[i] = (ohlc_data[-1, 1] - ohlc_data[-1, 0]) / ohlc_data[-1, 0]
            elif i == 2:  # 最低价变化
                current_pattern[i] = (ohlc_data[-1, 2] - ohlc_data[-1, 0]) / ohlc_data[-1, 0]
            elif i == 3:  # 收盘价变化
                current_pattern[i] = (ohlc_data[-1, 3] - ohlc_data[-1, 0]) / ohlc_data[-1, 0]
        
        # 计算与各典型形态的Wasserstein距离
        distances = {}
        for name, pattern in patterns.items():
            # 使用一维Wasserstein距离（简化版本）
            # 在实际应用中，可以使用更复杂的算法
            distances[name] = np.sum(np.abs(np.sort(current_pattern) - np.sort(pattern)))
        
        # 返回最小距离
        min_distance = min(distances.values())
        
        # 归一化
        max_possible_distance = 2.0  # 估计的最大可能距离
        normalized_distance = min_distance / max_possible_distance
        
        return float(normalized_distance)
    
    def calculate_fisher_information(self, window: int = 30) -> float:
        """
        计算费舍尔信息矩阵（Fisher Information Matrix）
        
        基于价格变化估计费舍尔信息量
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            费舍尔信息量值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 计算对数收益率
        log_returns = np.log(price_series[1:] / price_series[:-1])
        
        # 假设收益率服从正态分布，估计参数
        mu = np.mean(log_returns)
        sigma = np.std(log_returns)
        
        # 计算费舍尔信息矩阵
        # 对于正态分布，费舍尔信息矩阵为：
        # I(μ,σ) = [[1/σ², 0], [0, 2/σ²]]
        I_mu_mu = 1 / (sigma**2)
        I_sigma_sigma = 2 / (sigma**2)
        
        # 计算费舍尔信息量（矩阵的迹）
        fisher_info = I_mu_mu + I_sigma_sigma
        
        # 计算信息冲击指标
        if window > 1:
            # 获取前一个窗口的数据
            prev_price_series = self.data['close'].tail(window-1).head(window-1).values
            prev_log_returns = np.log(prev_price_series[1:] / prev_price_series[:-1])
            prev_mu = np.mean(prev_log_returns)
            prev_sigma = np.std(prev_log_returns)
            prev_I_mu_mu = 1 / (prev_sigma**2)
            prev_I_sigma_sigma = 2 / (prev_sigma**2)
            prev_fisher_info = prev_I_mu_mu + prev_I_sigma_sigma
            
            # 计算信息冲击
            delta_I = fisher_info - prev_fisher_info
        else:
            delta_I = 0
        
        # 返回费舍尔信息量和信息冲击的组合
        return float(fisher_info + 0.5 * delta_I)
    
    def calculate_exponential_family_curvature(self, window: int = 30) -> float:
        """
        计算指数族流形曲率（Exponential Family Curvature）
        
        基于价格变化估计统计流形的曲率
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            指数族流形曲率值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 计算对数收益率
        log_returns = np.log(price_series[1:] / price_series[:-1])
        
        # 假设收益率服从指数族分布（这里使用正态分布作为近似）
        mu = np.mean(log_returns)
        sigma = np.std(log_returns)
        
        # 计算自然参数
        theta1 = mu / (sigma**2)
        theta2 = -1 / (2 * sigma**2)
        
        # 计算期望参数
        eta1 = mu
        eta2 = mu**2 + sigma**2
        
        # 计算度量张量
        g11 = 1 / (sigma**2)
        g12 = 0
        g21 = 0
        g22 = 1 / (2 * sigma**4)
        
        # 计算Christoffel符号
        Gamma111 = 0
        Gamma112 = -1 / (sigma**3)
        Gamma121 = -1 / (sigma**3)
        Gamma122 = 0
        Gamma211 = 0
        Gamma212 = 0
        Gamma221 = 0
        Gamma222 = -1 / (sigma**3)
        
        # 计算Riemann曲率张量
        R1212 = Gamma111 * Gamma221 + Gamma112 * Gamma222 - Gamma211 * Gamma121 - Gamma212 * Gamma122
        
        # 计算截面曲率
        K = R1212 / (g11 * g22 - g12 * g21)
        
        # 返回曲率绝对值
        return float(abs(K))

    @classmethod
    def from_akshare(cls, symbol: str, start_date: str, end_date: str = None, adjust: str = "qfq"):
        """
        从AKShare获取数据并创建NonlinearFactors实例
        
        Parameters
        ----------
        symbol : str
            股票代码，如"000001"
        start_date : str
            开始日期，格式为"YYYYMMDD"，如"20240101"
        end_date : str, optional
            结束日期，格式为"YYYYMMDD"，如"20240531"
            如果不提供，则使用当前日期
        adjust : str, optional
            复权方式，可选值为："qfq"（前复权）、"hfq"（后复权）、None（不复权）
            默认为"qfq"（前复权）
            
        Returns
        -------
        NonlinearFactors
            非线性动力学因子计算类实例
        """
        return cls(data=None, symbol=symbol, start_date=start_date, end_date=end_date, adjust=adjust)