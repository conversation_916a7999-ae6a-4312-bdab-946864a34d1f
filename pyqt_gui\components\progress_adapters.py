"""进度适配器模块，连接各个线程的进度信号到StatusManager"""

from pyqt_gui.components.status_manager import StatusManager

class ProgressAdapters:
    """进度回调适配器类，提供各种进度回调方法"""
    
    @staticmethod
    def on_init_progress(self, progress, message):
        """初始化进度回调"""
        StatusManager.update_status(self, message, "loading", progress)
        if hasattr(self, 'progress_dialog') and self.progress_dialog.isVisible():
            self.progress_dialog.set_progress(progress, message)
    
    @staticmethod
    def on_data_load_progress(self, progress, message):
        """数据加载进度回调"""
        StatusManager.update_status(self, message, "loading", progress)
        if hasattr(self, 'data_progress_dialog') and self.data_progress_dialog.isVisible():
            self.data_progress_dialog.set_progress(progress, message)
    
    @staticmethod
    def on_money_effect_progress(self, progress, message):
        """赚钱效应评分进度回调"""
        StatusManager.update_status(self, message, "loading", progress)
    
    @staticmethod
    def on_constituent_progress(self, progress, message):
        """成分股加载进度回调"""
        StatusManager.update_status(self, message, "loading", progress)
    
    @staticmethod
    def on_history_data_progress(self, progress, message):
        """历史数据加载进度回调"""
        StatusManager.update_status(self, message, "loading", progress)
    
    @staticmethod
    def on_stock_data_progress(self, progress, message):
        """股票数据加载进度回调"""
        StatusManager.update_status(self, message, "loading", progress)
    
    # 模型加载完成