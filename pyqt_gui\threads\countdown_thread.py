import time
from PyQt6.QtCore import QThread, pyqtSignal

class CountdownThread(QThread):
    """倒计时线程，用于自动刷新功能"""
    update_signal = pyqtSignal(int)
    finished_signal = pyqtSignal()
    
    def __init__(self, interval):
        super().__init__()
        self.interval = interval
        self.running = True
        
    def run(self):
        for i in range(self.interval, 0, -1):
            if not self.running:
                break
            self.update_signal.emit(i)
            time.sleep(1)
        self.finished_signal.emit()
        
    def stop(self):
        self.running = False 