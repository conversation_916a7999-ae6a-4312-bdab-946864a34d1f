#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
拓扑数据因子计算模块

实现基于拓扑数据分析的因子计算，包括：
1. 持续性同调（Persistent Homology）
2. 沃瑟斯坦距离（Wasserstein Distance）
3. 拓扑特征提取（Topological Feature Extraction）
4. 同伦不变量（Homotopy Invariants）
5. 拓扑熵（Topological Entropy）
6. 拓扑复杂度（Topological Complexity）
7. 拓扑稳定性（Topological Stability）
8. 拓扑相变检测（Topological Phase Transition Detection）
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from scipy import stats, signal, optimize
from scipy.spatial.distance import pdist, squareform
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import warnings
import akshare as ak  # 导入AKShare库
from utils.logging_config import get_logger

warnings.filterwarnings('ignore')

# 配置日志
logger = get_logger(__name__)

class TopologyFactors:
    """拓扑数据因子计算类"""
    
    def __init__(self, data: Optional[pd.DataFrame] = None, symbol: str = None, 
                 start_date: str = None, end_date: str = None, 
                 period: str = "daily", adjust: str = "qfq"):
        """
        初始化拓扑数据因子计算类
        
        Parameters
        ----------
        data : pd.DataFrame, optional
            包含OHLCV数据的DataFrame，必须包含以下列：
            - 收盘/close: 收盘价
            - 最高/high: 最高价
            - 最低/low: 最低价
            - 开盘/open: 开盘价
            - 成交量/volume: 成交量
            如果data为None，则需要提供symbol, start_date, end_date参数从AKShare获取数据
        symbol : str, optional
            股票代码，例如"000001"，用于从AKShare获取数据
        start_date : str, optional
            开始日期，格式为"YYYYMMDD"，例如"20210301"，用于从AKShare获取数据
        end_date : str, optional
            结束日期，格式为"YYYYMMDD"，例如"20210616"，用于从AKShare获取数据
        period : str, optional
            数据周期，可选值为"daily", "weekly", "monthly"，默认为"daily"
        adjust : str, optional
            复权方式，可选值为"qfq"(前复权), "hfq"(后复权)，默认为"qfq"
        """
        if data is None and (symbol is None or start_date is None or end_date is None):
            raise ValueError("必须提供data参数或者symbol, start_date, end_date参数")
            
        if data is None:
            # 从AKShare获取数据
            self.data = self.fetch_stock_data(symbol, start_date, end_date, period, adjust)
        else:
            self.data = data.copy()
            
        self._validate_data()
        
        # 预处理数据
        self._preprocess_data()
    
    @staticmethod
    def fetch_stock_data(symbol: str, start_date: str, end_date: str, 
                        period: str = "daily", adjust: str = "qfq") -> pd.DataFrame:
        """
        使用AKShare获取股票历史数据
        
        Parameters
        ----------
        symbol : str
            股票代码，例如"000001"
        start_date : str
            开始日期，格式为"YYYYMMDD"，例如"20210301"
        end_date : str
            结束日期，格式为"YYYYMMDD"，例如"20210616"
        period : str, optional
            数据周期，可选值为"daily", "weekly", "monthly"，默认为"daily"
        adjust : str, optional
            复权方式，可选值为"qfq"(前复权), "hfq"(后复权)，默认为"qfq"
            
        Returns
        -------
        pd.DataFrame
            股票历史数据
        """
        try:
            logger.info(f"从AKShare获取股票 {symbol} 的历史数据")
            stock_data = ak.stock_zh_a_hist(
                symbol=symbol, 
                period=period, 
                start_date=start_date, 
                end_date=end_date, 
                adjust=adjust
            )
            
            # 重命名列，确保列名符合类中使用的命名规范
            column_mapping = {
                "日期": "date",
                "开盘": "open",
                "收盘": "close",
                "最高": "high",
                "最低": "low",
                "成交量": "volume",
                "成交额": "amount",
                "振幅": "amplitude",
                "涨跌幅": "pct_change",
                "涨跌额": "price_change",
                "换手率": "turnover_rate"
            }
            
            # 仅保留需要的列
            stock_data = stock_data.rename(columns=column_mapping)
            
            # 将日期列转换为datetime类型
            stock_data['date'] = pd.to_datetime(stock_data['date'])
            
            # 将成交量单位从手转换为股
            stock_data['volume'] = stock_data['volume'] * 100
            
            # 确保按日期排序
            stock_data = stock_data.sort_values('date')
            
            logger.info(f"成功获取股票 {symbol} 的历史数据，共 {len(stock_data)} 条记录")
            return stock_data
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 的历史数据失败: {str(e)}")
            raise
        
    def _validate_data(self):
        """验证输入数据的完整性"""
        required_columns = ['close', 'high', 'low', 'open', 'volume']
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要的列: {missing_columns}")
            
    def _preprocess_data(self):
        """预处理数据"""
        # 确保数据按时间排序
        if 'date' in self.data.columns:
            self.data = self.data.sort_values('date')
        
        # 计算对数收益率
        self.data['log_return'] = np.log(self.data['close'] / self.data['close'].shift(1))
        
        # 计算波动率
        self.data['volatility'] = self.data['log_return'].rolling(window=20).std()
        
        # 标准化价格序列
        self.data['normalized_price'] = (self.data['close'] - self.data['close'].mean()) / self.data['close'].std()
        
    def calculate_all_factors(self, window: int = 30) -> pd.Series:
        """
        计算所有拓扑数据因子
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        pd.Series
            包含所有因子值的Series
        """
        factors = {}
        
        # 计算各类因子
        factors['persistent_homology'] = self.calculate_persistent_homology(window)
        factors['wasserstein_distance'] = self.calculate_wasserstein_distance(window)
        factors['topological_features'] = self.calculate_topological_features(window)
        factors['homotopy_invariants'] = self.calculate_homotopy_invariants(window)
        factors['topological_entropy'] = self.calculate_topological_entropy(window)
        factors['topological_complexity'] = self.calculate_topological_complexity(window)
        factors['topological_stability'] = self.calculate_topological_stability(window)
        factors['phase_transition_detection'] = self.calculate_phase_transition_detection(window)
        
        return pd.Series(factors)
    
    def calculate_persistent_homology(self, window: int = 30) -> float:
        """
        计算持续性同调（Persistent Homology）
        
        使用简化版本计算一维同调群
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            持续性同调值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 将价格序列转换为点云
        time_points = np.arange(len(price_series))
        point_cloud = np.column_stack((time_points, price_series))
        
        # 计算点之间的距离矩阵
        dist_matrix = squareform(pdist(point_cloud))
        
        # 简化版本的持续性同调计算
        # 使用距离阈值构建图
        threshold = np.mean(dist_matrix)  # 使用平均距离作为阈值
        
        # 计算边的数量（一维同调）
        edges = np.sum(dist_matrix < threshold) - len(price_series)
        
        # 计算连通分量数量（零维同调）
        components = self._count_components(dist_matrix, threshold)
        
        # 计算一维同调群的秩
        beta_1 = edges - len(price_series) + components
        
        # 归一化
        beta_1_norm = beta_1 / len(price_series)
        
        return float(beta_1_norm)
    
    def _count_components(self, dist_matrix: np.ndarray, threshold: float) -> int:
        """
        计算图中的连通分量数量
        
        Parameters
        ----------
        dist_matrix : np.ndarray
            距离矩阵
        threshold : float
            距离阈值
            
        Returns
        -------
        int
            连通分量数量
        """
        n = len(dist_matrix)
        visited = np.zeros(n, dtype=bool)
        components = 0
        
        for i in range(n):
            if not visited[i]:
                # 使用深度优先搜索遍历连通分量
                self._dfs(dist_matrix, i, visited, threshold)
                components += 1
                
        return components
    
    def _dfs(self, dist_matrix: np.ndarray, v: int, visited: np.ndarray, threshold: float):
        """
        深度优先搜索
        
        Parameters
        ----------
        dist_matrix : np.ndarray
            距离矩阵
        v : int
            当前顶点
        visited : np.ndarray
            访问标记数组
        threshold : float
            距离阈值
        """
        visited[v] = True
        
        for i in range(len(dist_matrix)):
            if not visited[i] and dist_matrix[v, i] < threshold:
                self._dfs(dist_matrix, i, visited, threshold)
    
    def calculate_wasserstein_distance(self, window: int = 30) -> float:
        """
        计算沃瑟斯坦距离（Wasserstein Distance）
        
        计算与典型K线形态的距离
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            沃瑟斯坦距离值
        """
        # 获取窗口内的OHLC数据
        ohlc_data = self.data[['open', 'high', 'low', 'close']].tail(window).values
        
        # 定义典型K线形态
        patterns = {
            'hammer': np.array([0.0, 0.2, -0.3, 0.1]),  # 锤子线
            'shooting_star': np.array([0.0, 0.3, -0.2, -0.1]),  # 流星线
            'engulfing_bullish': np.array([-0.1, 0.2, -0.3, 0.1]),  # 看涨吞没
            'engulfing_bearish': np.array([0.1, -0.2, 0.3, -0.1])  # 看跌吞没
        }
        
        # 计算当前K线形态
        current_pattern = np.zeros(4)
        for i in range(4):
            if i == 0:  # 开盘价变化
                current_pattern[i] = (ohlc_data[-1, 0] - ohlc_data[-2, 3]) / ohlc_data[-2, 3]
            elif i == 1:  # 最高价变化
                current_pattern[i] = (ohlc_data[-1, 1] - ohlc_data[-1, 0]) / ohlc_data[-1, 0]
            elif i == 2:  # 最低价变化
                current_pattern[i] = (ohlc_data[-1, 2] - ohlc_data[-1, 0]) / ohlc_data[-1, 0]
            elif i == 3:  # 收盘价变化
                current_pattern[i] = (ohlc_data[-1, 3] - ohlc_data[-1, 0]) / ohlc_data[-1, 0]
        
        # 计算与各典型形态的Wasserstein距离
        distances = {}
        for name, pattern in patterns.items():
            # 使用一维Wasserstein距离（简化版本）
            # 在实际应用中，可以使用更复杂的算法
            distances[name] = np.sum(np.abs(np.sort(current_pattern) - np.sort(pattern)))
        
        # 返回最小距离
        min_distance = min(distances.values())
        
        # 归一化
        max_possible_distance = 2.0  # 估计的最大可能距离
        normalized_distance = min_distance / max_possible_distance
        
        return float(normalized_distance)
    
    def calculate_topological_features(self, window: int = 30) -> float:
        """
        计算拓扑特征提取（Topological Feature Extraction）
        
        提取价格序列的拓扑特征
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            拓扑特征值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 计算价格序列的梯度
        gradient = np.gradient(price_series)
        
        # 计算梯度的符号变化
        sign_changes = np.sum(np.abs(np.diff(np.sign(gradient))))
        
        # 计算局部极值点数量
        local_extrema = 0
        for i in range(1, len(price_series)-1):
            if (price_series[i] > price_series[i-1] and price_series[i] > price_series[i+1]) or \
               (price_series[i] < price_series[i-1] and price_series[i] < price_series[i+1]):
                local_extrema += 1
        
        # 计算拓扑特征
        # 结合符号变化和局部极值点
        topological_feature = (sign_changes + local_extrema) / len(price_series)
        
        return float(topological_feature)
    
    def calculate_homotopy_invariants(self, window: int = 30) -> float:
        """
        计算同伦不变量（Homotopy Invariants）
        
        计算价格序列的同伦不变量
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            同伦不变量值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 计算价格序列的梯度
        gradient = np.gradient(price_series)
        
        # 计算梯度的积分
        integral = np.sum(np.abs(gradient))
        
        # 计算价格序列的路径长度
        path_length = 0
        for i in range(1, len(price_series)):
            path_length += np.abs(price_series[i] - price_series[i-1])
        
        # 计算同伦不变量
        # 使用路径长度与积分的比值
        homotopy_invariant = path_length / (integral + 1e-10)
        
        return float(homotopy_invariant)
    
    def calculate_topological_entropy(self, window: int = 30) -> float:
        """
        计算拓扑熵（Topological Entropy）
        
        计算价格序列的拓扑熵
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            拓扑熵值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 计算价格序列的梯度
        gradient = np.gradient(price_series)
        
        # 计算梯度的符号序列
        sign_sequence = np.sign(gradient)
        
        # 计算符号序列的熵
        unique_signs, counts = np.unique(sign_sequence, return_counts=True)
        probabilities = counts / len(sign_sequence)
        entropy = -np.sum(probabilities * np.log2(probabilities + 1e-10))
        
        # 归一化
        max_entropy = np.log2(3)  # 最大可能熵（三种符号：-1, 0, 1）
        normalized_entropy = entropy / max_entropy
        
        return float(normalized_entropy)
    
    def calculate_topological_complexity(self, window: int = 30) -> float:
        """
        计算拓扑复杂度（Topological Complexity）
        
        计算价格序列的拓扑复杂度
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            拓扑复杂度值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 计算价格序列的梯度
        gradient = np.gradient(price_series)
        
        # 计算梯度的符号变化
        sign_changes = np.sum(np.abs(np.diff(np.sign(gradient))))
        
        # 计算局部极值点数量
        local_extrema = 0
        for i in range(1, len(price_series)-1):
            if (price_series[i] > price_series[i-1] and price_series[i] > price_series[i+1]) or \
               (price_series[i] < price_series[i-1] and price_series[i] < price_series[i+1]):
                local_extrema += 1
        
        # 计算拓扑复杂度
        # 结合符号变化和局部极值点
        complexity = (sign_changes + local_extrema) / len(price_series)
        
        # 计算价格序列的波动性
        volatility = np.std(price_series) / np.mean(price_series)
        
        # 综合复杂度指标
        topological_complexity = complexity * (1 + volatility)
        
        return float(topological_complexity)
    
    def calculate_topological_stability(self, window: int = 30) -> float:
        """
        计算拓扑稳定性（Topological Stability）
        
        计算价格序列的拓扑稳定性
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            拓扑稳定性值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 计算价格序列的梯度
        gradient = np.gradient(price_series)
        
        # 计算梯度的稳定性
        gradient_stability = 1 / (1 + np.std(gradient))
        
        # 计算价格序列的稳定性
        price_stability = 1 / (1 + np.std(price_series) / np.mean(price_series))
        
        # 计算拓扑稳定性
        # 结合梯度稳定性和价格稳定性
        stability = (gradient_stability + price_stability) / 2
        
        return float(stability)
    
    def calculate_phase_transition_detection(self, window: int = 30) -> float:
        """
        计算拓扑相变检测（Topological Phase Transition Detection）
        
        检测价格序列的拓扑相变
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小（天），默认为30天
            
        Returns
        -------
        float
            拓扑相变检测值
        """
        # 获取窗口内的价格序列
        price_series = self.data['close'].tail(window).values
        
        # 将窗口分为前半部分和后半部分
        mid_point = len(price_series) // 2
        first_half = price_series[:mid_point]
        second_half = price_series[mid_point:]
        
        # 计算前半部分的拓扑特征
        first_gradient = np.gradient(first_half)
        first_sign_changes = np.sum(np.abs(np.diff(np.sign(first_gradient))))
        first_local_extrema = 0
        for i in range(1, len(first_half)-1):
            if (first_half[i] > first_half[i-1] and first_half[i] > first_half[i+1]) or \
               (first_half[i] < first_half[i-1] and first_half[i] < first_half[i+1]):
                first_local_extrema += 1
        first_complexity = (first_sign_changes + first_local_extrema) / len(first_half)
        
        # 计算后半部分的拓扑特征
        second_gradient = np.gradient(second_half)
        second_sign_changes = np.sum(np.abs(np.diff(np.sign(second_gradient))))
        second_local_extrema = 0
        for i in range(1, len(second_half)-1):
            if (second_half[i] > second_half[i-1] and second_half[i] > second_half[i+1]) or \
               (second_half[i] < second_half[i-1] and second_half[i] < second_half[i+1]):
                second_local_extrema += 1
        second_complexity = (second_sign_changes + second_local_extrema) / len(second_half)
        
        # 计算拓扑相变检测值
        # 使用前后半部分复杂度的差异
        phase_transition = np.abs(second_complexity - first_complexity)
        
        return float(phase_transition)