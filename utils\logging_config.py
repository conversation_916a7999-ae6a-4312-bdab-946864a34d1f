#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
企业级日志配置模块

为整个应用提供统一、高性能、可靠的日志管理系统
功能特性：
- 单例模式确保配置唯一性
- 自动日志轮转和清理
- 性能监控和错误追踪
- 分层日志管理
- 异常恢复机制
"""

import os
import sys
import logging
import threading
import time
import gzip
import shutil
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from pathlib import Path
from typing import Optional, Dict, Any, List


class LoggingManager:
    """
    企业级日志管理器 - 单例模式
    
    提供统一的日志配置、管理和监控功能
    """
    
    _instance = None
    _lock = threading.Lock()
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(LoggingManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        with self._lock:
            if self._initialized:
                return
                
            # 初始化配置
            self.log_dir = Path("logs")
            self.log_dir.mkdir(exist_ok=True)
            
            # 日志配置
            self.log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            self.date_format = '%Y-%m-%d %H:%M:%S'
            
            # 性能监控
            self.start_time = time.time()
            self.log_count = 0
            self.error_count = 0
            
            # 已配置的logger缓存
            self._configured_loggers: Dict[str, logging.Logger] = {}
            
            # 清理旧的handlers
            self._cleanup_existing_handlers()
            
            # 配置根日志器
            self._configure_root_logger()
            
            # 启动后台清理任务
            self._start_cleanup_thread()
            
            self._initialized = True
    
    def _cleanup_existing_handlers(self):
        """
        清理现有的日志处理器，避免重复配置
        """
        try:
            # 清理根日志器的所有处理器
            root_logger = logging.getLogger()
            for handler in root_logger.handlers[:]:
                try:
                    handler.close()
                    root_logger.removeHandler(handler)
                except Exception as e:
                    print(f"清理处理器时出错: {e}")
            
            # 重置日志级别
            root_logger.setLevel(logging.NOTSET)
            
        except Exception as e:
            print(f"清理现有处理器时出错: {e}")
    
    def _configure_root_logger(self):
        """
        配置根日志器
        """
        try:
            root_logger = logging.getLogger()
            root_logger.setLevel(logging.DEBUG)
            
            # 创建格式化器
            formatter = logging.Formatter(
                fmt=self.log_format,
                datefmt=self.date_format
            )
            
            # 配置文件处理器 - 使用轮转日志
            log_file = self.log_dir / f"app_{datetime.now().strftime('%Y%m%d')}.log"
            file_handler = RotatingFileHandler(
                filename=str(log_file),
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            
            # 配置控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.DEBUG)  # 修改为DEBUG级别以显示更多信息
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)
            
            # 配置第三方库日志级别
            self._configure_third_party_loggers()
            
        except Exception as e:
            print(f"配置根日志器时出错: {e}")
            # 使用基本配置作为后备
            logging.basicConfig(
                level=logging.INFO,
                format=self.log_format,
                datefmt=self.date_format
            )
    
    def _configure_third_party_loggers(self):
        """
        配置第三方库的日志级别
        """
        third_party_loggers = [
            'matplotlib', 'urllib3', 'requests', 'chardet',
            'torch', 'tensorflow', 'sklearn', 'numba',
            'akshare', 'pandas', 'numpy', 'PIL',
            'selenium', 'webdriver', 'asyncio'
        ]
        
        for logger_name in third_party_loggers:
            try:
                logger = logging.getLogger(logger_name)
                logger.setLevel(logging.WARNING)
            except Exception as e:
                print(f"配置第三方库日志器 {logger_name} 时出错: {e}")
    
    def get_logger(self, name: Optional[str] = None, level: int = logging.DEBUG) -> logging.Logger:
        """
        获取配置好的logger实例
        
        Args:
            name: logger名称，通常使用 __name__
            level: 日志级别
            
        Returns:
            配置好的logger实例
        """
        try:
            if name is None:
                name = 'root'
            
            # 检查缓存
            if name in self._configured_loggers:
                return self._configured_loggers[name]
            
            # 创建新的logger
            logger = logging.getLogger(name)
            logger.setLevel(level)
            
            # 缓存logger
            self._configured_loggers[name] = logger
            
            # 更新统计
            self.log_count += 1
            
            return logger
            
        except Exception as e:
            print(f"获取logger时出错: {e}")
            # 返回基本logger作为后备
            return logging.getLogger(name or 'fallback')
    
    def _start_cleanup_thread(self):
        """
        启动后台清理线程
        """
        def cleanup_worker():
            while True:
                try:
                    time.sleep(3600)  # 每小时执行一次
                    self._cleanup_old_logs()
                    self._compress_old_logs()
                except Exception as e:
                    print(f"日志清理线程出错: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def _cleanup_old_logs(self, days_to_keep: int = 7):
        """
        清理过期的日志文件
        
        Args:
            days_to_keep: 保留天数
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            for log_file in self.log_dir.glob("*.log*"):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    print(f"已删除过期日志文件: {log_file}")
                    
        except Exception as e:
            print(f"清理过期日志时出错: {e}")
    
    def _compress_old_logs(self):
        """
        压缩旧的日志文件
        """
        try:
            yesterday = datetime.now() - timedelta(days=1)
            pattern = f"app_{yesterday.strftime('%Y%m%d')}.log*"
            
            for log_file in self.log_dir.glob(pattern):
                if not log_file.name.endswith('.gz'):
                    compressed_file = log_file.with_suffix(log_file.suffix + '.gz')
                    
                    with open(log_file, 'rb') as f_in:
                        with gzip.open(compressed_file, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    
                    log_file.unlink()
                    print(f"已压缩日志文件: {log_file} -> {compressed_file}")
                    
        except Exception as e:
            print(f"压缩日志文件时出错: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取日志系统统计信息
        
        Returns:
            统计信息字典
        """
        try:
            uptime = time.time() - self.start_time
            return {
                'uptime_seconds': uptime,
                'uptime_hours': uptime / 3600,
                'total_loggers': len(self._configured_loggers),
                'log_count': self.log_count,
                'error_count': self.error_count,
                'log_directory': str(self.log_dir),
                'log_files': [f.name for f in self.log_dir.glob("*.log*")]
            }
        except Exception as e:
            return {'error': str(e)}
    
    def force_cleanup(self):
        """
        强制执行日志清理
        """
        try:
            self._cleanup_old_logs()
            self._compress_old_logs()
            print("强制日志清理完成")
        except Exception as e:
            print(f"强制清理时出错: {e}")


# 全局日志管理器实例
_logging_manager = None
_manager_lock = threading.Lock()


def get_logging_manager() -> LoggingManager:
    """
    获取全局日志管理器实例
    
    Returns:
        LoggingManager实例
    """
    global _logging_manager
    
    if _logging_manager is None:
        with _manager_lock:
            if _logging_manager is None:
                _logging_manager = LoggingManager()
    
    return _logging_manager


def get_logger(name: Optional[str] = None, level: int = logging.DEBUG) -> logging.Logger:
    """
    获取配置好的logger实例 - 统一入口函数
    
    Args:
        name: logger名称，通常使用 __name__
        level: 日志级别
        
    Returns:
        配置好的logger实例
    """
    try:
        manager = get_logging_manager()
        return manager.get_logger(name, level)
    except Exception as e:
        print(f"获取logger时出错: {e}")
        # 后备方案
        logger = logging.getLogger(name or 'fallback')
        logger.setLevel(level)
        return logger


def configure_logging(level=logging.DEBUG, log_file=None, module_name=None, third_party_level=logging.WARNING):
    """
    配置日志系统 - 兼容性函数
    
    为了保持向后兼容性而保留的函数
    
    Args:
        level: 日志级别
        log_file: 日志文件路径（已废弃，由管理器自动处理）
        module_name: 模块名称
        third_party_level: 第三方库日志级别（已废弃，由管理器自动处理）
        
    Returns:
        logger实例
    """
    return get_logger(module_name, level)


def get_logging_stats() -> Dict[str, Any]:
    """
    获取日志系统统计信息
    
    Returns:
        统计信息字典
    """
    try:
        manager = get_logging_manager()
        return manager.get_stats()
    except Exception as e:
        return {'error': str(e)}


def force_log_cleanup():
    """
    强制执行日志清理
    """
    try:
        manager = get_logging_manager()
        manager.force_cleanup()
    except Exception as e:
        print(f"强制清理时出错: {e}")


# 模块初始化时自动创建管理器
try:
    _logging_manager = get_logging_manager()
except Exception as e:
    print(f"初始化日志管理器时出错: {e}")