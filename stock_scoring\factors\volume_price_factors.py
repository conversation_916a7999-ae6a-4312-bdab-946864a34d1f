#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
量价关系因子模块

基于"焚诀"选股公式的量价关系理论，实现适用于1-3天短线持股的因子计算。
核心理念："缩量买，爆量卖，连续爆量清仓"以及"分歧"与"抗分歧"的量价关系。

主要因子：
1. 抗分歧强度评分 (DRS - Divergence Resistance Score)
2. 空方占优风险评分 (BDR - Bear Dominance Risk)
3. 最终量价关系总分 (FPVS - Final Price-Volume Score)

作者: LilyBullRider团队
日期: 2025-01-20
版本: 1.0.0
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime, timedelta
from utils.logging_config import get_logger

# 获取配置好的logger实例
logger = get_logger(__name__)


class VolumePriceFactors:
    """量价关系因子计算模块"""
    
    def __init__(self, data: pd.DataFrame):
        """
        初始化量价关系因子计算器
        
        Parameters
        ----------
        data : pd.DataFrame
            包含OHLCV数据的DataFrame，必须包含以下列：
            - date: 日期
            - open: 开盘价
            - high: 最高价
            - low: 最低价
            - close: 收盘价
            - volume: 成交量
            - amount: 成交额（可选）
        """
        self.data = data.copy()
        self._validate_data()
        self._prepare_data()
        
    def _validate_data(self):
        """验证数据完整性"""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要的列: {missing_columns}")
            
        if len(self.data) < 10:
            raise ValueError("数据长度不足，至少需要10个交易日的数据")
            
    def _prepare_data(self):
        """预处理数据"""
        # 确保数据按日期排序
        if 'date' in self.data.columns:
            self.data = self.data.sort_values('date')
        else:
            self.data = self.data.sort_index()
            
        # 计算基础指标
        self.data['price_change'] = self.data['close'].pct_change()
        self.data['volume_ma5'] = self.data['volume'].rolling(window=5).mean()
        self.data['volume_ma10'] = self.data['volume'].rolling(window=10).mean()
        self.data['price_ma5'] = self.data['close'].rolling(window=5).mean()
        
    def calculate_all_factors(self, window: int = 30) -> Dict[str, pd.Series]:
        """
        计算所有量价关系因子
        
        Returns
        -------
        Dict[str, pd.Series]
            包含所有量价关系因子的字典
        """
        factors = {}
        
        # 核心量价关系因子
        factors['volume_ratio_1d'] = self.volume_ratio_factor(period=1)
        factors['volume_ratio_3d'] = self.volume_ratio_factor(period=3)
        factors['volume_ratio_5d'] = self.volume_ratio_factor(period=5)
        
        factors['price_momentum_1d'] = self.price_momentum_factor(period=1)
        factors['price_momentum_3d'] = self.price_momentum_factor(period=3)
        
        factors['trend_consistency_3d'] = self.trend_consistency_factor(period=3)
        factors['trend_consistency_5d'] = self.trend_consistency_factor(period=5)
        
        # 核心评分因子
        factors['drs_score'] = self.calculate_drs_score()
        factors['bdr_score'] = self.calculate_bdr_score()
        factors['fpvs_score'] = self.calculate_fpvs_score()
        
        # 辅助因子
        factors['volume_burst_signal'] = self.volume_burst_signal()
        factors['volume_shrink_signal'] = self.volume_shrink_signal()
        factors['price_volume_divergence'] = self.price_volume_divergence()
        
        return factors
        
    def volume_ratio_factor(self, period: int = 1) -> pd.Series:
        """
        成交量比率因子
        
        Parameters
        ----------
        period : int
            计算周期，默认为1天
            
        Returns
        -------
        pd.Series
            成交量比率因子
        """
        volume = self.data['volume']
        volume_ma = volume.rolling(window=10).mean()
        
        if period == 1:
            ratio = volume / volume_ma
        else:
            # 多日平均成交量比率
            volume_period = volume.rolling(window=period).mean()
            ratio = volume_period / volume_ma
            
        return ratio.fillna(1.0)
        
    def price_momentum_factor(self, period: int = 1) -> pd.Series:
        """
        价格动量因子
        
        Parameters
        ----------
        period : int
            计算周期，默认为1天
            
        Returns
        -------
        pd.Series
            价格动量因子
        """
        close = self.data['close']
        
        if period == 1:
            momentum = close.pct_change()
        else:
            momentum = close.pct_change(periods=period)
            
        return momentum.fillna(0.0)
        
    def trend_consistency_factor(self, period: int = 3) -> pd.Series:
        """
        趋势一致性因子
        
        Parameters
        ----------
        period : int
            计算周期，默认为3天
            
        Returns
        -------
        pd.Series
            趋势一致性因子
        """
        price_change = self.data['price_change']
        
        # 计算趋势一致性
        consistency = price_change.rolling(window=period).apply(
            lambda x: len(x[x > 0]) / len(x) if len(x) > 0 else 0.5
        )
        
        return consistency.fillna(0.5)
        
    def calculate_drs_score(self) -> pd.Series:
        """
        计算抗分歧强度评分 (DRS)
        
        基于"焚诀"理论中的"抗分歧"概念：
        - 价格上涨 + 成交量适度放大 = 抗分歧强度高
        - 价格上涨 + 成交量过度放大 = 可能存在分歧
        - 价格下跌 + 成交量放大 = 分歧明显
        
        Returns
        -------
        pd.Series
            抗分歧强度评分 (0-100)
        """
        # 获取基础数据
        volume_ratio_1d = self.volume_ratio_factor(period=1)
        volume_ratio_3d = self.volume_ratio_factor(period=3)
        price_momentum_1d = self.price_momentum_factor(period=1)
        price_momentum_3d = self.price_momentum_factor(period=3)
        trend_consistency_3d = self.trend_consistency_factor(period=3)
        
        # 初始化DRS评分
        drs_score = pd.Series(50.0, index=self.data.index)
        
        for i in range(len(self.data)):
            if i < 5:  # 前5个交易日数据不足，使用默认值
                continue
                
            # 当日指标
            vol_ratio_1 = volume_ratio_1d.iloc[i]
            vol_ratio_3 = volume_ratio_3d.iloc[i]
            price_mom_1 = price_momentum_1d.iloc[i]
            price_mom_3 = price_momentum_3d.iloc[i]
            trend_cons = trend_consistency_3d.iloc[i]
            
            # 基础分数
            score = 50.0
            
            # 价格动量评分 (权重40%)
            if price_mom_1 > 0.02:  # 单日涨幅超过2%
                score += 20
            elif price_mom_1 > 0:
                score += price_mom_1 * 500  # 正向动量加分
            else:
                score += price_mom_1 * 300  # 负向动量减分
                
            # 3日价格动量评分 (权重20%)
            if price_mom_3 > 0:
                score += price_mom_3 * 200
            else:
                score += price_mom_3 * 150
                
            # 成交量适度性评分 (权重25%)
            if 1.2 <= vol_ratio_1 <= 2.5:  # 适度放量
                score += 15
            elif vol_ratio_1 > 3.0:  # 过度放量，可能存在分歧
                score -= (vol_ratio_1 - 3.0) * 5
            elif vol_ratio_1 < 0.8:  # 缩量
                if price_mom_1 > 0:  # 缩量上涨，抗分歧强
                    score += 10
                else:  # 缩量下跌，弱势
                    score -= 5
                    
            # 趋势一致性评分 (权重15%)
            if trend_cons > 0.7:  # 趋势一致性强
                score += 10
            elif trend_cons < 0.3:  # 趋势混乱
                score -= 10
                
            # 确保评分在0-100范围内
            drs_score.iloc[i] = max(0, min(100, score))
            
        return drs_score
        
    def calculate_bdr_score(self) -> pd.Series:
        """
        计算空方占优风险评分 (BDR)
        
        基于"焚诀"理论中的"爆量卖"和"连续爆量清仓"概念：
        - 连续放量下跌 = 空方占优风险高
        - 单日爆量下跌 = 短期风险高
        - 缩量下跌 = 风险相对较低
        
        Returns
        -------
        pd.Series
            空方占优风险评分 (0-100，分数越高风险越大)
        """
        # 获取基础数据
        volume_ratio_1d = self.volume_ratio_factor(period=1)
        price_momentum_1d = self.price_momentum_factor(period=1)
        
        # 初始化BDR评分
        bdr_score = pd.Series(20.0, index=self.data.index)
        
        for i in range(len(self.data)):
            if i < 3:  # 前3个交易日数据不足，使用默认值
                continue
                
            # 当日及前几日指标
            vol_ratio_today = volume_ratio_1d.iloc[i]
            price_mom_today = price_momentum_1d.iloc[i]
            
            # 连续3日的价格和成交量变化
            vol_ratios_3d = volume_ratio_1d.iloc[i-2:i+1]
            price_moms_3d = price_momentum_1d.iloc[i-2:i+1]
            
            # 基础风险分数
            risk_score = 20.0
            
            # 当日风险评估 (权重50%)
            if price_mom_today < -0.03:  # 单日跌幅超过3%
                if vol_ratio_today > 2.0:  # 放量大跌
                    risk_score += 40
                elif vol_ratio_today > 1.5:  # 适度放量下跌
                    risk_score += 25
                else:  # 缩量下跌
                    risk_score += 10
            elif price_mom_today < 0:  # 一般下跌
                if vol_ratio_today > 2.0:  # 放量下跌
                    risk_score += 20
                else:
                    risk_score += abs(price_mom_today) * 500
                    
            # 连续性风险评估 (权重30%)
            negative_days = sum(1 for x in price_moms_3d if x < 0)
            high_volume_days = sum(1 for x in vol_ratios_3d if x > 1.5)
            
            if negative_days >= 2 and high_volume_days >= 2:  # 连续放量下跌
                risk_score += 25
            elif negative_days == 3:  # 连续3日下跌
                risk_score += 15
                
            # 爆量风险评估 (权重20%)
            max_vol_ratio = max(vol_ratios_3d)
            if max_vol_ratio > 4.0:  # 出现爆量
                corresponding_price_change = price_moms_3d.iloc[vol_ratios_3d.argmax()]
                if corresponding_price_change < 0:  # 爆量下跌
                    risk_score += 20
                    
            # 确保评分在0-100范围内
            bdr_score.iloc[i] = max(0, min(100, risk_score))
            
        return bdr_score
        
    def calculate_fpvs_score(self) -> pd.Series:
        """
        计算最终量价关系总分 (FPVS)
        
        综合DRS和BDR评分，形成最终的量价关系评分：
        FPVS = DRS * 0.6 - BDR * 0.4 + 调整因子
        
        Returns
        -------
        pd.Series
            最终量价关系总分 (0-100)
        """
        drs_score = self.calculate_drs_score()
        bdr_score = self.calculate_bdr_score()
        
        # 计算基础FPVS评分
        fpvs_base = drs_score * 0.6 - bdr_score * 0.4 + 40
        
        # 添加调整因子
        volume_burst = self.volume_burst_signal()
        volume_shrink = self.volume_shrink_signal()
        
        # 调整因子计算
        adjustment = pd.Series(0.0, index=self.data.index)
        
        for i in range(len(self.data)):
            if i < 5:
                continue
                
            adj = 0.0
            
            # 缩量上涨调整 ("缩量买")
            if volume_shrink.iloc[i] > 0.5 and self.data['price_change'].iloc[i] > 0:
                adj += 10
                
            # 爆量下跌调整 ("爆量卖")
            if volume_burst.iloc[i] > 0.5 and self.data['price_change'].iloc[i] < -0.02:
                adj -= 15
                
            # 连续爆量调整 ("连续爆量清仓")
            if i >= 2:
                recent_bursts = volume_burst.iloc[i-2:i+1].sum()
                if recent_bursts >= 2:
                    adj -= 20
                    
            adjustment.iloc[i] = adj
            
        # 计算最终FPVS评分
        fpvs_score = fpvs_base + adjustment
        
        # 确保评分在0-100范围内
        fpvs_score = fpvs_score.clip(0, 100)
        
        return fpvs_score
        
    def volume_burst_signal(self) -> pd.Series:
        """
        成交量爆发信号
        
        Returns
        -------
        pd.Series
            成交量爆发信号 (0-1)
        """
        volume_ratio = self.volume_ratio_factor(period=1)
        
        # 爆量阈值：成交量超过10日均量的3倍
        burst_signal = (volume_ratio > 3.0).astype(float)
        
        return burst_signal
        
    def volume_shrink_signal(self) -> pd.Series:
        """
        成交量萎缩信号
        
        Returns
        -------
        pd.Series
            成交量萎缩信号 (0-1)
        """
        volume_ratio = self.volume_ratio_factor(period=1)
        
        # 缩量阈值：成交量低于10日均量的70%
        shrink_signal = (volume_ratio < 0.7).astype(float)
        
        return shrink_signal
        
    def price_volume_divergence(self) -> pd.Series:
        """
        价量背离信号
        
        Returns
        -------
        pd.Series
            价量背离信号 (-1到1，负值表示负背离，正值表示正背离)
        """
        price_change = self.data['price_change']
        volume_ratio = self.volume_ratio_factor(period=1)
        
        # 计算价量背离
        divergence = pd.Series(0.0, index=self.data.index)
        
        for i in range(len(self.data)):
            if i < 1:
                continue
                
            price_dir = 1 if price_change.iloc[i] > 0 else -1
            volume_dir = 1 if volume_ratio.iloc[i] > 1 else -1
            
            # 背离程度
            if price_dir != volume_dir:
                divergence.iloc[i] = price_dir * abs(price_change.iloc[i]) * 10
            else:
                divergence.iloc[i] = 0
                
        return divergence.clip(-1, 1)
        
    def get_latest_signals(self) -> Dict[str, float]:
        """
        获取最新的量价关系信号
        
        Returns
        -------
        Dict[str, float]
            最新的量价关系信号字典
        """
        factors = self.calculate_all_factors()
        
        latest_signals = {}
        for name, series in factors.items():
            latest_signals[name] = series.iloc[-1] if not series.empty else 0.0
            
        return latest_signals
        
    def get_short_term_recommendation(self) -> Dict[str, Union[str, float]]:
        """
        获取短线操作建议
        
        Returns
        -------
        Dict[str, Union[str, float]]
            短线操作建议
        """
        latest_signals = self.get_latest_signals()
        
        fpvs_score = latest_signals.get('fpvs_score', 50)
        drs_score = latest_signals.get('drs_score', 50)
        bdr_score = latest_signals.get('bdr_score', 50)
        
        # 操作建议逻辑
        if fpvs_score >= 75 and drs_score >= 70 and bdr_score <= 30:
            recommendation = "强烈买入"
            confidence = 0.9
        elif fpvs_score >= 65 and drs_score >= 60 and bdr_score <= 40:
            recommendation = "买入"
            confidence = 0.7
        elif fpvs_score <= 25 or bdr_score >= 70:
            recommendation = "卖出"
            confidence = 0.8
        elif fpvs_score <= 35 or bdr_score >= 60:
            recommendation = "减仓"
            confidence = 0.6
        else:
            recommendation = "观望"
            confidence = 0.5
            
        return {
            'recommendation': recommendation,
            'confidence': confidence,
            'fpvs_score': fpvs_score,
            'drs_score': drs_score,
            'bdr_score': bdr_score
        }