#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文本编码处理工具

提供处理文本编码问题的工具函数，确保文本在不同环境下的一致性
"""

import re
from utils.logging_config import get_logger

# 配置日志
logger = get_logger(__name__)

def ensure_utf8(text):
    """确保文本是有效的UTF-8编码
    
    Args:
        text: 输入文本
        
    Returns:
        str: 编码正确的UTF-8文本
    """
    if text is None:
        return ""
        
    # 如果是字符串类型，尝试确保它是有效的UTF-8
    if isinstance(text, str):
        try:
            # 尝试编码然后解码来确保UTF-8有效性
            return text.encode('utf-8', errors='replace').decode('utf-8')
        except Exception:
            # 如果出现异常，尝试其他编码
            try:
                # 尝试GBK编码
                return text.encode('gbk', errors='replace').decode('utf-8', errors='replace')
            except Exception:
                # 最后尝试直接替换所有非ASCII字符
                return re.sub(r'[^\x00-\x7F]+', '?', str(text))
    
    # 如果是bytes类型，尝试不同的编码方式解码
    if isinstance(text, bytes):
        try:
            # 尝试UTF-8解码
            return text.decode('utf-8', errors='replace')
        except UnicodeDecodeError:
            try:
                # 尝试GBK解码
                return text.decode('gbk', errors='replace')
            except UnicodeDecodeError:
                try:
                    # 尝试GB18030解码
                    return text.decode('gb18030', errors='replace')
                except UnicodeDecodeError:
                    # 失败时使用errors='replace'替换不可解码的字符
                    logger.warning("文本解码失败，使用替换模式")
                    return text.decode('utf-8', errors='replace')
    
    # 其他类型，转为字符串
    return str(text)

def safe_log_text(text, max_length=50):
    """安全地记录文本，避免编码问题
    
    Args:
        text: 要记录的文本
        max_length: 最大长度，超过则截断
        
    Returns:
        str: 安全处理后的文本
    """
    if not text:
        return ""
        
    # 确保文本是UTF-8编码
    safe_text = ensure_utf8(text)
    
    # 截断长文本
    if len(safe_text) > max_length:
        return safe_text[:max_length] + "..."
        
    return safe_text

def fix_file_encoding(file_path, source_encoding=None, target_encoding='utf-8'):
    """修复文件编码
    
    Args:
        file_path: 文件路径
        source_encoding: 源编码，如果为None则自动检测
        target_encoding: 目标编码，默认为utf-8
        
    Returns:
        bool: 是否成功修复
    """
    try:
        # 如果未指定源编码，尝试自动检测
        if source_encoding is None:
            import chardet
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)
                source_encoding = result['encoding']
                logger.info(f"检测到文件编码: {source_encoding}")
        
        # 读取文件内容
        with open(file_path, 'r', encoding=source_encoding, errors='replace') as f:
            content = f.read()
        
        # 重新以目标编码写入
        with open(file_path, 'w', encoding=target_encoding) as f:
            f.write(content)
            
        logger.info(f"已将文件 {file_path} 的编码从 {source_encoding} 转换为 {target_encoding}")
        return True
    except Exception as e:
        logger.error(f"修复文件编码失败: {e}")
        return False
