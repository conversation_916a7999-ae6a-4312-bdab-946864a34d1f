# -*- coding: utf-8 -*-
"""
高级资金轮动强度计算模块
实现多维度、高精度的资金轮动强度分析
"""

import math
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
from utils.logging_config import get_logger

class AdvancedFundRotationCalculator:
    """
    高级资金轮动强度计算器
    基于多维度分析的资金轮动强度评估模型
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        # 历史数据缓存，用于计算持续性指标
        self.history_cache = []
        self.max_history_days = 5  # 保留5天历史数据
        
        # 市场状态权重配置
        self.market_weights = {
            'bull': {'direction': 0.25, 'liquidity': 0.20, 'persistence': 0.25, 'divergence': 0.15, 'participation': 0.15},
            'bear': {'direction': 0.20, 'liquidity': 0.15, 'persistence': 0.20, 'divergence': 0.25, 'participation': 0.20},
            'sideways': {'direction': 0.22, 'liquidity': 0.18, 'persistence': 0.22, 'divergence': 0.20, 'participation': 0.18}
        }
    
    def calculate_advanced_fund_rotation_strength(
        self, 
        market_data: Dict,
        historical_data: Optional[List[Dict]] = None
    ) -> Dict:
        """
        计算高级资金轮动强度
        
        Args:
            market_data: 当前市场数据
            historical_data: 历史数据（可选）
            
        Returns:
            包含详细分析结果的字典
        """
        try:
            # 提取基础数据
            up_stocks = market_data.get('up_stocks', 0)
            down_stocks = market_data.get('down_stocks', 0)
            flat_stocks = market_data.get('flat_stocks', 0)
            limit_up_real = market_data.get('limit_up_real', 0)
            limit_up_oneway = market_data.get('limit_up_oneway', 0)
            limit_down_real = market_data.get('limit_down_real', 0)
            limit_down_oneway = market_data.get('limit_down_oneway', 0)
            activity = market_data.get('activity', 50)
            volume_ratio = market_data.get('volume_ratio', 1.0)  # 成交量比率
            turnover_rate = market_data.get('turnover_rate', 0.02)  # 换手率
            
            total_stocks = up_stocks + down_stocks + flat_stocks
            limit_up_total = limit_up_real + limit_up_oneway
            limit_down_total = limit_down_real + limit_down_oneway
            
            # 1. 方向性强度计算
            direction_strength = self._calculate_direction_strength(
                up_stocks, down_stocks, flat_stocks,
                limit_up_real, limit_up_oneway,
                limit_down_real, limit_down_oneway
            )
            
            # 2. 流动性强度计算
            liquidity_strength = self._calculate_liquidity_strength(
                activity, volume_ratio, turnover_rate,
                limit_up_total, limit_down_total, total_stocks
            )
            
            # 3. 持续性强度计算
            persistence_strength = self._calculate_persistence_strength(
                market_data, historical_data
            )
            
            # 4. 分化强度计算
            divergence_strength = self._calculate_divergence_strength(
                up_stocks, down_stocks, flat_stocks,
                limit_up_real, limit_up_oneway,
                limit_down_real, limit_down_oneway
            )
            
            # 5. 参与度强度计算
            participation_strength = self._calculate_participation_strength(
                total_stocks, flat_stocks, activity, turnover_rate
            )
            
            # 6. 市场状态识别和权重动态调整
            market_state = self._identify_market_state(market_data)
            weights = self.market_weights.get(market_state, self.market_weights['sideways'])
            
            # 7. 综合计算资金轮动强度
            fund_rotation_strength = (
                direction_strength * weights['direction'] +
                liquidity_strength * weights['liquidity'] +
                persistence_strength * weights['persistence'] +
                divergence_strength * weights['divergence'] +
                participation_strength * weights['participation']
            )
            
            # 8. 应用非线性调整和阈值效应
            fund_rotation_strength = self._apply_nonlinear_adjustment(fund_rotation_strength)
            
            # 9. 时间衰减调整
            fund_rotation_strength = self._apply_time_decay(fund_rotation_strength)
            
            # 确保结果在0-100范围内
            fund_rotation_strength = max(0, min(100, fund_rotation_strength))
            
            # 构建详细结果
            result = {
                'fund_rotation_strength': round(fund_rotation_strength, 2),
                'market_state': market_state,
                'components': {
                    'direction_strength': round(direction_strength, 2),
                    'liquidity_strength': round(liquidity_strength, 2),
                    'persistence_strength': round(persistence_strength, 2),
                    'divergence_strength': round(divergence_strength, 2),
                    'participation_strength': round(participation_strength, 2)
                },
                'weights': weights,
                'confidence_level': self._calculate_confidence_level(market_data),
                'risk_indicators': {
                    'volatility_risk': self._calculate_volatility_risk(market_data),
                    'liquidity_risk': self._calculate_liquidity_risk(market_data),
                    'concentration_risk': self._calculate_concentration_risk(market_data)
                }
            }
            
            # 更新历史缓存
            self._update_history_cache(market_data, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"计算高级资金轮动强度失败: {str(e)}")
            return {
                'fund_rotation_strength': 50.0,
                'market_state': 'unknown',
                'error': str(e)
            }
    
    def _calculate_direction_strength(self, up_stocks, down_stocks, flat_stocks,
                                    limit_up_real, limit_up_oneway,
                                    limit_down_real, limit_down_oneway) -> float:
        """
        计算方向性强度
        考虑涨跌比例、涨停跌停质量、市值分布等因素
        """
        total_stocks = up_stocks + down_stocks + flat_stocks
        if total_stocks == 0:
            return 50.0
        
        # 基础方向性：涨跌比例
        up_ratio = up_stocks / total_stocks
        down_ratio = down_stocks / total_stocks
        base_direction = (up_ratio - down_ratio + 1) * 50  # 转换为0-100
        
        # 涨停跌停质量调整
        limit_up_total = limit_up_real + limit_up_oneway
        limit_down_total = limit_down_real + limit_down_oneway
        
        if limit_up_total + limit_down_total > 0:
            # 真实涨停权重更高
            quality_weighted_up = limit_up_real * 1.5 + limit_up_oneway * 0.8
            quality_weighted_down = limit_down_real * 1.5 + limit_down_oneway * 0.8
            
            limit_direction = (quality_weighted_up - quality_weighted_down) / total_stocks * 1000
            limit_direction = max(-50, min(50, limit_direction))  # 限制在±50范围内
        else:
            limit_direction = 0
        
        # 综合方向性强度
        direction_strength = base_direction + limit_direction
        return max(0, min(100, direction_strength))
    
    def _calculate_liquidity_strength(self, activity, volume_ratio, turnover_rate,
                                    limit_up_total, limit_down_total, total_stocks) -> float:
        """
        计算流动性强度
        考虑成交量放大、换手率变化、量价配合度等
        """
        # 活跃度基础分
        activity_score = min(100, activity)
        
        # 成交量放大效应
        volume_amplification = min(50, (volume_ratio - 1) * 100) if volume_ratio > 1 else 0
        
        # 换手率活跃度
        turnover_activity = min(30, turnover_rate * 1500)  # 2%换手率对应30分
        
        # 涨停封板强度（流动性消耗指标）
        if total_stocks > 0 and limit_up_total > 0:
            limit_up_ratio = limit_up_total / total_stocks
            sealing_strength = min(20, limit_up_ratio * 2000)  # 涨停比例越高，封板越强
        else:
            sealing_strength = 0
        
        # 综合流动性强度
        liquidity_strength = (activity_score * 0.4 + volume_amplification * 0.3 + 
                            turnover_activity * 0.2 + sealing_strength * 0.1)
        
        return max(0, min(100, liquidity_strength))
    
    def _calculate_persistence_strength(self, current_data, historical_data) -> float:
        """
        计算持续性强度
        分析涨停持续时间、资金流向连续性等
        """
        if not historical_data or len(historical_data) < 2:
            return 50.0  # 无历史数据时返回中性值
        
        # 计算涨停持续性
        current_limit_up = current_data.get('limit_up_real', 0) + current_data.get('limit_up_oneway', 0)
        
        persistence_scores = []
        
        # 检查过去几天的涨停趋势
        for i, hist_data in enumerate(historical_data[-3:]):
            hist_limit_up = hist_data.get('limit_up_real', 0) + hist_data.get('limit_up_oneway', 0)
            
            if current_limit_up > 0 and hist_limit_up > 0:
                # 涨停数量的连续性
                continuity = min(1.0, hist_limit_up / current_limit_up)
                weight = 0.8 ** i  # 越近的数据权重越高
                persistence_scores.append(continuity * weight)
        
        if persistence_scores:
            avg_persistence = sum(persistence_scores) / len(persistence_scores)
            persistence_strength = avg_persistence * 100
        else:
            persistence_strength = 50.0
        
        # 资金流向连续性
        if len(historical_data) >= 2:
            current_flow = self._calculate_fund_flow_direction(current_data)
            prev_flow = self._calculate_fund_flow_direction(historical_data[-1])
            
            flow_consistency = 1 - abs(current_flow - prev_flow) / 100
            persistence_strength = persistence_strength * 0.7 + flow_consistency * 100 * 0.3
        
        return max(0, min(100, persistence_strength))
    
    def _calculate_divergence_strength(self, up_stocks, down_stocks, flat_stocks,
                                     limit_up_real, limit_up_oneway,
                                     limit_down_real, limit_down_oneway) -> float:
        """
        计算分化强度
        衡量市场内部结构性变化和板块分化程度
        """
        total_stocks = up_stocks + down_stocks + flat_stocks
        if total_stocks == 0:
            return 50.0
        
        # 涨跌分化程度
        up_ratio = up_stocks / total_stocks
        down_ratio = down_stocks / total_stocks
        flat_ratio = flat_stocks / total_stocks
        
        # 计算分布的离散度（基于信息熵）
        ratios = [up_ratio, down_ratio, flat_ratio]
        ratios = [r for r in ratios if r > 0]  # 过滤零值
        
        if len(ratios) > 1:
            entropy = -sum(r * math.log(r) for r in ratios)
            max_entropy = math.log(3)  # 三种状态的最大熵
            divergence_base = (entropy / max_entropy) * 100
        else:
            divergence_base = 0  # 完全集中，无分化
        
        # 涨停跌停的分化效应
        limit_up_total = limit_up_real + limit_up_oneway
        limit_down_total = limit_down_real + limit_down_oneway
        
        if limit_up_total + limit_down_total > 0:
            limit_divergence = abs(limit_up_total - limit_down_total) / total_stocks * 100
            limit_divergence = min(50, limit_divergence)
        else:
            limit_divergence = 0
        
        # 真实涨停与一字涨停的分化
        if limit_up_total > 0:
            quality_divergence = abs(limit_up_real - limit_up_oneway) / limit_up_total * 30
        else:
            quality_divergence = 0
        
        # 综合分化强度
        divergence_strength = (divergence_base * 0.5 + limit_divergence * 0.3 + 
                             quality_divergence * 0.2)
        
        return max(0, min(100, divergence_strength))
    
    def _calculate_participation_strength(self, total_stocks, flat_stocks, 
                                        activity, turnover_rate) -> float:
        """
        计算参与度强度
        衡量市场整体参与程度和活跃度
        """
        if total_stocks == 0:
            return 50.0
        
        # 非平盘股票比例（参与交易的股票比例）
        active_ratio = (total_stocks - flat_stocks) / total_stocks
        participation_base = active_ratio * 100
        
        # 活跃度调整
        activity_adjustment = min(100, activity)
        
        # 换手率参与度
        turnover_participation = min(50, turnover_rate * 2500)  # 2%换手率对应50分
        
        # 综合参与度强度
        participation_strength = (participation_base * 0.4 + 
                                activity_adjustment * 0.4 + 
                                turnover_participation * 0.2)
        
        return max(0, min(100, participation_strength))
    
    def _identify_market_state(self, market_data) -> str:
        """
        识别市场状态（牛市/熊市/震荡市）
        """
        up_stocks = market_data.get('up_stocks', 0)
        down_stocks = market_data.get('down_stocks', 0)
        flat_stocks = market_data.get('flat_stocks', 0)
        limit_up_total = (market_data.get('limit_up_real', 0) + 
                         market_data.get('limit_up_oneway', 0))
        limit_down_total = (market_data.get('limit_down_real', 0) + 
                           market_data.get('limit_down_oneway', 0))
        
        total_stocks = up_stocks + down_stocks + flat_stocks
        if total_stocks == 0:
            return 'sideways'
        
        up_ratio = up_stocks / total_stocks
        limit_up_ratio = limit_up_total / total_stocks
        limit_down_ratio = limit_down_total / total_stocks
        
        # 牛市特征：上涨比例高，涨停多，跌停少
        if up_ratio > 0.6 and limit_up_ratio > 0.02 and limit_down_ratio < 0.01:
            return 'bull'
        # 熊市特征：下跌比例高，跌停多，涨停少
        elif up_ratio < 0.4 and limit_down_ratio > 0.01 and limit_up_ratio < 0.02:
            return 'bear'
        else:
            return 'sideways'
    
    def _apply_nonlinear_adjustment(self, strength) -> float:
        """
        应用非线性调整和阈值效应
        """
        # S型曲线调整，增强极值效应
        if strength > 80:
            # 高值区域增强
            adjustment = (strength - 80) * 0.5
            strength = min(100, strength + adjustment)
        elif strength < 20:
            # 低值区域增强
            adjustment = (20 - strength) * 0.5
            strength = max(0, strength - adjustment)
        
        return strength
    
    def _apply_time_decay(self, strength) -> float:
        """
        应用时间衰减因子
        """
        # 简单的时间衰减，可以根据需要调整
        current_hour = datetime.now().hour
        
        # 开盘和收盘时段权重更高
        if 9 <= current_hour <= 11 or 13 <= current_hour <= 15:
            time_weight = 1.0
        else:
            time_weight = 0.9
        
        return strength * time_weight
    
    def _calculate_fund_flow_direction(self, data) -> float:
        """
        计算资金流向方向
        """
        up_stocks = data.get('up_stocks', 0)
        down_stocks = data.get('down_stocks', 0)
        total = up_stocks + down_stocks
        
        if total == 0:
            return 50.0
        
        return (up_stocks / total) * 100
    
    def _calculate_confidence_level(self, market_data) -> float:
        """
        计算置信度水平
        """
        total_stocks = (market_data.get('up_stocks', 0) + 
                       market_data.get('down_stocks', 0) + 
                       market_data.get('flat_stocks', 0))
        
        # 样本量越大，置信度越高
        if total_stocks > 4000:
            return 0.95
        elif total_stocks > 3000:
            return 0.90
        elif total_stocks > 2000:
            return 0.85
        elif total_stocks > 1000:
            return 0.80
        else:
            return 0.75
    
    def _calculate_volatility_risk(self, market_data) -> float:
        """
        计算波动性风险
        """
        limit_up_total = (market_data.get('limit_up_real', 0) + 
                         market_data.get('limit_up_oneway', 0))
        limit_down_total = (market_data.get('limit_down_real', 0) + 
                           market_data.get('limit_down_oneway', 0))
        total_stocks = (market_data.get('up_stocks', 0) + 
                       market_data.get('down_stocks', 0) + 
                       market_data.get('flat_stocks', 0))
        
        if total_stocks == 0:
            return 0.5
        
        extreme_ratio = (limit_up_total + limit_down_total) / total_stocks
        return min(1.0, extreme_ratio * 10)  # 涨停跌停比例越高，波动风险越大
    
    def _calculate_liquidity_risk(self, market_data) -> float:
        """
        计算流动性风险
        """
        activity = market_data.get('activity', 50)
        turnover_rate = market_data.get('turnover_rate', 0.02)
        
        # 活跃度和换手率越低，流动性风险越高
        liquidity_score = (activity / 100 + turnover_rate * 50) / 2
        return max(0, min(1.0, 1 - liquidity_score))
    
    def _calculate_concentration_risk(self, market_data) -> float:
        """
        计算集中度风险
        """
        limit_up_real = market_data.get('limit_up_real', 0)
        limit_up_oneway = market_data.get('limit_up_oneway', 0)
        
        if limit_up_real + limit_up_oneway == 0:
            return 0.3
        
        # 一字涨停比例越高，集中度风险越大
        oneway_ratio = limit_up_oneway / (limit_up_real + limit_up_oneway)
        return min(1.0, oneway_ratio * 1.5)
    
    def _update_history_cache(self, market_data, result):
        """
        更新历史数据缓存
        """
        cache_entry = {
            'timestamp': datetime.now(),
            'market_data': market_data.copy(),
            'result': result.copy()
        }
        
        self.history_cache.append(cache_entry)
        
        # 保持缓存大小
        if len(self.history_cache) > self.max_history_days:
            self.history_cache.pop(0)