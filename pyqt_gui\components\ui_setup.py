from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, 
                           QLabel, QPushButton, QComboBox, QCheckBox, QFrame, QSplitter,
                           QTreeWidget, QTreeWidgetItem, QHeaderView, QTextEdit, QLineEdit,
                           QTabWidget, QGridLayout, QGroupBox, QTableWidget, QTableWidgetItem,
                           QAbstractItemView, QScrollArea, QSizePolicy, QStackedWidget, QButtonGroup)
from PyQt6.QtCore import Qt, QTimer, QSize
from PyQt6.QtGui import QFont, QColor, QIcon, QPixmap

from pyqt_gui.components.auto_refresh import AutoRefresh
from pyqt_gui.components.status_manager import StatusManager

class UISetup:
    """UI设置类，用于管理主窗口的UI组件设置"""
    
    @staticmethod
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(12, 12, 12, 12)  # 增加边距
        main_layout.setSpacing(12)  # 增加组件间距
        
        # 顶部控制栏
        UISetup.create_control_bar(self, main_layout)
        
        # 主内容区域
        content_splitter = QSplitter(Qt.Orientation.Horizontal)
        content_splitter.setObjectName("main_splitter")  # 为响应式布局添加标识
        content_splitter.setChildrenCollapsible(False)  # 防止子组件被完全折叠
        main_layout.addWidget(content_splitter, 1)  # 1表示拉伸因子
        
        # 左侧板块排名区域
        sector_widget = UISetup.create_sector_panel(self)
        sector_widget.setObjectName("left_panel")  # 为响应式布局添加标识
        
        # 中间成分股区域
        constituent_widget = UISetup.create_constituent_panel(self)
        constituent_widget.setObjectName("middle_panel")  # 为响应式布局添加标识
        
        # 右侧个股分析区域
        analysis_widget = UISetup.create_analysis_panel(self)
        analysis_widget.setObjectName("right_panel")  # 为响应式布局添加标识
        
        # 添加三个主要区域到分割器
        content_splitter.addWidget(sector_widget)
        content_splitter.addWidget(constituent_widget)
        content_splitter.addWidget(analysis_widget)
        
        # 设置分割器的初始大小，适应1600x900分辨率
        content_splitter.setSizes([350, 550, 600])
        
        # 保存重要组件的引用
        self.main_splitter = content_splitter
        self.left_panel = sector_widget
        self.middle_panel = constituent_widget
        self.right_panel = analysis_widget
    
    @staticmethod
    def create_control_bar(self, parent_layout):
        """创建顶部控制栏"""
        control_frame = QFrame()
        control_frame.setFrameShape(QFrame.Shape.StyledPanel)
        control_frame.setMaximumHeight(70)  # 增加高度
        
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(12, 8, 12, 8)  # 增加内边距
        
        # 刷新按钮
        refresh_btn = QPushButton("获取板块排名")
        refresh_btn.setMinimumWidth(120)  # 增加按钮宽度
        refresh_btn.setMinimumHeight(40)  # 增加按钮高度
        refresh_btn.clicked.connect(lambda: self.load_initial_data())
        control_layout.addWidget(refresh_btn)
        
        # 自动刷新复选框
        self.auto_refresh_checkbox = QCheckBox("自动刷新")
        self.auto_refresh_checkbox.setFont(QFont("微软雅黑", 10))  # 增加字体大小
        self.auto_refresh_checkbox.toggled.connect(lambda checked: AutoRefresh.toggle_auto_refresh(self, checked))
        control_layout.addWidget(self.auto_refresh_checkbox)
        
        # 刷新间隔下拉框
        interval_label = QLabel("刷新间隔:")
        interval_label.setFont(QFont("微软雅黑", 10))  # 增加字体大小
        control_layout.addWidget(interval_label)
        
        self.interval_combo = QComboBox()
        self.interval_combo.setMinimumHeight(30)  # 增加下拉框高度
        self.interval_combo.addItems(["5秒", "10秒", "30秒", "1分钟", "3分钟", "5分钟", "10分钟", "30分钟", "1小时"])
        self.interval_combo.setCurrentText("1分钟")
        self.interval_combo.currentTextChanged.connect(lambda text: AutoRefresh.update_refresh_interval(self, text))
        control_layout.addWidget(self.interval_combo)
        
        # 倒计时标签
        self.countdown_label = QLabel("")
        self.countdown_label.setFont(QFont("微软雅黑", 10))  # 增加字体大小
        control_layout.addWidget(self.countdown_label)
        
        # 添加弹性空间
        control_layout.addStretch(1)
        
        # 主题切换按钮
        self.theme_btn = QPushButton("切换主题")
        self.theme_btn.setMinimumWidth(100)
        self.theme_btn.setMinimumHeight(40)
        self.theme_btn.clicked.connect(lambda: StatusManager.toggle_theme(self))
        control_layout.addWidget(self.theme_btn)
        
        # 添加到主布局
        parent_layout.addWidget(control_frame)
    
    @staticmethod
    def create_sector_panel(self):
        """创建左侧板块排名面板"""
        sector_widget = QWidget()
        sector_layout = QVBoxLayout(sector_widget)
        sector_layout.setContentsMargins(0, 0, 0, 0)
        
        # 板块排名框（包含行业和概念切换）
        sector_group = QGroupBox("板块排名")
        sector_inner_layout = QVBoxLayout(sector_group)
        
        # 添加切换按钮
        button_layout = QHBoxLayout()
        self.sector_button_group = QButtonGroup()
        
        self.industry_button = QPushButton("行业板块")
        self.industry_button.setCheckable(True)
        self.industry_button.setChecked(True)
        self.sector_button_group.addButton(self.industry_button)
        button_layout.addWidget(self.industry_button)
        
        self.concept_button = QPushButton("概念板块")
        self.concept_button.setCheckable(True)
        self.sector_button_group.addButton(self.concept_button)
        button_layout.addWidget(self.concept_button)
        
        sector_inner_layout.addLayout(button_layout)
        
        # 创建堆叠部件用于切换显示
        self.sector_stack = QStackedWidget()
        
        # 行业板块排名页
        industry_page = QWidget()
        industry_layout = QVBoxLayout(industry_page)
        industry_layout.setContentsMargins(0, 0, 0, 0)
        self.industry_tree = UISetup.create_rank_tree(self)
        industry_layout.addWidget(self.industry_tree)
        self.sector_stack.addWidget(industry_page)
        
        # 概念板块排名页
        concept_page = QWidget()
        concept_layout = QVBoxLayout(concept_page)
        concept_layout.setContentsMargins(0, 0, 0, 0)
        self.concept_tree = UISetup.create_rank_tree(self)
        concept_layout.addWidget(self.concept_tree)
        self.sector_stack.addWidget(concept_page)
        
        # 添加堆叠部件到布局
        sector_inner_layout.addWidget(self.sector_stack)
        
        # 连接按钮信号
        self.industry_button.clicked.connect(lambda: self.sector_stack.setCurrentIndex(0))
        self.concept_button.clicked.connect(lambda: self.sector_stack.setCurrentIndex(1))
        
        # 赚钱效应评分模块
        money_effect_group = QGroupBox("赚钱效应评分")
        money_effect_layout = QVBoxLayout(money_effect_group)
        
        # 添加操作栏
        money_effect_control_layout = QHBoxLayout()
        
        # 手动刷新按钮
        refresh_money_effect_btn = QPushButton("刷新评分")
        refresh_money_effect_btn.clicked.connect(lambda: self.update_money_effect_score())
        money_effect_control_layout.addWidget(refresh_money_effect_btn)
        
        # 自动刷新复选框
        self.money_effect_auto_refresh = QCheckBox("自动刷新")
        self.money_effect_auto_refresh.toggled.connect(lambda checked: AutoRefresh.toggle_money_effect_auto_refresh(self, checked))
        money_effect_control_layout.addWidget(self.money_effect_auto_refresh)
        
        # 刷新间隔下拉框
        money_effect_control_layout.addWidget(QLabel("间隔:"))
        self.money_effect_interval_combo = QComboBox()
        self.money_effect_interval_combo.addItems(["5秒", "10秒", "30秒", "1分钟", "3分钟", "5分钟", "10分钟", "30分钟", "1小时"])
        self.money_effect_interval_combo.setCurrentText("1分钟")
        self.money_effect_interval_combo.currentTextChanged.connect(lambda text: AutoRefresh.update_money_effect_refresh_interval(self, text))
        money_effect_control_layout.addWidget(self.money_effect_interval_combo)
        
        # 倒计时标签
        self.money_effect_countdown_label = QLabel("")
        money_effect_control_layout.addWidget(self.money_effect_countdown_label)
        
        # 添加到布局
        money_effect_layout.addLayout(money_effect_control_layout)
        
        # 创建赚钱效应评分显示区域
        self.money_effect_text = QTextEdit()
        self.money_effect_text.setReadOnly(True)
        self.money_effect_text.setFont(QFont("微软雅黑", 10))  # 使用更大的字体
        money_effect_layout.addWidget(self.money_effect_text)
        
        # 添加到左侧布局
        sector_layout.addWidget(sector_group)
        sector_layout.addWidget(money_effect_group)
        
        return sector_widget
    
    @staticmethod
    def create_constituent_panel(self):
        """创建中间成分股面板"""
        constituent_widget = QWidget()
        constituent_layout = QVBoxLayout(constituent_widget)
        constituent_layout.setContentsMargins(0, 0, 0, 0)
        
        # 成分股标题和表格
        constituent_group = QGroupBox("板块成分股")
        constituent_inner_layout = QVBoxLayout(constituent_group)
        
        # 添加选中板块显示
        self.selected_sector_label = QLabel("请双击左侧板块查看成分股")
        self.selected_sector_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.selected_sector_label.setFont(QFont("微软雅黑", 11, QFont.Weight.Bold))  # 增加字体大小
        constituent_inner_layout.addWidget(self.selected_sector_label)
        
        # 成分股表格
        self.constituent_table = QTableWidget()
        self.constituent_table.setAlternatingRowColors(True)
        self.constituent_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.constituent_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.constituent_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.constituent_table.setColumnCount(6)
        self.constituent_table.setHorizontalHeaderLabels(["股票代码", "股票名称", "最新价", "成交额", "涨跌幅", "分数"])
        self.constituent_table.setObjectName("constituent_table")  # 为响应式布局添加标识
        
        # 设置表格头部不自动拉伸，由响应式布局管理器控制
        self.constituent_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        self.constituent_table.verticalHeader().setVisible(False)
        
        # 设置初始列宽（将由响应式布局管理器动态调整）
        self.constituent_table.setColumnWidth(0, 80)   # 股票代码
        self.constituent_table.setColumnWidth(1, 100)  # 股票名称
        self.constituent_table.setColumnWidth(2, 80)   # 最新价
        self.constituent_table.setColumnWidth(3, 100)  # 成交额
        self.constituent_table.setColumnWidth(4, 80)   # 涨跌幅
        self.constituent_table.setColumnWidth(5, 80)   # 分数
        
        constituent_inner_layout.addWidget(self.constituent_table)
        
        # 将成分股区域添加到主布局
        constituent_layout.addWidget(constituent_group)
        

        # 原快讯区域的UI组件已被删除，包括：
        # - 刷新快讯按钮
        # - 自动刷新复选框和间隔选择
        # - 数据源选择下拉框
        # - 快讯表格
        # 中间面板现在只显示成分股信息
        
        return constituent_widget
    
    @staticmethod
    def create_analysis_panel(self):
        """创建右侧个股分析面板"""
        analysis_widget = QWidget()
        analysis_layout = QVBoxLayout(analysis_widget)
        analysis_layout.setContentsMargins(0, 0, 0, 0)
        
        # 个股分析标题和内容
        analysis_group = QGroupBox("个股分析")
        analysis_inner_layout = QVBoxLayout(analysis_group)
        
        # 添加股票代码输入区域
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("股票代码:"))
        self.stock_code_input = QLineEdit()
        self.stock_code_input.setPlaceholderText("输入股票代码，如: 600000")
        self.stock_code_input.setFixedHeight(32)  # 增加输入框高度
        input_layout.addWidget(self.stock_code_input)
        
        analyze_btn = QPushButton("开始分析")
        analyze_btn.clicked.connect(lambda: self.analyze_stock())
        analyze_btn.setFixedHeight(32)  # 增加按钮高度
        input_layout.addWidget(analyze_btn)
        input_layout.addStretch(1)
        
        # 添加股票信息标签区域
        stock_info_layout = QGridLayout()
        stock_info_layout.setSpacing(10)  # 增加空间
        
        self.stock_code_label = QLabel("股票代码:")
        self.stock_name_label = QLabel("股票名称:")
        self.latest_price_label = QLabel("最新价:")
        self.change_label = QLabel("涨跌幅:")
        self.trade_date_label = QLabel("日期:")
        
        # 设置标签字体
        label_font = QFont("微软雅黑", 11)
        self.stock_code_label.setFont(label_font)
        self.stock_name_label.setFont(label_font)
        self.latest_price_label.setFont(label_font)
        self.change_label.setFont(label_font)
        self.trade_date_label.setFont(label_font)
        
        stock_info_layout.addWidget(self.stock_code_label, 0, 0)
        stock_info_layout.addWidget(self.stock_name_label, 0, 1)
        stock_info_layout.addWidget(self.latest_price_label, 1, 0)
        stock_info_layout.addWidget(self.change_label, 1, 1)
        stock_info_layout.addWidget(self.trade_date_label, 2, 0, 1, 2)
        
        # 分析结果文本区域
        self.analysis_text = QTextEdit()
        self.analysis_text.setReadOnly(True)
        self.analysis_text.setFont(QFont("微软雅黑", 10))  # 设置更大的字体
        
        analysis_inner_layout.addLayout(input_layout)
        analysis_inner_layout.addLayout(stock_info_layout)
        analysis_inner_layout.addWidget(self.analysis_text)
        
        analysis_layout.addWidget(analysis_group)
        
        return analysis_widget

    @staticmethod
    def create_rank_tree(self):
        """创建排名树形视图"""
        tree = QTreeWidget()
        tree.setColumnCount(5)
        tree.setHeaderLabels(["排名", "板块名称", "分数", "涨跌幅", "主力净流入"])
        tree.setAlternatingRowColors(True)
        tree.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        tree.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        
        # 设置列宽，适配1600x900分辨率
        tree.setColumnWidth(0, 60)   # 排名
        tree.setColumnWidth(1, 120)  # 板块名称
        tree.setColumnWidth(2, 80)   # 分数
        tree.setColumnWidth(3, 90)   # 涨跌幅
        tree.setColumnWidth(4, 130)  # 主力净流入
        
        # 设置字体
        font = QFont("微软雅黑", 10)
        tree.setFont(font)
        
        header = tree.header()
        header.setFont(QFont("微软雅黑", 10, QFont.Weight.Bold))
        
        # 设置选择时的高亮颜色为蓝色
        tree.setStyleSheet("""
            QTreeWidget::item:selected {
                background-color: #3f8cff;
                color: white;
            }
        """)
        
        return tree

    @staticmethod
    def setup_status_bar(self):
        """设置状态栏和进度指示器"""
        # 导入自定义进度组件
        from pyqt_gui.progress_widgets import StatusIndicator, ProgressBar
        
        # 创建状态栏
        status_bar = self.statusBar()
        
        # 添加状态指示器
        self.status_indicator = StatusIndicator()
        status_bar.addPermanentWidget(self.status_indicator)
        
        # 添加进度条
        self.status_progress_bar = ProgressBar()
        self.status_progress_bar.setFixedWidth(180)  # 增加进度条宽度
        self.status_progress_bar.setVisible(False)
        status_bar.addPermanentWidget(self.status_progress_bar)
        
        # 添加状态文本
        self.status_label = QLabel("就绪")
        self.status_label.setFont(QFont("微软雅黑", 10))  # 增加字体大小
        status_bar.addWidget(self.status_label, 1)