"""
UI启动组件 - 提供增强型启动画面和相关UI组件
"""

from __future__ import annotations

import sys
import os
import traceback
from utils.logging_config import get_logger

try:
    from PyQt6.QtWidgets import QApplication, QSplashScreen, QProgressBar, QMessageBox
    from PyQt6.QtCore import Qt, QTimer
    from PyQt6.QtGui import QPixmap, QFontDatabase, QFont, QColor
except ImportError:
    print("PyQt6未安装或导入失败。请确保已安装PyQt6库。")
    sys.exit(1)

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.append(project_root)

# 配置模块日志
logger = get_logger(__name__)

# 定义启动阶段
STARTUP_STAGES = [
    "初始化应用程序...",
    "加载系统资源...",
    "准备数据模块...",
    "初始化界面组件...",
    "连接信号槽...",
    "准备就绪"
]

class EnhancedSplashScreen(QSplashScreen):
    """增强型启动画面，支持进度条和自定义绘制"""
    def __init__(self, pixmap):
        super().__init__(pixmap)
        self.setObjectName("SplashScreen")
        
        # 设置窗口属性，避免强制置顶但保持在任务栏显示
        self.setWindowFlags(
            Qt.WindowType.SplashScreen | 
            Qt.WindowType.FramelessWindowHint
        )
        
        # 设置窗口居中显示
        self._center_on_screen()
        
        # 允许点击关闭启动画面
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose)
    
        # 创建进度条
        self.progress_bar = QProgressBar(self)
        self.progress_bar.setGeometry(50, 260, 300, 20)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFormat("%p%")
        
        # 设置进度条样式
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #CCCCCC;
                border-radius: 5px;
                background-color: #F5F5F5;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 5px;
            }
        """)
        
        # 当前阶段和进度
        self.current_stage = 0
        self.current_progress = 0
        self.message = "初始化应用程序..."
    
    def _center_on_screen(self):
        """将启动画面居中显示在屏幕上"""
        try:
            from PyQt6.QtWidgets import QApplication
            screen = QApplication.primaryScreen()
            if screen:
                screen_geometry = screen.availableGeometry()
                splash_geometry = self.geometry()
                x = (screen_geometry.width() - splash_geometry.width()) // 2
                y = (screen_geometry.height() - splash_geometry.height()) // 2
                self.move(x, y)
        except Exception:
            # 如果居中失败，使用默认位置
            pass
    
    def mousePressEvent(self, event):
        """允许用户点击跳过启动画面"""
        # 如果进度超过50%，允许点击跳过
        if self.current_progress > 50:
            self.close()
        super().mousePressEvent(event)
    
    def set_progress(self, progress, message=None):
        """设置进度和消息"""
        try:
            self.current_progress = progress
            if message:
                self.message = message
            
            # 检查进度条是否仍然存在
            if hasattr(self, 'progress_bar') and self.progress_bar is not None:
                self.progress_bar.setValue(progress)
            
            self.repaint()
        except RuntimeError:
            # 如果对象已被删除，忽略错误
            pass
        except Exception as e:
            logger.warning(f"设置启动画面进度时出错: {e}")
    
    def drawContents(self, painter):
        """自定义绘制内容"""
        # 绘制标题
        title_font = QFont("Arial", 16, QFont.Weight.Bold)
        painter.setFont(title_font)
        painter.setPen(QColor(44, 62, 80))
        painter.drawText(50, 50, "AI股票分析系统")
        
        # 绘制副标题
        subtitle_font = QFont("Arial", 10)
        painter.setFont(subtitle_font)
        painter.setPen(QColor(52, 73, 94))
        painter.drawText(50, 80, "基于PyQt6的高性能分析工具")
        
        # 绘制当前状态消息
        status_font = QFont("Arial", 10)
        painter.setFont(status_font)
        painter.setPen(QColor(41, 128, 185))
        painter.drawText(50, 240, self.message)
        
        # 绘制跳过提示（当进度超过50%时）
        if self.current_progress > 50:
            hint_font = QFont("Arial", 8)
            painter.setFont(hint_font)
            painter.setPen(QColor(128, 128, 128))
            painter.drawText(250, 290, "点击任意位置跳过")

# 导出所有需要的组件
__all__ = ['EnhancedSplashScreen', 'STARTUP_STAGES']