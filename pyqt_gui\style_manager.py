# style_manager.py
from PyQt6.QtGui import QColor, QPalette, QFont
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QSettings


class StyleManager:
    """应用程序样式管理类"""
    
    # 颜色方案
    COLORS = {
        'light': {
        'bg': '#f8f9fa',           # 背景色
        'fg': '#212529',           # 前景色
        'accent': '#0d6efd',       # 强调色
        'accent_hover': '#0b5ed7', # 强调色悬停
        'card_bg': '#ffffff',      # 卡片背景
        'border': '#dee2e6',       # 边框色
        'rise': '#e63946',         # 上涨色（红色）
        'fall': '#2a9d8f',         # 下跌色（绿色）
        'neutral': '#457b9d',      # 中性色
        'highlight': '#f8f9d7',    # 高亮色
        'header_bg': '#f1f3f5',    # 表头背景色
            'grid_line': '#e9ecef',    # 网格线颜色
            'primary': '#1a73e8',      # 主要按钮色
            'success': '#2e7d32',      # 成功状态色
            'warning': '#ed6c02',      # 警告状态色
            'error': '#d32f2f',        # 错误状态色
            'info': '#0288d1',         # 信息状态色
        },
        'dark': {
            'bg': '#121212',           # 背景色
            'fg': '#e0e0e0',           # 前景色
            'accent': '#3f8cff',       # 强调色
            'accent_hover': '#5c9eff', # 强调色悬停
            'card_bg': '#1e1e1e',      # 卡片背景
            'border': '#333333',       # 边框色
            'rise': '#ff5252',         # 上涨色（红色）
            'fall': '#4caf50',         # 下跌色（绿色）
            'neutral': '#64b5f6',      # 中性色
            'highlight': '#2c2c2c',    # 高亮色
            'header_bg': '#2c2c2c',    # 表头背景色
            'grid_line': '#333333',    # 网格线颜色
            'primary': '#3f8cff',      # 主要按钮色
            'success': '#4caf50',      # 成功状态色
            'warning': '#ff9800',      # 警告状态色
            'error': '#ff5252',        # 错误状态色
            'info': '#29b6f6',         # 信息状态色
        },
        'blue': {
            'bg': '#e7f0fe',           # 背景色 - 淡蓝色
            'fg': '#1e293b',           # 前景色 - 深蓝灰色
            'accent': '#0ea5e9',       # 强调色 - 天蓝色
            'accent_hover': '#0284c7', # 强调色悬停 - 深天蓝色
            'card_bg': '#ffffff',      # 卡片背景 - 白色
            'border': '#bfdbfe',       # 边框色 - 淡蓝色
            'rise': '#dc2626',         # 上涨色 - 鲜红色
            'fall': '#15803d',         # 下跌色 - 深绿色
            'neutral': '#3b82f6',      # 中性色 - 蓝色
            'highlight': '#f0f9ff',    # 高亮色 - 超淡蓝色
            'header_bg': '#dbeafe',    # 表头背景色 - 淡蓝色
            'grid_line': '#e0f2fe',    # 网格线颜色 - 淡蓝色
            'primary': '#0ea5e9',      # 主要按钮色 - 天蓝色
            'success': '#16a34a',      # 成功状态色 - 绿色
            'warning': '#f97316',      # 警告状态色 - 橙色
            'error': '#dc2626',        # 错误状态色 - 红色
            'info': '#2563eb',         # 信息状态色 - 蓝色
        },
        'volcano': {
            'bg': '#fffbeb',           # 背景色 - 淡黄色
            'fg': '#1e293b',           # 前景色 - 深蓝灰色
            'accent': '#f97316',       # 强调色 - 橙色
            'accent_hover': '#ea580c', # 强调色悬停 - 深橙色
            'card_bg': '#ffffff',      # 卡片背景 - 白色
            'border': '#fed7aa',       # 边框色 - 淡橙色
            'rise': '#ef4444',         # 上涨色 - 红色
            'fall': '#10b981',         # 下跌色 - 绿色
            'neutral': '#f59e0b',      # 中性色 - 琥珀色
            'highlight': '#fff7ed',    # 高亮色 - 超淡橙色
            'header_bg': '#ffedd5',    # 表头背景色 - 浅橙色
            'grid_line': '#fef3c7',    # 网格线颜色 - 浅黄色
            'primary': '#f97316',      # 主要按钮色 - 橙色
            'success': '#16a34a',      # 成功状态色 - 绿色
            'warning': '#d97706',      # 警告状态色 - 深黄色
            'error': '#dc2626',        # 错误状态色 - 红色
            'info': '#2563eb',         # 信息状态色 - 蓝色
        }
    }
    
    # 主题循环顺序
    THEME_CYCLE = ['light', 'blue', 'volcano', 'dark']
    
    # 字体设置 - 支持不同分辨率
    FONTS = {
        'default': {  # 默认分辨率 (1920x1080以下)
            'default': QFont("微软雅黑", 10),
            'title': QFont("微软雅黑", 14, QFont.Weight.Bold),
            'subtitle': QFont("微软雅黑", 12, QFont.Weight.Bold),
            'small': QFont("微软雅黑", 9),
            'data': QFont("微软雅黑", 11),
            'data_bold': QFont("微软雅黑", 11, QFont.Weight.Bold)
        },
        'high': {     # 高分辨率 (1920x1080及以上)
            'default': QFont("微软雅黑", 11),
            'title': QFont("微软雅黑", 16, QFont.Weight.Bold),
            'subtitle': QFont("微软雅黑", 14, QFont.Weight.Bold),
            'small': QFont("微软雅黑", 10),
            'data': QFont("微软雅黑", 12),
            'data_bold': QFont("微软雅黑", 12, QFont.Weight.Bold)
        },
    }
    
    # 当前主题和分辨率
    current_theme = 'light'
    current_resolution = 'default'
    settings = None
    
    @staticmethod
    def initialize():
        """初始化样式管理器"""
        StyleManager.settings = QSettings("LilyBull", "StockAnalysis")
        StyleManager.current_theme = StyleManager.settings.value("theme", "light")
        # 确保主题名称有效
        if StyleManager.current_theme not in StyleManager.COLORS:
            StyleManager.current_theme = "light"
        # 根据屏幕分辨率自动确定使用哪组字体
        screen = QApplication.primaryScreen()
        if screen.size().width() >= 1920 or screen.size().height() >= 1080:
            StyleManager.current_resolution = 'high'
    
    @staticmethod
    def apply_fusion_style(app: QApplication):
        """应用Fusion样式并自定义颜色"""
        app.setStyle("Fusion")
        
        colors = StyleManager.COLORS[StyleManager.current_theme]
        
        # 创建自定义调色板
        palette = QPalette()
        
        # 设置基本颜色
        palette.setColor(QPalette.ColorRole.Window, QColor(colors['bg']))
        palette.setColor(QPalette.ColorRole.WindowText, QColor(colors['fg']))
        palette.setColor(QPalette.ColorRole.Base, QColor(colors['card_bg']))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(colors['bg']))
        palette.setColor(QPalette.ColorRole.Text, QColor(colors['fg']))
        
        # 设置按钮颜色
        palette.setColor(QPalette.ColorRole.Button, QColor(colors['bg']))
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(colors['fg']))
        
        # 设置高亮颜色
        palette.setColor(QPalette.ColorRole.Highlight, QColor(colors['accent']))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor("white"))
        
        # 设置工具提示颜色
        palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(colors['card_bg']))
        palette.setColor(QPalette.ColorRole.ToolTipText, QColor(colors['fg']))
        
        # 应用调色板
        app.setPalette(palette)
        
        # 应用全局样式表
        app.setStyleSheet(StyleManager.get_stylesheet())
        
    @staticmethod
    def get_color(color_name):
        """获取颜色值"""
        colors = StyleManager.COLORS[StyleManager.current_theme]
        return QColor(colors.get(color_name, colors['fg']))
    
    @staticmethod
    def get_font(font_name):
        """获取字体"""
        fonts = StyleManager.FONTS[StyleManager.current_resolution]
        return fonts.get(font_name, fonts['default'])
        
    @staticmethod
    def toggle_theme():
        """切换主题，按照预定顺序循环切换"""
        # 获取当前主题在循环中的索引
        current_index = StyleManager.THEME_CYCLE.index(StyleManager.current_theme) if StyleManager.current_theme in StyleManager.THEME_CYCLE else 0
        
        # 计算下一个主题的索引
        next_index = (current_index + 1) % len(StyleManager.THEME_CYCLE)
        
        # 设置新主题
        StyleManager.current_theme = StyleManager.THEME_CYCLE[next_index]
        
        # 保存主题设置
        if StyleManager.settings:
            StyleManager.settings.setValue("theme", StyleManager.current_theme)
        
        # 获取当前应用程序实例重新应用样式
        app = QApplication.instance()
        if app:
            StyleManager.apply_fusion_style(app)
            
    @staticmethod
    def get_stylesheet():
        """获取全局样式表"""
        theme = StyleManager.current_theme
        colors = StyleManager.COLORS[theme]
        
        # 创建通用样式，对所有主题适用
        base_stylesheet = f"""
            QTreeWidget::item:selected, QTableWidget::item:selected {{
                background-color: {colors['accent']};
                color: white;
            }}
            
            QTreeWidget::item:hover:!selected, QTableWidget::item:hover:!selected {{
                background-color: {colors['highlight']};
            }}
        """
        
        if theme == 'light':
            return base_stylesheet + """
                QMainWindow, QDialog {
                    background-color: #f8f9fa;
                }
                
                QGroupBox {
                    font-weight: bold;
                    border: 1px solid #dee2e6;
                    border-radius: 6px;
                    margin-top: 10px;
                    padding-top: 20px;
                    background-color: #ffffff;
                }
                
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    background-color: #ffffff;
                }
                
                QPushButton {
                    background-color: #1a73e8;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 15px;
                    min-width: 85px;
                    font-weight: bold;
                }
                
                QPushButton:hover {
                    background-color: #1765cc;
                }
                
                QPushButton:pressed {
                    background-color: #1557b0;
                }
                
                QPushButton:disabled {
                    background-color: #a0a0a0;
                    color: #e0e0e0;
                }
                """
        elif theme == 'blue':
            return base_stylesheet + """
                QMainWindow, QDialog {
                    background-color: #e7f0fe;
                }
                
                QGroupBox {
                    font-weight: bold;
                    border: 1px solid #bfdbfe;
                    border-radius: 6px;
                    margin-top: 10px;
                    padding-top: 20px;
                    background-color: #ffffff;
                }
                
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    background-color: #ffffff;
                    color: #1e293b;
                }
                
                QPushButton {
                    background-color: #0ea5e9;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 15px;
                    min-width: 85px;
                    font-weight: bold;
                }
                
                QPushButton:hover {
                    background-color: #0284c7;
                }
                
                QPushButton:pressed {
                    background-color: #0369a1;
                }
                
                QPushButton:disabled {
                    background-color: #93c5fd;
                    color: #e0e0e0;
                }
                
                QTreeWidget, QTableWidget {
                    border: 1px solid #bfdbfe;
                    border-radius: 6px;
                    padding: 2px;
                    alternate-background-color: #f0f9ff;
                    gridline-color: #e0f2fe;
                    color: #1e293b;
                    background-color: #ffffff;
                }
                
                QTreeWidget::item, QTableWidget::item {
                    padding: 6px;
                    border-bottom: 1px solid #dbeafe;
                }
                
                QHeaderView::section {
                    background-color: #dbeafe;
                    padding: 8px;
                    border: 1px solid #bfdbfe;
                    border-top: 0px;
                    border-left: 0px;
                    font-weight: bold;
                    color: #1e293b;
                }
                
                QTextEdit, QLineEdit {
                    border: 1px solid #bfdbfe;
                    border-radius: 5px;
                    padding: 8px;
                    background-color: white;
                    color: #1e293b;
                    selection-background-color: #0ea5e9;
                }
                
                QComboBox {
                    border: 1px solid #bfdbfe;
                    border-radius: 5px;
                    padding: 6px;
                    min-width: 8em;
                    background-color: white;
                    color: #1e293b;
                }
                
                QComboBox::drop-down {
                    subcontrol-origin: padding;
                    subcontrol-position: top right;
                    width: 20px;
                    border-left: 1px solid #bfdbfe;
                }
                
                QStatusBar {
                    background-color: #e7f0fe;
                    color: #1e293b;
                    border-top: 1px solid #bfdbfe;
                    padding: 5px;
                }
                
                QTabWidget::pane {
                    border: 1px solid #bfdbfe;
                    border-radius: 6px;
                    padding: 5px;
                    background-color: white;
                }
                
                QTabBar::tab {
                    background-color: #dbeafe;
                    border: 1px solid #bfdbfe;
                    border-bottom: none;
                    border-top-left-radius: 4px;
                    border-top-right-radius: 4px;
                    padding: 8px 16px;
                    margin-right: 2px;
                    color: #1e293b;
                }
                
                QTabBar::tab:selected {
                    background-color: white;
                    border-bottom: 1px solid white;
                }
                
                QTabBar::tab:hover:!selected {
                    background-color: #f0f9ff;
                }
                
                QSplitter::handle {
                    background-color: #bfdbfe;
                }
                
                QSplitter::handle:horizontal {
                    width: 2px;
                }
                
                QSplitter::handle:vertical {
                    height: 2px;
                }
                
                QScrollBar:vertical {
                    border: none;
                    background: #e7f0fe;
                    width: 12px;
                    margin: 0px;
                    border-radius: 6px;
                }
                
                QScrollBar::handle:vertical {
                    background: #93c5fd;
                    min-height: 30px;
                    border-radius: 6px;
                }
                
                QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                    height: 0px;
                }
            """
        elif theme == 'volcano':
            return base_stylesheet + """
                QMainWindow, QDialog {
                    background-color: #fffbeb;
                }
                
                QGroupBox {
                    font-weight: bold;
                    border: 1px solid #fed7aa;
                    border-radius: 6px;
                    margin-top: 10px;
                    padding-top: 20px;
                    background-color: #ffffff;
                }
                
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    background-color: #ffffff;
                    color: #1e293b;
                }
                
                QPushButton {
                    background-color: #f97316;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 15px;
                    min-width: 85px;
                    font-weight: bold;
                }
                
                QPushButton:hover {
                    background-color: #ea580c;
                }
                
                QPushButton:pressed {
                    background-color: #c2410c;
                }
                
                QPushButton:disabled {
                    background-color: #fdba74;
                    color: #e0e0e0;
                }
                
                QTreeWidget, QTableWidget {
                    border: 1px solid #fed7aa;
                    border-radius: 6px;
                    padding: 2px;
                    alternate-background-color: #fff7ed;
                    gridline-color: #fef3c7;
                    color: #1e293b;
                    background-color: #ffffff;
                }
                
                QTreeWidget::item, QTableWidget::item {
                    padding: 6px;
                    border-bottom: 1px solid #ffedd5;
                }
                
                QHeaderView::section {
                    background-color: #ffedd5;
                    padding: 8px;
                    border: 1px solid #fed7aa;
                    border-top: 0px;
                    border-left: 0px;
                    font-weight: bold;
                    color: #1e293b;
                }
                
                QTextEdit, QLineEdit {
                    border: 1px solid #fed7aa;
                    border-radius: 5px;
                    padding: 8px;
                    background-color: white;
                    color: #1e293b;
                    selection-background-color: #f97316;
                }
                
                QComboBox {
                    border: 1px solid #fed7aa;
                    border-radius: 5px;
                    padding: 6px;
                    min-width: 8em;
                    background-color: white;
                    color: #1e293b;
                }
                
                QComboBox::drop-down {
                    subcontrol-origin: padding;
                    subcontrol-position: top right;
                    width: 20px;
                    border-left: 1px solid #fed7aa;
                }
                
                QStatusBar {
                    background-color: #fffbeb;
                    color: #1e293b;
                    border-top: 1px solid #fed7aa;
                    padding: 5px;
                }
                
                QTabWidget::pane {
                    border: 1px solid #fed7aa;
                    border-radius: 6px;
                    padding: 5px;
                    background-color: white;
                }
                
                QTabBar::tab {
                    background-color: #ffedd5;
                    border: 1px solid #fed7aa;
                    border-bottom: none;
                    border-top-left-radius: 4px;
                    border-top-right-radius: 4px;
                    padding: 8px 16px;
                    margin-right: 2px;
                    color: #1e293b;
                }
                
                QTabBar::tab:selected {
                    background-color: white;
                    border-bottom: 1px solid white;
                }
                
                QTabBar::tab:hover:!selected {
                    background-color: #fff7ed;
                }
                
                QSplitter::handle {
                    background-color: #fed7aa;
                }
                
                QSplitter::handle:horizontal {
                    width: 2px;
                }
                
                QSplitter::handle:vertical {
                    height: 2px;
                }
                
                QScrollBar:vertical {
                    border: none;
                    background: #fffbeb;
                    width: 12px;
                    margin: 0px;
                    border-radius: 6px;
                }
                
                QScrollBar::handle:vertical {
                    background: #fdba74;
                    min-height: 30px;
                    border-radius: 6px;
                }
                
                QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                    height: 0px;
                }
            """
        else:  # 深色主题
            return base_stylesheet + """
                QMainWindow, QDialog {
                    background-color: #121212;
                }
                
                QGroupBox {
                    font-weight: bold;
                    border: 1px solid #333333;
                    border-radius: 6px;
                    margin-top: 10px;
                    padding-top: 20px;
                    background-color: #1e1e1e;
                    color: #e0e0e0;
                }
                
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    background-color: #1e1e1e;
                    color: #e0e0e0;
                }
                
                QPushButton {
                    background-color: #3f8cff;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 15px;
                    min-width: 85px;
                    font-weight: bold;
                }
                
                QPushButton:hover {
                    background-color: #5c9eff;
                }
                
                QPushButton:pressed {
                    background-color: #2d79ed;
                }
                
                QPushButton:disabled {
                    background-color: #505050;
                    color: #888888;
                }
                
                QTreeWidget, QTableWidget {
                    border: 1px solid #333333;
                    border-radius: 6px;
                    padding: 2px;
                    alternate-background-color: #252525;
                    gridline-color: #333333;
                    color: #e0e0e0;
                    background-color: #1e1e1e;
                }
                
                QTreeWidget::item, QTableWidget::item {
                    padding: 6px;
                    border-bottom: 1px solid #2a2a2a;
                }
                
                QHeaderView::section {
                    background-color: #2c2c2c;
                    padding: 8px;
                    border: 1px solid #333333;
                    border-top: 0px;
                    border-left: 0px;
                    font-weight: bold;
                    color: #e0e0e0;
                }
                
                QTextEdit, QLineEdit {
                    border: 1px solid #333333;
                    border-radius: 5px;
                    padding: 8px;
                    background-color: #1e1e1e;
                    color: #e0e0e0;
                    selection-background-color: #3f8cff;
                }
                
                QComboBox {
                    border: 1px solid #333333;
                    border-radius: 5px;
                    padding: 6px;
                    min-width: 8em;
                    background-color: #1e1e1e;
                    color: #e0e0e0;
                }
                
                QComboBox::drop-down {
                    subcontrol-origin: padding;
                    subcontrol-position: top right;
                    width: 20px;
                    border-left: 1px solid #333333;
                }
                
                QStatusBar {
                    background-color: #121212;
                    color: #9e9e9e;
                    border-top: 1px solid #333333;
                    padding: 5px;
                }
                
                QTabWidget::pane {
                    border: 1px solid #333333;
                    border-radius: 6px;
                    padding: 5px;
                    background-color: #1e1e1e;
                }
                
                QTabBar::tab {
                    background-color: #2c2c2c;
                    border: 1px solid #333333;
                    border-bottom: none;
                    border-top-left-radius: 4px;
                    border-top-right-radius: 4px;
                    padding: 8px 16px;
                    margin-right: 2px;
                    color: #e0e0e0;
                }
                
                QTabBar::tab:selected {
                    background-color: #1e1e1e;
                    border-bottom: 1px solid #1e1e1e;
                }
                
                QTabBar::tab:hover:!selected {
                    background-color: #252525;
                }
                
                QSplitter::handle {
                    background-color: #333333;
                }
                
                QSplitter::handle:horizontal {
                    width: 2px;
                }
                
                QSplitter::handle:vertical {
                    height: 2px;
                }
                
                QScrollBar:vertical {
                    border: none;
                    background: #121212;
                    width: 12px;
                    margin: 0px;
                    border-radius: 6px;
                }
                
                QScrollBar::handle:vertical {
                    background: #505050;
                    min-height: 30px;
                    border-radius: 6px;
                }
                
                QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                    height: 0px;
                }
                
                QLabel {
                    color: #e0e0e0;
                }
                
                QCheckBox {
                    color: #e0e0e0;
                }
            """