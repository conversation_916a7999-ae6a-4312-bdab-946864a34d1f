import pandas as pd
from datetime import datetime
from PyQt6.QtWidgets import QTableWidgetItem, QHeaderView
from PyQt6.QtGui import QColor
from PyQt6.QtCore import Qt
import akshare as ak
from stock_scoring.models import StockScoringModelV4_1
from utils.logging_config import get_logger

# 导入数据验证和清洗模块
from utils.data_validator import DataValidator
from utils.data_cleaner import DataCleaner
from utils.data_validation import StockDataCleaningRules
from utils.validation_rules import StockDataValidatorFactory
from utils.validation_config import ValidationConfigManager

class DataHandlers:
    """处理数据更新、加载和表格渲染等功能"""
    
    # 类级别的验证器和清洗器
    _validator = None
    _cleaner = None
    _config_manager = None
    _validation_enabled = True
    _cleaning_enabled = True
    _statistics = {
        'total_processed': 0,
        'validation_failures': 0,
        'cleaning_applied': 0,
        'data_quality_scores': []
    }
    
    @classmethod
    def initialize_validation(cls, validation_enabled=True, cleaning_enabled=True):
        """初始化数据验证和清洗功能"""
        try:
            cls._validation_enabled = validation_enabled
            cls._cleaning_enabled = cleaning_enabled
            
            if validation_enabled:
                # 初始化验证器
                cls._validator = StockDataValidatorFactory.create_basic_stock_validator()
                
            if cleaning_enabled:
                # 初始化清洗器
                cls._cleaner = StockDataCleaningRules.create_realtime_cleaner()
                
            # 初始化配置管理器
            cls._config_manager = ValidationConfigManager()
            
            logger = get_logger(__name__)
            logger.info(f"GUI数据验证初始化完成 - 验证: {validation_enabled}, 清洗: {cleaning_enabled}")
            
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"GUI数据验证初始化失败: {str(e)}")
            cls._validation_enabled = False
            cls._cleaning_enabled = False
    
    @classmethod
    def _validate_and_clean_dataframe(cls, df, data_type='gui_display'):
        """验证和清洗DataFrame数据"""
        if df is None or df.empty:
            return df, {'quality_score': 0, 'validation_passed': False, 'cleaning_applied': False}
        
        result_info = {
            'quality_score': 100,
            'validation_passed': True,
            'cleaning_applied': False,
            'original_rows': len(df),
            'final_rows': len(df),
            'removed_rows': 0
        }
        
        try:
            cls._statistics['total_processed'] += 1
            
            # 数据验证
            if cls._validation_enabled and cls._validator:
                valid_rows = []
                invalid_count = 0
                
                for idx, row in df.iterrows():
                    try:
                        # 转换为字典进行验证
                        record = row.to_dict()
                        
                        # 标准化字段名
                        record = cls._standardize_gui_fields(record)
                        
                        # 执行验证
                        validated_record, validation_report = cls._validator.validate_record(record)
                        
                        if validation_report.failures == 0:
                            valid_rows.append(idx)
                        else:
                            invalid_count += 1
                            logger = get_logger(__name__)
                            logger.debug(f"GUI数据验证失败 - 行{idx}: {[error.message for error in validation_report.errors]}")
                            
                    except Exception as e:
                        invalid_count += 1
                        logger = get_logger(__name__)
                        logger.debug(f"GUI数据验证异常 - 行{idx}: {str(e)}")
                
                # 移除无效行
                if invalid_count > 0:
                    df = df.loc[valid_rows]
                    result_info['final_rows'] = len(df)
                    result_info['removed_rows'] = invalid_count
                    cls._statistics['validation_failures'] += invalid_count
                    
                    if len(df) == 0:
                        result_info['validation_passed'] = False
                        result_info['quality_score'] = 0
                        return df, result_info
            
            # 数据清洗
            if cls._cleaning_enabled and cls._cleaner and not df.empty:
                try:
                    cleaned_df = cls._cleaner.clean_dataframe(df)
                    if cleaned_df is not None and not cleaned_df.empty:
                        df = cleaned_df
                        result_info['cleaning_applied'] = True
                        cls._statistics['cleaning_applied'] += 1
                except Exception as e:
                    logger = get_logger(__name__)
                    logger.warning(f"GUI数据清洗失败: {str(e)}")
            
            # 计算数据质量评分
            if result_info['original_rows'] > 0:
                quality_score = (result_info['final_rows'] / result_info['original_rows']) * 100
                result_info['quality_score'] = round(quality_score, 2)
                cls._statistics['data_quality_scores'].append(quality_score)
            
            return df, result_info
            
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"GUI数据验证和清洗过程异常: {str(e)}")
            result_info['validation_passed'] = False
            result_info['quality_score'] = 50  # 默认评分
            return df, result_info
    
    @classmethod
    def _standardize_gui_fields(cls, record):
        """标准化GUI数据字段名"""
        field_mapping = {
            '代码': 'stock_code',
            '名称': 'stock_name', 
            '最新价': 'latest_price',
            '涨跌幅': 'change_percent',
            '成交额': 'amount',
            '成交量': 'volume',
            '换手率': 'turnover_rate',
            'main_net_amount': 'main_net_amount',
            'strength_score': 'strength_score',
            'sector_name': 'sector_name',
            'concept_name': 'concept_name'
        }
        
        standardized = {}
        for key, value in record.items():
            new_key = field_mapping.get(key, key)
            standardized[new_key] = value
        
        return standardized
    
    @classmethod
    def get_gui_statistics(cls):
        """获取GUI数据处理统计信息"""
        stats = cls._statistics.copy()
        if stats['data_quality_scores']:
            stats['average_quality_score'] = sum(stats['data_quality_scores']) / len(stats['data_quality_scores'])
        else:
            stats['average_quality_score'] = 0
        return stats
    
    @classmethod
    def reset_gui_statistics(cls):
        """重置GUI数据处理统计信息"""
        cls._statistics = {
            'total_processed': 0,
            'validation_failures': 0,
            'cleaning_applied': 0,
            'data_quality_scores': []
        }
    
    @staticmethod
    def update_tree_view(tree, df):
        """更新树形视图数据"""
        from PyQt6.QtWidgets import QTreeWidgetItem
        
        # 确保验证功能已初始化
        if DataHandlers._validator is None:
            DataHandlers.initialize_validation()
        
        # 数据验证和清洗
        if df is not None and not df.empty:
            df, validation_info = DataHandlers._validate_and_clean_dataframe(df, 'tree_view')
            
            # 记录数据质量信息
            logger = get_logger(__name__)
            if validation_info['removed_rows'] > 0:
                logger.info(f"树形视图数据验证 - 移除{validation_info['removed_rows']}行无效数据，数据质量评分: {validation_info['quality_score']}")
        
        tree.clear()
        items = []
        
        # 添加行号计数
        row_count = 0
        
        for _, row in df.iterrows():
            row_count += 1
            
            # 处理主力净流入金额
            main_net = row.get('main_net_amount', 0)
            
            # 增强类型转换
            try:
                main_net = float(str(main_net).replace(',', ''))
            except:
                main_net = 0.0
            
            # 根据数值大小选择合适的单位
            if abs(main_net) < 0.01:  # 小于0.01亿（100万）显示为万单位
                main_net_str = f"{main_net * 10000:.2f}万"
            else:
                main_net_str = f"{main_net:.2f}亿" if main_net >= 0 else f"-{abs(main_net):.2f}亿"
            
            # 统一数值格式化
            strength_score = f"{row.get('strength_score', 0):.2f}"
            
            # 涨跌幅处理
            change_percent = row.get('change_percent', 0)
            change_str = f"{change_percent:.2f}%"
            
            # 创建树项
            item = QTreeWidgetItem([
                str(row.get('rank', row_count)),
                row.get('sector_name', row.get('concept_name', 'N/A')),
                strength_score,
                change_str,
                main_net_str
            ])
            
            # 设置文本对齐方式
            item.setTextAlignment(0, Qt.AlignmentFlag.AlignCenter)
            item.setTextAlignment(2, Qt.AlignmentFlag.AlignCenter)
            item.setTextAlignment(3, Qt.AlignmentFlag.AlignCenter)
            item.setTextAlignment(4, Qt.AlignmentFlag.AlignCenter)
            
            # 设置前三名背景色
            if row_count <= 3:
                for col in range(5):
                    item.setBackground(col, QColor(248, 249, 215))
            
            items.append(item)
        
        # 批量添加项目
        tree.addTopLevelItems(items)
    
    @staticmethod
    def clean_constituent_data(df):
        """清洗成分股数据"""
        if df is None or df.empty:
            return df
        
        # 确保验证功能已初始化
        if DataHandlers._validator is None:
            DataHandlers.initialize_validation()
        
        try:
            # 基础字段名称统一
            df = df.rename(columns={
                '涨跌幅度': '涨跌幅',
                'turnover_rate': '换手率',
                'latest_price': '最新价',
                'amount': '成交额',
                'volume': '成交量'
            })

            # 如果没有成交额字段但有成交量和最新价，可以尝试计算
            if '成交额' not in df.columns and '成交量' in df.columns and '最新价' in df.columns:
                try:
                    df['成交额'] = df['成交量'] * df['最新价']
                except:
                    # 如果计算失败，添加空列
                    df['成交额'] = float('nan')

            # 转换数值类型
            numeric_cols = ['最新价', '涨跌幅', '换手率', '成交额']
            for col in numeric_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col].astype(str).replace('%', '', regex=True), errors='coerce')
            
            # 应用高级数据验证和清洗
            cleaned_df, validation_info = DataHandlers._validate_and_clean_dataframe(df, 'constituent_data')
            
            # 记录清洗结果
            logger = get_logger(__name__)
            if validation_info['removed_rows'] > 0 or validation_info['cleaning_applied']:
                logger.info(f"成分股数据清洗完成 - 原始行数: {validation_info['original_rows']}, "
                          f"最终行数: {validation_info['final_rows']}, "
                          f"数据质量评分: {validation_info['quality_score']}")
            
            return cleaned_df
            
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"成分股数据清洗失败: {str(e)}")
            return df  # 返回原始数据
    
    @staticmethod
    def update_constituent_table(self, df):
        """更新成分股表格"""
        self.constituent_table.setRowCount(0)
        
        if df is None or df.empty:
            return
        
        # 确保验证功能已初始化
        if DataHandlers._validator is None:
            DataHandlers.initialize_validation()
        
        try:
            # 数据清洗和转换（包含高级验证和清洗）
            df = DataHandlers.clean_constituent_data(df)
            
            if df is None or df.empty:
                return
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"成分股表格更新失败: {str(e)}")
            return
        
        # 设置行数
        self.constituent_table.setRowCount(len(df))
        
        # 填充数据
        for row_idx, (_, row) in enumerate(df.iterrows()):
            # 股票代码
            code = str(row['代码']).split('.')[0]
            code_item = QTableWidgetItem(code)
            self.constituent_table.setItem(row_idx, 0, code_item)
            
            # 股票名称
            name_item = QTableWidgetItem(row['名称'])
            self.constituent_table.setItem(row_idx, 1, name_item)
            
            # 最新价
            latest_price = row.get('最新价', 'N/A')
            if isinstance(latest_price, (int, float)):
                price_item = QTableWidgetItem(f"{latest_price:.2f}")
            else:
                price_item = QTableWidgetItem(str(latest_price))
            price_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.constituent_table.setItem(row_idx, 2, price_item)
            
            # 成交额（单位：亿）
            amount = row.get('成交额', 'N/A')
            if isinstance(amount, (int, float)):
                # 转换为亿元单位并保留两位小数
                amount_in_billion = amount / 100000000  # 假设原始单位是元
                amount_item = QTableWidgetItem(f"{amount_in_billion:.2f}亿")
            else:
                amount_item = QTableWidgetItem(str(amount))
            amount_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.constituent_table.setItem(row_idx, 3, amount_item)
            
            # 涨跌幅
            change_percent = row.get('涨跌幅', 'N/A')
            if isinstance(change_percent, (int, float)):
                change_item = QTableWidgetItem(f"{change_percent:.2f}%")
                # 设置颜色
                if change_percent > 0:
                    change_item.setForeground(QColor(230, 57, 70))  # 红色
                elif change_percent < 0:
                    change_item.setForeground(QColor(42, 157, 143))  # 绿色
            else:
                change_item = QTableWidgetItem(str(change_percent))
            change_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.constituent_table.setItem(row_idx, 4, change_item)
            
            # 评分
            score = row.get('score', 50.0)
            if isinstance(score, (int, float)):
                score_item = QTableWidgetItem(f"{score:.2f}")
                # 根据评分设置颜色
                if score >= 80:
                    score_item.setForeground(QColor(0, 128, 0))  # 深绿色（优秀）
                elif score >= 60:
                    score_item.setForeground(QColor(0, 180, 0))  # 绿色（良好）
                elif score >= 40:
                    score_item.setForeground(QColor(128, 128, 128))  # 灰色（中性）
                elif score >= 20:
                    score_item.setForeground(QColor(180, 0, 0))  # 红色（较差）
                else:
                    score_item.setForeground(QColor(128, 0, 0))  # 深红色（极差）
            else:
                score_item = QTableWidgetItem(str(score))
            score_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.constituent_table.setItem(row_idx, 5, score_item)
    
    
    
    @staticmethod
    def update_money_effect_text(self, report):
        """更新赚钱效应评分文本"""
        # 清空文本框
        self.money_effect_text.clear()
        
        # 设置标题和评分 - 适配新的数据结构
        score = report.get('composite_score', 50)
        status = report.get('market_status', '未知')
        market_state = report.get('market_state', '未知')
        
        # 使用HTML格式优化显示效果
        self.money_effect_text.setHtml(f"<h3>赚钱效应评分: <span style='color:{'red' if score >= 70 else 'green' if score <= 30 else 'black'};'>{score}</span></h3>")
        self.money_effect_text.append(f"市场状态: {status} ({market_state}市)")
        self.money_effect_text.append(f"特征描述: {report.get('description', 'N/A')}")
        self.money_effect_text.append(f"操作建议: {report.get('suggestion', 'N/A')}")
        self.money_effect_text.append(f"建议仓位: {report.get('position_advice', 'N/A')}")
        self.money_effect_text.append(f"凯利仓位: {report.get('kelly_position', 'N/A')}")
        
        # 添加三维评分模型数据
        if 'dimension_scores' in report:
            self.money_effect_text.append("\n【三维评分模型】")
            dimensions = report['dimension_scores']
            
            # 动态权重显示
            if 'weights' in report:
                w = report['weights']
                self.money_effect_text.append(
                    f"当前权重: 市场动能({w['market_dynamics']}%) / "
                    f"资金博弈({w['fund_flow']}%)"
                )
            
            # 市场动能维度
            market = dimensions['market_dynamics']
            self.money_effect_text.append(
                f"市场动能: {market['score']} (涨跌家数比: {market['components']['涨跌家数比']}, "
                f"量价背离指数: {market['components']['量价背离指数']})"
            )
            
            # 资金博弈维度
            fund = dimensions['fund_flow']
            self.money_effect_text.append(
                f"资金博弈: {fund['score']} (涨停效应: {fund['components']['涨停效应']}, "
                f"波动效率: {fund['components']['波动效率']}, "
                f"资金轮动强度: {fund['components']['资金轮动强度']})"
            )
            
            # 
        
        # 添加风险指标
        if 'risk_indicators' in report:
            self.money_effect_text.append("\n【风险指标】")
            for key, value in report['risk_indicators'].items():
                self.money_effect_text.append(f"{key}: {value}")
        
        # 添加市场指标
        if 'market_metrics' in report:
            self.money_effect_text.append("\n【市场指标】")
            metrics = report['market_metrics']
            self.money_effect_text.append(f"上涨/下跌: {metrics.get('上涨家数', 0)}/{metrics.get('下跌家数', 0)}")
            self.money_effect_text.append(f"涨停/跌停: {metrics.get('涨停家数', 0)}/{metrics.get('跌停家数', 0)}")
            self.money_effect_text.append(f"市场活跃度: {metrics.get('活跃度', 0)}")
        
        # 添加行业偏好信息
        if 'positioning_advice' in report:
            self.money_effect_text.append("\n【配置建议】")
            pos = report['positioning_advice']
            self.money_effect_text.append(f"推荐仓位: {pos.get('推荐仓位', 'N/A')}")
            industry_prefs = pos.get('行业偏好', ['N/A'])
            self.money_effect_text.append(f"行业偏好: {', '.join(industry_prefs)}")
            self.money_effect_text.append(f"风险提示: {pos.get('风险提示', 'N/A')}")
    
    @staticmethod
    def format_volume(volume):
        """格式化成交量"""
        try:
            volume = float(volume)
            if volume < 1e4:  # 小于1万
                return f"{volume:.2f}手"
            elif volume < 1e8:  # 小于1亿
                return f"{volume / 1e4:.2f}万手"
            else:  # 大于等于1亿
                return f"{volume / 1e8:.2f}亿手"
        except Exception:
            return "N/A"
    
    def display_technical_analysis(self, df, code):
        """显示技术分析结果"""
        try:
            # 检查数据是否为空
            if df is None or df.empty:
                self.analysis_text.append("无法进行技术分析：数据为空")
                return
            
            # 确保验证功能已初始化
            if DataHandlers._validator is None:
                DataHandlers.initialize_validation()
            
            # 数据验证和清洗
            df, validation_info = DataHandlers._validate_and_clean_dataframe(df, 'technical_analysis')
            
            # 记录数据质量信息
            logger = get_logger(__name__)
            if validation_info['removed_rows'] > 0:
                logger.info(f"技术分析数据验证 - 移除{validation_info['removed_rows']}行无效数据，数据质量评分: {validation_info['quality_score']}")
            
            # 检查清洗后的数据是否为空
            if df is None or df.empty:
                self.analysis_text.append("数据验证后无有效数据进行技术分析")
                return
                
            # 确保有StockAnalyzer实例
            if not hasattr(self, 'stock_analyzer') or self.stock_analyzer is None:
                try:
                    from technical_analysis.stock_analyzer import StockAnalyzer
                    self.stock_analyzer = StockAnalyzer()
                except Exception as e:
                    self.analysis_text.append(f"初始化StockAnalyzer失败: {str(e)}")
                    return
            
            # 先清空分析文本区域
            self.analysis_text.clear()
            self.analysis_text.append(f"【{code}技术分析】\n")
            
            # 确保价格列存在
            price_column = '收盘' if '收盘' in df.columns else ('close' if 'close' in df.columns else None)
            if price_column is None:
                # 尝试查找可能的价格列名
                possible_columns = ['close', 'Close', '收盘', '收盘价']
                for col in possible_columns:
                    if col in df.columns:
                        price_column = col
                        break
                
                if price_column is None:
                    self.analysis_text.append("无法进行技术分析：找不到价格数据列")
                    return
            
            # 确保数据量足够
            if len(df) < 10:
                self.analysis_text.append("数据量不足，无法进行完整技术分析")
                self.analysis_text.append(f"当前数据量: {len(df)}行，建议至少需要30行")
                return
                
            # 获取最新价格
            try:
                current_price = df[price_column].iloc[-1]
                if pd.isna(current_price):
                    self.analysis_text.append("最新价格数据无效")
                    return
            except:
                self.analysis_text.append("获取价格数据失败")
                return
            
            # 1. 均线分析
            try:
                self.analysis_text.append("【均线分析】")
                self.analysis_text.append(f"当前价格: {current_price:.2f}")
                
                # 计算均线 (如果不存在)
                if 'MA5' not in df.columns:
                    df['MA5'] = df[price_column].rolling(window=5).mean()
                if 'MA10' not in df.columns:
                    df['MA10'] = df[price_column].rolling(window=10).mean()
                
                # 获取最新均线值
                ma5 = df['MA5'].iloc[-1] if not pd.isna(df['MA5'].iloc[-1]) else None
                ma10 = df['MA10'].iloc[-1] if not pd.isna(df['MA10'].iloc[-1]) else None
                
                if ma5 is not None:
                    self.analysis_text.append(f"5日均价: {ma5:.2f}")
                if ma10 is not None:
                    self.analysis_text.append(f"10日均价: {ma10:.2f}")
                
                # 添加均线趋势分析
                if ma5 is not None and ma10 is not None:
                    if ma5 > ma10:
                        self.analysis_text.append("短期均线位于长期均线之上，呈多头排列")
                    else:
                        self.analysis_text.append("短期均线位于长期均线之下，呈空头排列")
                
                # 尝试调用高级均线分析
                if hasattr(self.stock_analyzer, 'analyze_ma_trend'):
                    try:
                        ma_analysis = self.stock_analyzer.analyze_ma_trend(df)
                        if isinstance(ma_analysis, dict):
                            if 'ma_cross' in ma_analysis and ma_analysis['ma_cross']:
                                self.analysis_text.append(ma_analysis['ma_cross'])
                            if 'trend' in ma_analysis and ma_analysis['trend']:
                                self.analysis_text.append(ma_analysis['trend'])
                    except Exception as e:
                        self.analysis_text.append(f"高级均线分析失败: {str(e)}")
            except Exception as e:
                self.analysis_text.append(f"均线分析失败: {str(e)}")
            
            # 2. MACD分析
            try:
                # 检查是否需要计算MACD
                if 'DIF' not in df.columns or 'DEA' not in df.columns:
                    try:
                        if hasattr(self.stock_analyzer, 'calculate_macd'):
                            df = self.stock_analyzer.calculate_macd(df)
                    except Exception as e:
                        self.analysis_text.append(f"\n计算MACD失败: {str(e)}")
                
                # 如果MACD指标存在，进行分析
                if 'DIF' in df.columns and 'DEA' in df.columns:
                    dif = df['DIF'].iloc[-1]
                    dea = df['DEA'].iloc[-1]
                    
                    if not pd.isna(dif) and not pd.isna(dea):
                        self.analysis_text.append("\n【MACD分析】")
                        macd = df['MACD'].iloc[-1] if 'MACD' in df.columns else (dif - dea)
                        
                        self.analysis_text.append(f"DIF: {dif:.4f}")
                        self.analysis_text.append(f"DEA: {dea:.4f}")
                        self.analysis_text.append(f"MACD: {macd:.4f}")
                        
                        # 简单MACD判断
                        if dif > dea:
                            self.analysis_text.append("MACD金叉形态，看多信号")
                        else:
                            self.analysis_text.append("MACD死叉形态，看空信号")
                        
                        # 尝试调用高级MACD分析
                        if hasattr(self.stock_analyzer, 'analyze_macd'):
                            try:
                                macd_analysis = self.stock_analyzer.analyze_macd(df)
                                if isinstance(macd_analysis, dict):
                                    for key, value in macd_analysis.items():
                                        if key != 'histogram' and value:  # 跳过柱状图描述，只显示关键信号
                                            self.analysis_text.append(value)
                            except Exception as e:
                                self.analysis_text.append(f"高级MACD分析失败: {str(e)}")
            except Exception as e:
                self.analysis_text.append(f"\nMACD分析失败: {str(e)}")
            
            # 3. RSI分析
            try:
                # 尝试计算RSI
                if 'RSI6' not in df.columns and hasattr(self.stock_analyzer, 'calculate_rsi'):
                    try:
                        df = self.stock_analyzer.calculate_rsi(df)
                    except:
                        pass
                        
                if 'RSI6' in df.columns:
                    rsi6 = df['RSI6'].iloc[-1]
                    if not pd.isna(rsi6):
                        self.analysis_text.append("\n【RSI分析】")
                        self.analysis_text.append(f"RSI(6): {rsi6:.2f}")
                        
                        if rsi6 > 80:
                            self.analysis_text.append("RSI处于超买区域，可能出现回调")
                        elif rsi6 < 20:
                            self.analysis_text.append("RSI处于超卖区域，可能出现反弹")
                        
                        # 显示RSI趋势
                        if len(df) > 3:
                            rsi_values = df['RSI6'].dropna()
                            if len(rsi_values) > 3:
                                rsi_trend = rsi_values.diff(3).iloc[-1]
                                if not pd.isna(rsi_trend):
                                    if rsi_trend > 5:
                                        self.analysis_text.append("RSI呈上升趋势，动能增强")
                                    elif rsi_trend < -5:
                                        self.analysis_text.append("RSI呈下降趋势，动能减弱")
            except Exception as e:
                self.analysis_text.append(f"\nRSI分析失败: {str(e)}")
            
            # 4. 布林带分析
            try:
                needed_columns = ['BOLL_UP', 'BOLL_DOWN', 'BOLL_BANDWIDTH']
                if not all(x in df.columns for x in needed_columns) and hasattr(self.stock_analyzer, 'calculate_bollinger'):
                    try:
                        df = self.stock_analyzer.calculate_bollinger(df)
                    except:
                        pass
                        
                if all(x in df.columns for x in needed_columns):
                    boll_up = df['BOLL_UP'].iloc[-1]
                    boll_down = df['BOLL_DOWN'].iloc[-1]
                    bandwidth = df['BOLL_BANDWIDTH'].iloc[-1]
                    
                    if not pd.isna(boll_up) and not pd.isna(boll_down) and not pd.isna(bandwidth):
                        self.analysis_text.append("\n【布林带分析】")
                        self.analysis_text.append(f"布林带宽度: {bandwidth:.2f}%")
                        
                        if current_price > boll_up:
                            self.analysis_text.append("价格位于布林带上轨之上，呈强势状态")
                        elif current_price < boll_down:
                            self.analysis_text.append("价格位于布林带下轨之下，呈弱势状态")
                        else:
                            if boll_up == boll_down:  # 防止除零错误
                                band_position = 0.5
                            else:
                                band_position = (current_price - boll_down) / (boll_up - boll_down)
                            self.analysis_text.append(f"价格位于布林带内 {band_position*100:.1f}% 处")
                        
                        # 带宽分析
                        if bandwidth < 5:
                            self.analysis_text.append("布林带收窄，可能即将出现突破行情")
                        elif bandwidth > 20:
                            self.analysis_text.append("布林带扩张，波动性较大")
            except Exception as e:
                self.analysis_text.append(f"\n布林带分析失败: {str(e)}")
            
            # 5. 超短线信号分析
            try:
                # 检查是否有超短线分析方法
                if hasattr(self.stock_analyzer, 'analyze_ultrashort_signals'):
                    self.analysis_text.append("\n【超短线综合分析】")
                    
                    try:
                        signals = self.stock_analyzer.analyze_ultrashort_signals(df)
                        
                        if isinstance(signals, dict):
                            if 'error' in signals:
                                self.analysis_text.append(signals['error'])
                            else:
                                # 计算超短线指标得分
                                score = 0
                                buy_signals = 0
                                sell_signals = 0
                                
                                for signal_type, signal_info in signals.items():
                                    if not isinstance(signal_info, dict) or 'signal' not in signal_info:
                                        continue
                                        
                                    strength_value = {
                                        'strong': 2,
                                        'medium': 1,
                                        'weak': 0.5
                                    }
                                    
                                    value = strength_value.get(signal_info.get('strength', ''), 0)
                                    
                                    if signal_info['signal'] == '买入':
                                        score += value
                                        buy_signals += 1
                                    elif signal_info['signal'] == '卖出':
                                        score -= value
                                        sell_signals += 1
                                
                                # 显示评分和信号总结
                                if score >= 3:
                                    self.analysis_text.append(f"综合评分: {score:.1f} (高度看多)")
                                elif score >= 1.5:
                                    self.analysis_text.append(f"综合评分: {score:.1f} (看多)")
                                elif score <= -3:
                                    self.analysis_text.append(f"综合评分: {score:.1f} (高度看空)")
                                elif score <= -1.5:
                                    self.analysis_text.append(f"综合评分: {score:.1f} (看空)")
                                else:
                                    self.analysis_text.append(f"综合评分: {score:.1f} (中性)")
                                
                                self.analysis_text.append(f"买入信号: {buy_signals}个, 卖出信号: {sell_signals}个")
                                
                                # 显示主要信号
                                self.analysis_text.append("\n主要技术信号:")
                                signal_count = 0
                                for signal_type, signal_info in signals.items():
                                    if isinstance(signal_info, dict) and 'signal' in signal_info and 'description' in signal_info:
                                        self.analysis_text.append(f"• {signal_info['description']}")
                                        signal_count += 1
                                        if signal_count >= 5:  # 最多显示5个主要信号
                                            break
                    except Exception as e:
                        self.analysis_text.append(f"超短线分析异常: {str(e)}")
                    
                    # 添加动态止损建议
                    if 'STOP_LOSS' in df.columns and not pd.isna(df['STOP_LOSS'].iloc[-1]):
                        stop_loss = df['STOP_LOSS'].iloc[-1]
                        self.analysis_text.append(f"\n止损建议: {stop_loss:.2f}")
            except Exception as e:
                self.analysis_text.append(f"\n超短线分析失败: {str(e)}")
                
        except Exception as e:
            import traceback
            logger = get_logger(__name__)
            logger.error(f"技术分析失败: {str(e)}\n{traceback.format_exc()}")
            self.analysis_text.append(f"\n技术分析过程中出现错误: {str(e)}")