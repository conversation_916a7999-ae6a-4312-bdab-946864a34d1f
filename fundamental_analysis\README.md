# 基本面分析模块

本模块提供了对股票基本面的全面分析功能，包括财务数据分析、行业分析和概念分析等。

## 模块结构

```
fundamental_analysis/
├── stock_selection.py    # 股票选择器
├── sector_rank.py        # 行业排名分析
├── concept_rank.py       # 概念排名分析
├── sector_rank.csv       # 行业排名数据
├── concept_rank.csv      # 概念排名数据
└── __init__.py
```

## 主要功能

### 1. 股票选择器 (stock_selection.py)

根据基本面数据筛选优质股票。

**主要功能**：
- 财务指标筛选：基于ROE、毛利率、净利率等财务指标筛选
- 成长性分析：分析营收增长、利润增长等成长指标
- 估值分析：计算PE、PB、PS等估值指标并进行筛选
- 质量评分：综合多维度指标计算股票质量评分
- 财务健康度：分析资产负债率、现金流等财务健康指标

### 2. 行业排名分析 (sector_rank.py)

分析各行业的表现和排名。

**主要功能**：
- 行业涨跌幅排名：计算各行业的涨跌幅并排名
- 行业资金流向：分析行业资金净流入/流出情况
- 行业估值对比：对比不同行业的估值水平
- 行业景气度：评估行业的整体景气程度
- 行业轮动分析：分析行业轮动规律和当前阶段

### 3. 概念排名分析 (concept_rank.py)

分析各概念板块的表现和排名。

**主要功能**：
- 概念涨跌幅排名：计算各概念板块的涨跌幅并排名
- 概念热度分析：基于交易量和关注度分析概念热度
- 概念关联度：分析不同概念之间的关联关系
- 热点概念追踪：追踪市场热点概念的变化
- 概念持续性评估：评估热点概念的持续性

## 使用示例

### 股票基本面筛选

```python
from fundamental_analysis import StockSelector

# 创建股票选择器
selector = StockSelector()

# 使用预设策略筛选股票
quality_stocks = selector.select_stocks(strategy="quality")
print(f"优质股票数量: {len(quality_stocks)}")
for stock in quality_stocks[:5]:  # 显示前5只
    print(f"{stock['code']} {stock['name']}: 得分 {stock['score']}")

# 自定义条件筛选
custom_stocks = selector.filter_by_criteria(
    min_roe=15,              # ROE大于15%
    min_profit_growth=10,    # 净利润增长率大于10%
    max_pe=30,               # PE小于30
    min_dividend_yield=2,    # 股息率大于2%
    max_debt_ratio=60        # 资产负债率小于60%
)

print(f"满足自定义条件的股票: {len(custom_stocks)}")
for stock in custom_stocks[:5]:
    print(f"{stock['code']} {stock['name']}")

# 获取特定股票的基本面分析
stock_analysis = selector.analyze_stock("600519")
print(f"股票: {stock_analysis['name']} ({stock_analysis['code']})")
print(f"行业: {stock_analysis['industry']}")
print(f"市值: {stock_analysis['market_cap']}亿元")
print(f"市盈率(TTM): {stock_analysis['pe_ttm']}")
print(f"市净率: {stock_analysis['pb']}")
print(f"ROE: {stock_analysis['roe']}%")
print(f"股息率: {stock_analysis['dividend_yield']}%")
```

### 行业分析

```python
from fundamental_analysis import SectorAnalyzer

# 创建行业分析器
sector_analyzer = SectorAnalyzer()

# 获取行业排名
sectors = sector_analyzer.get_sector_ranking(
    sort_by="increase",  # 按涨幅排序
    period="week"        # 周涨幅
)

print("行业周涨幅排名:")
for i, sector in enumerate(sectors[:10], 1):  # 前10名
    print(f"{i}. {sector['name']}: {sector['increase']}%")

# 获取特定行业详细分析
industry = "计算机"
industry_analysis = sector_analyzer.analyze_sector(industry)

print(f"\n{industry}行业分析:")
print(f"行业涨幅: {industry_analysis['increase']}%")
print(f"行业估值: PE {industry_analysis['average_pe']}, PB {industry_analysis['average_pb']}")
print(f"资金流向: {industry_analysis['capital_flow']}亿元")
print(f"行业景气度: {industry_analysis['prosperity_level']}")

# 获取行业轮动状态
rotation = sector_analyzer.get_sector_rotation()
print(f"\n当前行业轮动阶段: {rotation['current_stage']}")
print("上升行业:")
for sector in rotation['rising_sectors'][:3]:
    print(f"- {sector}")
print("下降行业:")
for sector in rotation['falling_sectors'][:3]:
    print(f"- {sector}")
```

### 概念分析

```python
from fundamental_analysis import ConceptAnalyzer

# 创建概念分析器
concept_analyzer = ConceptAnalyzer()

# 获取概念排名
concepts = concept_analyzer.get_concept_ranking(
    sort_by="increase",  # 按涨幅排序
    period="day"         # 日涨幅
)

print("概念日涨幅排名:")
for i, concept in enumerate(concepts[:10], 1):  # 前10名
    print(f"{i}. {concept['name']}: {concept['increase']}%")

# 获取热门概念
hot_concepts = concept_analyzer.get_hot_concepts(top_n=5)
print("\n当前热门概念:")
for i, concept in enumerate(hot_concepts, 1):
    print(f"{i}. {concept['name']}: 热度 {concept['heat_score']}")
    print(f"   相关股票: {', '.join([s['name'] for s in concept['related_stocks'][:3]])}")

# 获取概念关联分析
concept = "人工智能"
concept_related = concept_analyzer.get_related_concepts(concept)
print(f"\n与{concept}相关的概念:")
for related in concept_related[:5]:
    print(f"- {related['name']}: 相关度 {related['correlation_score']}%")
```

### 综合使用示例

```python
from fundamental_analysis import FundamentalAnalyzer

# 创建综合分析器
analyzer = FundamentalAnalyzer()

# 行业景气度和估值分析
industry_analysis = analyzer.get_industry_prosperity_and_valuation()
print("行业景气度与估值分析:")
for industry in industry_analysis[:5]:
    print(f"{industry['name']}: 景气度 {industry['prosperity']}，估值水平 {industry['valuation_level']}")

# 发现低估值高成长股票
value_growth_stocks = analyzer.find_value_growth_stocks(top_n=10)
print("\n低估值高成长股:")
for i, stock in enumerate(value_growth_stocks, 1):
    print(f"{i}. {stock['name']} ({stock['code']}): PE {stock['pe_ttm']}, 净利润增长 {stock['profit_growth']}%")

# 获取行业龙头股
industry = "医药生物"
leading_stocks = analyzer.get_industry_leading_stocks(industry, top_n=3)
print(f"\n{industry}行业龙头股:")
for i, stock in enumerate(leading_stocks, 1):
    print(f"{i}. {stock['name']} ({stock['code']}): 市占率 {stock['market_share']}%, 行业排名 {stock['industry_rank']}")
```

## 数据来源

本模块的数据来源包括：

- **财务数据**: 上市公司季报、年报等公开财务数据
- **行业分类**: 申万/证监会行业分类体系
- **概念分类**: 东方财富/同花顺概念分类体系
- **市场数据**: 实时和历史交易数据

## 性能优化

- **数据缓存**: 实现了本地数据缓存，减少重复网络请求
- **并行计算**: 支持多线程并行计算大量股票的指标
- **增量更新**: 财务数据支持增量更新，减少数据处理量
- **内存优化**: 优化了大量数据处理时的内存使用

## 高级用法

### 1. 自定义筛选策略

```python
from fundamental_analysis import StockSelector

# 自定义筛选策略
class CustomStrategy:
    def __init__(self, min_roe=10, max_pe=25):
        self.min_roe = min_roe
        self.max_pe = max_pe
    
    def apply(self, stock_data):
        # 实现筛选逻辑
        if stock_data['roe'] < self.min_roe:
            return False, "ROE过低"
        if stock_data['pe_ttm'] > self.max_pe:
            return False, "PE过高"
        # 计算综合得分
        score = (stock_data['roe'] / 2) + (50 / stock_data['pe_ttm'])
        return True, score

# 创建选择器并应用自定义策略
selector = StockSelector()
custom_strategy = CustomStrategy(min_roe=12, max_pe=20)
results = selector.apply_custom_strategy(custom_strategy)

for stock in sorted(results, key=lambda x: x['score'], reverse=True)[:5]:
    print(f"{stock['code']} {stock['name']}: 得分 {stock['score']:.2f}")
```

### 2. 行业轮动策略

```python
from fundamental_analysis import SectorRotation
from datetime import datetime, timedelta

# 创建行业轮动分析器
rotation = SectorRotation()

# 获取历史轮动数据
start_date = datetime.now() - timedelta(days=365)
end_date = datetime.now()
history = rotation.get_historical_rotation(start_date, end_date)

# 分析轮动规律
patterns = rotation.analyze_rotation_patterns(history)
print("行业轮动规律:")
for pattern in patterns:
    print(f"规律: {pattern['description']}")
    print(f"周期: {pattern['cycle_days']}天")
    print(f"可信度: {pattern['confidence']}%")

# 生成行业轮动策略
strategy = rotation.generate_rotation_strategy(
    lookback_days=90,
    holding_period=20,
    top_n_sectors=3
)

print("\n当前行业轮动策略:")
print(f"当前阶段: {strategy['current_stage']}")
print("推荐行业:")
for sector in strategy['recommended_sectors']:
    print(f"- {sector['name']}: 预期收益 {sector['expected_return']}%")
```

## 注意事项

1. 本模块提供的分析结果仅供参考，不构成投资建议
2. 财务数据的时效性取决于上市公司的报告周期，通常存在一定滞后性
3. 概念和主题的变化较快，分析结果可能随市场热点快速变化
4. 基本面数据作为投资决策的参考，建议与技术分析结合使用

## 依赖项

本模块依赖以下主要库：

```
pandas>=1.3.0
numpy>=1.20.0
matplotlib>=3.4.0
requests>=2.25.0
lxml>=4.6.0
sqlalchemy>=1.4.0  # 用于数据存储
```

可以通过以下命令安装依赖：

```bash
pip install pandas numpy matplotlib requests lxml sqlalchemy
``` 