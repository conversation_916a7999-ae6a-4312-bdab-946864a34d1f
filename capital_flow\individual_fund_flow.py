# capital_flow/capital_flow_rank.py
import akshare as ak
import pandas as pd
from concurrent.futures import ThreadPoolExecutor


def _process_single_rank(indicator: str) -> pd.DataFrame:
    """处理单个时间周期的数据获取（修复单位转换错误）"""
    try:
        df = ak.stock_individual_fund_flow_rank(indicator=indicator)
        if df.empty:
            return pd.DataFrame()

        # 列名标准化
        df.columns = [col.replace(' ', '').replace('-', '_') for col in df.columns]

        # 过滤无效数据和特殊板块
        valid_condition = (
                (df['代码'] != '-') &
                (df['代码'].str.match(r'^\d{6}$')) &
                (~df['代码'].str.startswith(('688', '300', '301')))
        )
        df = df[valid_condition].copy()

        # 转换主力净流入金额（元→亿）
        main_net_col = f'{indicator}主力净流入_净额'
        if main_net_col in df.columns:
            # 修复单位转换系数：1亿=100000000元
            df[main_net_col] = pd.to_numeric(
                df[main_net_col].replace('--', '0').replace('-', '0'),
                errors='coerce'
            ).fillna(0) / 100000000  # 修正此处

            # 重命名列显示单位
            df.rename(columns={main_net_col: f'{indicator}主力净流入(亿)'}, inplace=True)

        # 转换其他数值字段
        numeric_cols = [
            col for col in df.columns
            if any(kw in col for kw in ['净占比', '最新价', '涨跌幅'])
        ]

        for col in numeric_cols:
            if '净占比' in col:
                df[col] = pd.to_numeric(
                    df[col].str.replace('%', '', regex=False)
                    .replace('--', '0')
                    .replace('-', '0'),
                    errors='coerce'
                ) / 100
            else:
                df[col] = pd.to_numeric(
                    df[col].replace('--', '0').replace('-', '0'),
                    errors='coerce'
                ).fillna(0)

        # 添加周期标识
        df['周期'] = indicator
        return df

    except Exception as e:
        print(f"获取{indicator}数据失败: {str(e)}")
        return pd.DataFrame()


def get_multi_period_rank(indicators: list = None) -> dict:
    """
    获取多周期资金流数据（增强版）
    :return: 字典格式数据，包含各周期主力净流入金额（单位：亿）
    """
    default_indicators = ["今日", "3日", "5日"]
    indicators = indicators or default_indicators

    results = {}

    with ThreadPoolExecutor(max_workers=3) as executor:
        future_to_indicator = {
            executor.submit(_process_single_rank, indicator): indicator
            for indicator in indicators
        }

        for future in future_to_indicator:
            indicator = future_to_indicator[future]
            try:
                df = future.result()
                if not df.empty:
                    results[indicator] = df
            except Exception as e:
                print(f"处理{indicator}数据时出错: {str(e)}")

    return results


# 示例用法
if __name__ == "__main__":
    from time import time

    # 测试获取并展示主力资金数据
    start = time()
    data_dict = get_multi_period_rank()
    print(f"\n总耗时: {time() - start:.2f}秒")

    # 展示各周期主力资金流向
    for period, df in data_dict.items():
        if not df.empty:
            print(f"\n{period}主力资金净流入TOP3:")
            print(df[['代码', '名称', f'{period}主力净流入(亿)', f'{period}涨跌幅']]
                  .sort_values(f'{period}主力净流入(亿)', ascending=False)
                  .head(10))
        else:
            print(f"\n{period}无有效数据")
