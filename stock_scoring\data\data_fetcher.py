import akshare as ak
import pandas as pd
from typing import Optional, Union, Dict, Any, List
from datetime import datetime, timedelta
import logging

# 导入数据验证和清洗模块
from utils.data_validator import validate_data, DataValidator
from utils.validation_config import get_current_validator, get_current_cleaning_rules
from utils.data_cleaner import DataCleaner, BASIC_STOCK_CLEANER

# 配置日志
logger = logging.getLogger(__name__)

class DataFetcher:
    """数据获取模块，统一管理从AKShare获取数据，集成数据验证和清洗功能"""
    
    def __init__(self, enable_validation: bool = True, enable_cleaning: bool = True):
        """初始化数据获取器
        
        Parameters
        ----------
        enable_validation : bool, optional
            是否启用数据验证，默认为True
        enable_cleaning : bool, optional
            是否启用数据清洗，默认为True
        """
        self.enable_validation = enable_validation
        self.enable_cleaning = enable_cleaning
        self.validator = get_current_validator() if enable_validation else None
        self.cleaner = BASIC_STOCK_CLEANER if enable_cleaning else None
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'validation_failures': 0,
            'cleaning_applied': 0,
            'data_quality_score': 0.0
        }
    
    def get_stock_hist(self, symbol: str, 
                      start_date: str, 
                      end_date: Optional[str] = None,
                      period: str = 'daily',
                      adjust: str = 'qfq') -> pd.DataFrame:
        """
        获取个股历史行情数据
        
        Parameters
        ----------
        symbol : str
            股票代码，如'000001'
        start_date : str
            开始日期，格式'YYYYMMDD'
        end_date : str, optional
            结束日期，格式'YYYYMMDD'，默认为当前日期
        period : str, optional
            周期，可选'daily'/'weekly'/'monthly'，默认为'daily'
        adjust : str, optional
            复权类型，可选'qfq'/'hfq'/'None'，默认为'qfq'
            
        Returns
        -------
        pd.DataFrame
            包含OHLCV数据的DataFrame
        """
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        self.stats['total_requests'] += 1
        
        try:
            # 验证输入参数
            if self.enable_validation:
                input_data = {
                    'stock_code': symbol,
                    'start_date': start_date,
                    'end_date': end_date
                }
                validation_result = self.validator.validate(input_data)
                if not validation_result.is_valid:
                    self.stats['validation_failures'] += 1
                    logger.warning(f"输入参数验证失败 {symbol}: {validation_result.get_error_summary()}")
                    # 根据验证级别决定是否继续
                    if validation_result.has_errors():
                        return pd.DataFrame()
            
            # 获取原始数据
            df = ak.stock_zh_a_hist(
                symbol=symbol,
                period=period,
                start_date=start_date,
                end_date=end_date,
                adjust=adjust
            )
            
            if df.empty:
                logger.warning(f"获取到空数据集 {symbol}")
                return df
            
            # 重命名列以保持一致性
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'pct_change',
                '涨跌额': 'change',
                '换手率': 'turnover'
            })
            
            # 设置日期为索引
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            
            # 数据验证和清洗
            df = self._validate_and_clean_dataframe(df, symbol)
            
            if not df.empty:
                self.stats['successful_requests'] += 1
                logger.info(f"成功获取并处理股票数据 {symbol}: {len(df)} 条记录")
            
            return df
            
        except Exception as e:
            logger.error(f"获取股票{symbol}数据失败: {str(e)}")
            return pd.DataFrame()
            
    def get_market_data(self, symbol: str = '000001',
                       start_date: str = None,
                       end_date: str = None) -> pd.DataFrame:
        """
        获取市场指数数据
        
        Parameters
        ----------
        symbol : str, optional
            指数代码，默认为上证指数'000001'
        start_date : str, optional
            开始日期，格式'YYYYMMDD'
        end_date : str, optional
            结束日期，格式'YYYYMMDD'
            
        Returns
        -------
        pd.DataFrame
            包含指数数据的DataFrame
        """
        return self.get_stock_hist(symbol, start_date, end_date)
        
    def get_sector_data(self, symbol: str,
                       start_date: str = None,
                       end_date: str = None) -> pd.DataFrame:
        """
        获取板块指数数据
        
        Parameters
        ----------
        symbol : str
            板块指数代码
        start_date : str, optional
            开始日期，格式'YYYYMMDD'
        end_date : str, optional
            结束日期，格式'YYYYMMDD'
            
        Returns
        -------
        pd.DataFrame
            包含板块指数数据的DataFrame
        """
        return self.get_stock_hist(symbol, start_date, end_date)
        
    def get_stock_list(self) -> pd.DataFrame:
        """
        获取A股股票列表
        
        Returns
        -------
        pd.DataFrame
            包含所有A股股票信息的DataFrame
        """
        self.stats['total_requests'] += 1
        
        try:
            df = ak.stock_zh_a_spot_em()
            
            if not df.empty:
                # 对股票列表进行基本验证
                if self.enable_validation:
                    df = self._validate_stock_list(df)
                
                self.stats['successful_requests'] += 1
                logger.info(f"成功获取股票列表: {len(df)} 只股票")
            
            return df
        except Exception as e:
            logger.error(f"获取股票列表失败: {str(e)}")
            return pd.DataFrame()
    
    def _validate_and_clean_dataframe(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """验证和清洗DataFrame数据
        
        Parameters
        ----------
        df : pd.DataFrame
            原始数据DataFrame
        symbol : str
            股票代码
            
        Returns
        -------
        pd.DataFrame
            验证和清洗后的DataFrame
        """
        if df.empty:
            return df
        
        original_count = len(df)
        
        try:
            # 转换为记录列表进行验证
            records = df.reset_index().to_dict('records')
            
            # 数据验证
            if self.enable_validation and self.validator:
                validated_records = []
                validation_failures = 0
                
                for record in records:
                    validation_result = self.validator.validate(record)
                    if validation_result.is_valid or not validation_result.has_errors():
                        validated_records.append(record)
                    else:
                        validation_failures += 1
                        logger.debug(f"记录验证失败 {symbol}: {validation_result.get_error_summary()}")
                
                records = validated_records
                if validation_failures > 0:
                    self.stats['validation_failures'] += validation_failures
                    logger.info(f"数据验证完成 {symbol}: 移除 {validation_failures} 条无效记录")
            
            # 数据清洗
            if self.enable_cleaning and self.cleaner and records:
                cleaned_records, cleaning_report = self.cleaner.clean_data(records)
                
                if cleaned_records:
                    records = cleaned_records
                    self.stats['cleaning_applied'] += 1
                    logger.info(f"数据清洗完成 {symbol}: 清洗率 {cleaning_report.cleaning_rate:.2%}")
                else:
                    logger.warning(f"数据清洗后为空 {symbol}")
            
            # 转换回DataFrame
            if records:
                cleaned_df = pd.DataFrame(records)
                if 'date' in cleaned_df.columns:
                    cleaned_df['date'] = pd.to_datetime(cleaned_df['date'])
                    cleaned_df.set_index('date', inplace=True)
                
                # 计算数据质量评分
                quality_score = len(cleaned_df) / original_count if original_count > 0 else 0.0
                self.stats['data_quality_score'] = (
                    self.stats['data_quality_score'] * (self.stats['successful_requests'] - 1) + quality_score
                ) / self.stats['successful_requests'] if self.stats['successful_requests'] > 0 else quality_score
                
                return cleaned_df
            else:
                logger.warning(f"验证和清洗后数据为空 {symbol}")
                return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"数据验证和清洗失败 {symbol}: {str(e)}")
            return df  # 返回原始数据
    
    def _validate_stock_list(self, df: pd.DataFrame) -> pd.DataFrame:
        """验证股票列表数据
        
        Parameters
        ----------
        df : pd.DataFrame
            股票列表DataFrame
            
        Returns
        -------
        pd.DataFrame
            验证后的股票列表DataFrame
        """
        if df.empty:
            return df
        
        try:
            original_count = len(df)
            
            # 基本的股票代码验证
            if '代码' in df.columns:
                # 过滤无效的股票代码
                valid_codes = df['代码'].astype(str).str.match(r'^\d{6}$')
                df = df[valid_codes]
                
                removed_count = original_count - len(df)
                if removed_count > 0:
                    logger.info(f"股票列表验证: 移除 {removed_count} 条无效代码记录")
            
            return df
        
        except Exception as e:
            logger.error(f"股票列表验证失败: {str(e)}")
            return df
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据获取统计信息
        
        Returns
        -------
        Dict[str, Any]
            统计信息字典
        """
        stats = self.stats.copy()
        
        # 计算成功率
        if stats['total_requests'] > 0:
            stats['success_rate'] = stats['successful_requests'] / stats['total_requests']
        else:
            stats['success_rate'] = 0.0
        
        # 计算验证失败率
        if stats['total_requests'] > 0:
            stats['validation_failure_rate'] = stats['validation_failures'] / stats['total_requests']
        else:
            stats['validation_failure_rate'] = 0.0
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'validation_failures': 0,
            'cleaning_applied': 0,
            'data_quality_score': 0.0
        }
        logger.info("数据获取统计信息已重置")
    
    def configure_validation(self, enable: bool = True, validator: Optional[DataValidator] = None):
        """配置数据验证
        
        Parameters
        ----------
        enable : bool, optional
            是否启用验证，默认为True
        validator : DataValidator, optional
            自定义验证器，如果为None则使用默认验证器
        """
        self.enable_validation = enable
        if enable:
            self.validator = validator if validator else get_current_validator()
        else:
            self.validator = None
        
        logger.info(f"数据验证已{'启用' if enable else '禁用'}")
    
    def configure_cleaning(self, enable: bool = True, cleaner: Optional[DataCleaner] = None):
        """配置数据清洗
        
        Parameters
        ----------
        enable : bool, optional
            是否启用清洗，默认为True
        cleaner : DataCleaner, optional
            自定义清洗器，如果为None则使用默认清洗器
        """
        self.enable_cleaning = enable
        if enable:
            self.cleaner = cleaner if cleaner else BASIC_STOCK_CLEANER
        else:
            self.cleaner = None
        
        logger.info(f"数据清洗已{'启用' if enable else '禁用'}")


# 创建默认的数据获取器实例
default_data_fetcher = DataFetcher()

# 向后兼容的静态方法
class LegacyDataFetcher:
    """向后兼容的静态方法包装器"""
    
    @staticmethod
    def get_stock_hist(symbol: str, 
                      start_date: str, 
                      end_date: Optional[str] = None,
                      period: str = 'daily',
                      adjust: str = 'qfq') -> pd.DataFrame:
        """向后兼容的静态方法"""
        return default_data_fetcher.get_stock_hist(symbol, start_date, end_date, period, adjust)
    
    @staticmethod
    def get_market_data(symbol: str = '000001',
                       start_date: str = None,
                       end_date: str = None) -> pd.DataFrame:
        """向后兼容的静态方法"""
        return default_data_fetcher.get_market_data(symbol, start_date, end_date)
    
    @staticmethod
    def get_sector_data(symbol: str,
                       start_date: str = None,
                       end_date: str = None) -> pd.DataFrame:
        """向后兼容的静态方法"""
        return default_data_fetcher.get_sector_data(symbol, start_date, end_date)
    
    @staticmethod
    def get_stock_list() -> pd.DataFrame:
        """向后兼容的静态方法"""
        return default_data_fetcher.get_stock_list()

# 为了向后兼容，保留原有的类名
DataFetcher_Legacy = LegacyDataFetcher