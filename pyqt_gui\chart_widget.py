# chart_widget.py
import sys
import pandas as pd
import numpy as np
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QApplication
from PyQt6.QtCore import Qt

# 导入PyQtGraph库用于绘制图表
try:
    import pyqtgraph as pg
except ImportError:
    print("请安装PyQtGraph库: pip install pyqtgraph")
    sys.exit(1)


class StockChartWidget(QWidget):
    """股票图表组件"""
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置背景色为白色
        pg.setConfigOption('background', 'w')
        pg.setConfigOption('foreground', 'k')
        
        # 创建布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建绘图窗口
        self.plot_widget = pg.PlotWidget()
        self.layout.addWidget(self.plot_widget)
        
        # 设置图表属性
        self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
        self.plot_widget.setLabel('left', 'Price')
        self.plot_widget.setLabel('bottom', 'Date')
        
        # 初始化图表项
        self.candle_item = None
        self.volume_item = None
        self.ma5_item = None
        self.ma10_item = None
        self.ma20_item = None
        
    def plot_k_line(self, df):
        """绘制K线图"""
        if df.empty:
            return
            
        # 清除现有图表
        self.plot_widget.clear()
        
        # 准备数据
        dates = np.arange(len(df))
        
        # 创建K线图
        self._plot_candles(df, dates)
        
        # 绘制均线
        self._plot_ma_lines(df, dates)
        
        # 设置X轴刻度
        self._set_x_axis_ticks(df, dates)
        
    def _plot_candles(self, df, dates):
        """绘制K线蜡烛图"""
        # 创建蜡烛图数据
        candle_data = []
        for i, (_, row) in enumerate(df.iterrows()):
            # 蜡烛图数据格式: (时间索引, 开盘价, 最高价, 最低价, 收盘价)
            candle = (dates[i], row['开盘'], row['收盘'], row['最低'], row['最高'])
            candle_data.append(candle)
        
        # 创建蜡烛图
        self.candle_item = pg.CandlestickItem(candle_data)
        self.plot_widget.addItem(self.candle_item)
    
    def _plot_ma_lines(self, df, dates):
        """绘制均线"""
        # 绘制MA5
        if 'MA5' in df.columns:
            self.ma5_item = pg.PlotDataItem(
                dates, df['MA5'].values, 
                pen=pg.mkPen('#FF9500', width=1.5),
                name='MA5'
            )
            self.plot_widget.addItem(self.ma5_item)
        
        # 绘制MA10
        if 'MA10' in df.columns:
            self.ma10_item = pg.PlotDataItem(
                dates, df['MA10'].values, 
                pen=pg.mkPen('#00A3E0', width=1.5),
                name='MA10'
            )
            self.plot_widget.addItem(self.ma10_item)
        
        # 绘制MA20
        if 'MA20' in df.columns:
            self.ma20_item = pg.PlotDataItem(
                dates, df['MA20'].values, 
                pen=pg.mkPen('#7B4173', width=1.5),
                name='MA20'
            )
            self.plot_widget.addItem(self.ma20_item)
    
    def _set_x_axis_ticks(self, df, dates):
        """设置X轴刻度"""
        # 设置X轴刻度
        date_axis = pg.AxisItem(orientation='bottom')
        date_strings = [date.strftime('%m-%d') for date in pd.to_datetime(df.index)]
        date_dict = {i: date for i, date in zip(dates, date_strings)}
        date_axis.setTicks([date_dict.items()])
        
        # 更新X轴
        self.plot_widget.getPlotItem().setAxisItems({'bottom': date_axis})
        
    def plot_volume(self, df):
        """绘制成交量图表"""
        if df.empty:
            return
            
        # 创建成交量视图
        volume_view = pg.PlotWidget()
        volume_view.setMaximumHeight(100)
        volume_view.showGrid(x=True, y=True, alpha=0.3)
        volume_view.setLabel('left', 'Volume')
        
        # 准备数据
        dates = np.arange(len(df))
        volumes = df['成交量'].values
        
        # 创建成交量柱状图
        volume_bars = pg.BarGraphItem(
            x=dates, height=volumes, width=0.8,
            brush='#5470c6'
        )
        volume_view.addItem(volume_bars)
        
        # 添加到布局
        self.layout.addWidget(volume_view)
        
    def plot_macd(self, df):
        """绘制MACD图表"""
        if df.empty or 'DIF' not in df.columns or 'DEA' not in df.columns:
            return
            
        # 创建MACD视图
        macd_view = pg.PlotWidget()
        macd_view.setMaximumHeight(100)
        macd_view.showGrid(x=True, y=True, alpha=0.3)
        macd_view.setLabel('left', 'MACD')
        
        # 准备数据
        dates = np.arange(len(df))
        dif = df['DIF'].values
        dea = df['DEA'].values
        macd = df['MACD'].values if 'MACD' in df.columns else dif - dea
        
        # 绘制DIF线
        dif_curve = pg.PlotDataItem(
            dates, dif,
            pen=pg.mkPen('#FF9500', width=1.5),
            name='DIF'
        )
        
        # 绘制DEA线
        dea_curve = pg.PlotDataItem(
            dates, dea,
            pen=pg.mkPen('#00A3E0', width=1.5),
            name='DEA'
        )
        
        # 绘制MACD柱状图
        macd_bars = []
        for i, val in enumerate(macd):
            color = '#e63946' if val >= 0 else '#2a9d8f'  # 红色为正，绿色为负
            bar = pg.BarGraphItem(
                x=[dates[i]], height=[val], width=0.6,
                brush=color
            )
            macd_bars.append(bar)
            macd_view.addItem(bar)
        
        # 添加曲线
        macd_view.addItem(dif_curve)
        macd_view.addItem(dea_curve)
        
        # 添加到布局
        self.layout.addWidget(macd_view)
        
    def plot_kdj(self, df):
        """绘制KDJ图表"""
        if df.empty or 'K' not in df.columns or 'D' not in df.columns or 'J' not in df.columns:
            return
            
        # 创建KDJ视图
        kdj_view = pg.PlotWidget()
        kdj_view.setMaximumHeight(100)
        kdj_view.showGrid(x=True, y=True, alpha=0.3)
        kdj_view.setLabel('left', 'KDJ')
        
        # 准备数据
        dates = np.arange(len(df))
        k_values = df['K'].values
        d_values = df['D'].values
        j_values = df['J'].values
        
        # 绘制K线
        k_curve = pg.PlotDataItem(
            dates, k_values,
            pen=pg.mkPen('#FF9500', width=1.5),
            name='K'
        )
        
        # 绘制D线
        d_curve = pg.PlotDataItem(
            dates, d_values,
            pen=pg.mkPen('#00A3E0', width=1.5),
            name='D'
        )
        
        # 绘制J线
        j_curve = pg.PlotDataItem(
            dates, j_values,
            pen=pg.mkPen('#7B4173', width=1.5),
            name='J'
        )
        
        # 添加曲线
        kdj_view.addItem(k_curve)
        kdj_view.addItem(d_curve)
        kdj_view.addItem(j_curve)
        
        # 添加到布局
        self.layout.addWidget(kdj_view)
        
    def clear(self):
        """清除所有图表"""
        # 清除主图表
        self.plot_widget.clear()
        
        # 移除所有子视图
        for i in reversed(range(1, self.layout.count())):
            widget = self.layout.itemAt(i).widget()
            if widget:
                widget.deleteLater()


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 创建测试数据
    dates = pd.date_range(start='2023-01-01', periods=30)
    data = {
        '开盘': np.random.normal(100, 5, 30),
        '最高': np.random.normal(105, 5, 30),
        '最低': np.random.normal(95, 5, 30),
        '收盘': np.random.normal(100, 5, 30),
        '成交量': np.random.normal(10000, 3000, 30),
    }
    df = pd.DataFrame(data, index=dates)
    
    # 计算均线
    df['MA5'] = df['收盘'].rolling(5).mean()
    df['MA10'] = df['收盘'].rolling(10).mean()
    
    # 创建图表组件
    chart = StockChartWidget()
    chart.resize(800, 600)
    chart.plot_k_line(df)
    chart.plot_volume(df)
    chart.show()
    
    sys.exit(app.exec())