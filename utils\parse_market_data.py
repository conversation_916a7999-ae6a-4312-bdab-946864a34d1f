"""
解析股票行情数据

用于读取和解析股票行情数据文件，查看其格式并输出内容
"""

import os
import pandas as pd
import json
from utils.logging_config import get_logger

# 配置日志
logger = get_logger(__name__)

def read_market_data_file(file_path):
    """
    读取并解析股票行情数据文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        解析后的数据
    """
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return None
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        logger.info(f"成功读取文件，大小: {len(content)} 字节")
        logger.info(f"文件内容预览:\n{content[:500]}...")
        
        # 尝试判断文件格式
        if content.strip().startswith('{') and content.strip().endswith('}'):
            logger.info("检测到可能的JSON格式")
            try:
                data = json.loads(content)
                logger.info(f"成功解析为JSON格式，顶级键: {list(data.keys())}")
                return data
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败: {str(e)}")
        
        # 尝试CSV解析
        try:
            # 尝试不同的分隔符
            for sep in [',', '\t', '|', ';']:
                try:
                    df = pd.read_csv(file_path, sep=sep)
                    if len(df.columns) > 1:  # 成功解析为多列
                        logger.info(f"成功解析为CSV格式(分隔符: '{sep}')，列名: {list(df.columns)}")
                        logger.info(f"数据预览:\n{df.head(3)}")
                        return df
                except Exception:
                    continue
        except Exception as e:
            logger.warning(f"CSV解析失败: {str(e)}")
            
        # 如果以上方法都失败，则尝试按行解析
        lines = content.splitlines()
        logger.info(f"解析为文本行，共 {len(lines)} 行")
        
        # 查看前几行数据结构
        for i, line in enumerate(lines[:5]):
            logger.info(f"第 {i+1} 行: {line}")
            
        return lines
    
    except Exception as e:
        logger.error(f"解析文件时出错: {str(e)}")
        return None

if __name__ == "__main__":
    # 解析指定的文件
    file_path = r"e:\PythonProject\提示词\个股评分\个股行情数据.txt"
    logger.info(f"开始解析文件: {file_path}")
    
    data = read_market_data_file(file_path)
    
    if data is not None:
        logger.info("文件解析完成")
        
        # 根据解析结果的类型提供更多信息
        if isinstance(data, dict):
            # 如果是字典(JSON)，查看结构
            for key, value in data.items():
                if isinstance(value, dict):
                    logger.info(f"键 '{key}' 包含子字典，子键: {list(value.keys())}")
                elif isinstance(value, list):
                    logger.info(f"键 '{key}' 包含列表，长度: {len(value)}")
                    if len(value) > 0 and isinstance(value[0], dict):
                        logger.info(f"  列表中的第一项是字典，键: {list(value[0].keys())}")
                else:
                    logger.info(f"键 '{key}' 包含值: {value}")
        
        elif isinstance(data, pd.DataFrame):
            # 如果是DataFrame，展示更多统计信息
            logger.info(f"数据形状: {data.shape}，列名: {list(data.columns)}")
            logger.info(f"数据类型:\n{data.dtypes}")
            
            # 如果数据包含日期列，检查日期范围
            date_cols = [col for col in data.columns if 'date' in col.lower() or 'time' in col.lower() or '日期' in col or '时间' in col]
            if date_cols:
                for col in date_cols:
                    try:
                        data[col] = pd.to_datetime(data[col])
                        logger.info(f"日期列 '{col}' 范围: {data[col].min()} 到 {data[col].max()}")
                    except:
                        pass
    else:
        logger.error("文件解析失败")