#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
组件管理器

负责管理系统中所有组件的生命周期，实现统一的组件创建、获取和销毁机制
"""

from typing import Dict, Any, Type, Optional, TypeVar, Generic, cast

# 配置日志
from utils.logging_config import get_logger
logger = get_logger(__name__)

# 定义组件类型变量
T = TypeVar('T')

class ComponentManager:
    """
    组件管理器
    
    负责管理系统中所有组件的生命周期，实现统一的组件创建、获取和销毁机制
    """
    
    # 单例实例
    _instance = None
    
    # 组件实例字典
    _components: Dict[str, Any] = {}
    
    # 组件配置字典
    _component_configs: Dict[str, Dict[str, Any]] = {}
    
    def __new__(cls):
        """实现单例模式"""
        if cls._instance is None:
            cls._instance = super(ComponentManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化组件管理器"""
        # 如果已经初始化过则跳过
        if getattr(self, '_initialized', False):
            return
            
        logger.info("初始化组件管理器")
        
        # 标记为已初始化
        self._initialized = True
    
    def register_component_config(self, component_type: str, config: Dict[str, Any]) -> None:
        """
        注册组件配置
        
        Args:
            component_type: 组件类型名称
            config: 组件配置
        """
        self._component_configs[component_type] = config
        logger.debug(f"已注册组件配置: {component_type}")
    
    def get_component_config(self, component_type: str) -> Dict[str, Any]:
        """
        获取组件配置
        
        Args:
            component_type: 组件类型名称
            
        Returns:
            组件配置字典
        """
        return self._component_configs.get(component_type, {})
    
    def register_component(self, component_type: str, instance: Any) -> None:
        """
        注册组件实例
        
        Args:
            component_type: 组件类型名称
            instance: 组件实例
        """
        self._components[component_type] = instance
        logger.debug(f"已注册组件实例: {component_type}")
    
    def get_component(self, component_type: str, component_class: Type[T] = None, config: Dict[str, Any] = None) -> Optional[T]:
        """
        获取组件实例，如果不存在则创建
        
        Args:
            component_type: 组件类型名称
            component_class: 组件类
            config: 组件配置，如果为None则使用已注册的配置
            
        Returns:
            组件实例
        """
        # 如果组件已存在，直接返回
        if component_type in self._components:
            return cast(T, self._components[component_type])
        
        # 如果没有提供组件类，无法创建
        if component_class is None:
            logger.warning(f"未提供组件类，无法创建组件: {component_type}")
            return None
        
        # 获取组件配置
        if config is None:
            config = self.get_component_config(component_type)
        
        # 创建组件实例
        try:
            logger.info(f"创建组件实例: {component_type}")
            instance = component_class(config)
            self.register_component(component_type, instance)
            return cast(T, instance)
        except Exception as e:
            logger.error(f"创建组件实例失败: {component_type}, 错误: {str(e)}")
            return None
    
    def has_component(self, component_type: str) -> bool:
        """
        检查组件是否存在
        
        Args:
            component_type: 组件类型名称
            
        Returns:
            组件是否存在
        """
        return component_type in self._components
    
    def remove_component(self, component_type: str) -> bool:
        """
        移除组件
        
        Args:
            component_type: 组件类型名称
            
        Returns:
            是否成功移除
        """
        if component_type in self._components:
            del self._components[component_type]
            logger.debug(f"已移除组件: {component_type}")
            return True
        return False
    
    def clear_components(self) -> None:
        """清空所有组件"""
        self._components.clear()
        logger.debug("已清空所有组件")
    
    def get_all_component_types(self) -> list:
        """
        获取所有已注册的组件类型
        
        Returns:
            组件类型列表
        """
        return list(self._components.keys())

# 全局组件管理器实例
component_manager = ComponentManager()
