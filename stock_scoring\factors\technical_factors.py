import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from scipy import stats
from scipy.signal import find_peaks
from scipy.fft import fft, fftfreq
from scipy.stats import entropy
from scipy.spatial.distance import pdist, squareform
from scipy.optimize import curve_fit
import akshare as ak
from datetime import datetime, timedelta
from utils.logging_config import get_logger

# 配置日志
logger = get_logger(__name__)

class TechnicalFactors:
    """技术因子计算模块"""
    
    def __init__(self, data: Optional[pd.DataFrame] = None, symbol: str = None, 
                 start_date: str = None, end_date: str = None, adjust: str = "qfq"):
        """
        初始化技术因子计算器
        
        Parameters
        ----------
        data : pd.DataFrame, optional
            包含OHLCV数据的DataFrame，必须包含以下列：
            - date: 日期
            - open: 开盘价
            - high: 最高价
            - low: 最低价
            - close: 收盘价
            - volume: 成交量
            - amount: 成交额
            如果为None，则会使用symbol, start_date, end_date参数从AKShare获取数据
        symbol : str, optional
            股票代码，如"000001"，当data为None时必须提供
        start_date : str, optional
            开始日期，格式为"YYYYMMDD"，如"20240101"，当data为None时必须提供
        end_date : str, optional
            结束日期，格式为"YYYYMMDD"，如"20240531"，当data为None时必须提供
        adjust : str, optional
            复权方式，可选值为："qfq"（前复权）、"hfq"（后复权）、None（不复权）
            默认为"qfq"（前复权）
        """
        # 如果未提供数据，则从AKShare获取
        if data is None:
            if symbol is None or start_date is None:
                raise ValueError("当data为None时，必须提供symbol和start_date参数")
            
            # 如果未提供end_date，则使用当前日期
            if end_date is None:
                end_date = datetime.now().strftime("%Y%m%d")
                
            try:
                logger.info(f"从AKShare获取股票{symbol}的历史数据，时间范围: {start_date}至{end_date}")
                data = self._get_stock_data_from_akshare(symbol, start_date, end_date, adjust)
            except Exception as e:
                logger.error(f"从AKShare获取数据失败: {e}")
                raise
        
        self.data = data.copy()
        self._validate_data()
    
    def _get_stock_data_from_akshare(self, symbol: str, start_date: str, 
                                     end_date: str, adjust: str = "qfq") -> pd.DataFrame:
        """
        从AKShare获取股票历史数据
        
        Parameters
        ----------
        symbol : str
            股票代码，如"000001"
        start_date : str
            开始日期，格式为"YYYYMMDD"，如"20240101"
        end_date : str
            结束日期，格式为"YYYYMMDD"，如"20240531"
        adjust : str, optional
            复权方式，可选值为："qfq"（前复权）、"hfq"（后复权）、None（不复权）
            默认为"qfq"（前复权）
            
        Returns
        -------
        pd.DataFrame
            包含OHLCV数据的DataFrame
        """
        try:
            # 使用AKShare的stock_zh_a_hist函数获取A股历史数据
            stock_data = ak.stock_zh_a_hist(
                symbol=symbol,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                adjust=adjust
            )
            
            # 将字段名称转换为程序中使用的格式
            column_mapping = {
                "日期": "date",
                "开盘": "open",
                "收盘": "close",
                "最高": "high",
                "最低": "low",
                "成交量": "volume",
                "成交额": "amount",
                "振幅": "amplitude",
                "涨跌幅": "pct_change",
                "涨跌额": "change",
                "换手率": "turnover_rate"
            }
            
            # 重命名列
            stock_data = stock_data.rename(columns=column_mapping)
            
            # 将date字段转换为datetime类型
            stock_data['date'] = pd.to_datetime(stock_data['date'])
            
            # 确保数据按日期排序
            stock_data = stock_data.sort_values('date')
            
            # 将成交量转换为int类型（AKShare返回的成交量单位为手）
            stock_data['volume'] = stock_data['volume'].astype(int)
            
            return stock_data
            
        except Exception as e:
            logger.error(f"获取股票{symbol}历史数据失败: {e}")
            raise
        
    def _validate_data(self):
        """验证数据完整性"""
        required_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要的列: {missing_columns}")
            
    @classmethod
    def from_akshare(cls, symbol: str, start_date: str, end_date: str = None, adjust: str = "qfq"):
        """
        从AKShare获取数据并创建TechnicalFactors实例
        
        Parameters
        ----------
        symbol : str
            股票代码，如"000001"
        start_date : str
            开始日期，格式为"YYYYMMDD"，如"20240101"
        end_date : str, optional
            结束日期，格式为"YYYYMMDD"，如"20240531"
            如果不提供，则使用当前日期
        adjust : str, optional
            复权方式，可选值为："qfq"（前复权）、"hfq"（后复权）、None（不复权）
            默认为"qfq"（前复权）
            
        Returns
        -------
        TechnicalFactors
            技术因子计算类实例
        """
        return cls(data=None, symbol=symbol, start_date=start_date, end_date=end_date, adjust=adjust)
        
    def calculate_all_factors(self, window: int = 30) -> Dict[str, pd.Series]:
        """
        计算所有技术因子（短线模式）
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为30
            
        Returns
        -------
        Dict[str, pd.Series]
            包含所有技术因子的字典
        """
        factors = {}
        
        # 使用短期技术因子
        factors['ma_5'] = self.short_term_ma(5)
        factors['ma_10'] = self.short_term_ma(10)
        factors['rsi_5'] = self.short_term_rsi(5)
        factors['rsi_9'] = self.short_term_rsi(9)
        factors['fast_macd'] = self.fast_macd()
        factors['short_bollinger'] = self.short_bollinger_bands()
        factors['price_deviation'] = self.price_deviation_rate()
        factors['trend_strength'] = self.trend_strength(10)  # 缩短窗口
        factors['momentum'] = self.momentum(5)  # 缩短窗口
        factors['volatility'] = self.volatility(10)  # 缩短窗口
        
        return factors
        
    def trend_strength(self, window: int) -> pd.Series:
        """趋势强度因子"""
        close = self.data['close']
        returns = close.pct_change()
        
        # 计算趋势强度
        trend = close.rolling(window).mean()
        trend_strength = (close - trend) / trend
        
        return trend_strength
        
    def momentum(self, window: int) -> pd.Series:
        """动量因子"""
        close = self.data['close']
        returns = close.pct_change()
        
        # 计算动量
        momentum = returns.rolling(window).sum()
        
        return momentum
        
    def acceleration(self, window: int) -> pd.Series:
        """加速度因子"""
        close = self.data['close']
        returns = close.pct_change()
        
        # 计算加速度
        momentum = returns.rolling(window).sum()
        acceleration = momentum.diff()
        
        return acceleration
        
    def volatility(self, window: int) -> pd.Series:
        """波动率因子"""
        close = self.data['close']
        returns = close.pct_change()
        
        # 计算波动率
        volatility = returns.rolling(window).std()
        
        return volatility
        
    def amplitude(self, window: int) -> pd.Series:
        """振幅因子"""
        high = self.data['high']
        low = self.data['low']
        
        # 计算振幅
        amplitude = (high - low) / low
        
        return amplitude
        
    def atr(self, window: int) -> pd.Series:
        """ATR因子"""
        high = self.data['high']
        low = self.data['low']
        close = self.data['close']
        
        # 计算TR
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # 计算ATR
        atr = tr.rolling(window).mean()
        
        return atr
        
    def volume_trend(self, window: int) -> pd.Series:
        """成交量趋势因子"""
        volume = self.data['volume']
        
        # 计算成交量趋势
        volume_ma = volume.rolling(window).mean()
        volume_trend = (volume - volume_ma) / volume_ma
        
        return volume_trend
        
    def volume_ratio(self, window: int) -> pd.Series:
        """成交量比率因子"""
        volume = self.data['volume']
        
        # 计算成交量比率
        volume_ma = volume.rolling(window).mean()
        volume_ratio = volume / volume_ma
        
        return volume_ratio
        
    def obv(self) -> pd.Series:
        """OBV因子"""
        close = self.data['close']
        volume = self.data['volume']
        
        # 计算OBV
        obv = (np.sign(close.diff()) * volume).cumsum()
        
        return obv
        
    def rsi(self, window: int) -> pd.Series:
        """RSI因子"""
        close = self.data['close']
        returns = close.pct_change()
        
        # 计算RSI
        gains = returns.where(returns > 0, 0)
        losses = -returns.where(returns < 0, 0)
        
        avg_gain = gains.rolling(window).mean()
        avg_loss = losses.rolling(window).mean()
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
        
    def macd(self, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.Series:
        """MACD因子"""
        close = self.data['close']
        
        # 计算EMA
        ema_fast = close.ewm(span=fast, adjust=False).mean()
        ema_slow = close.ewm(span=slow, adjust=False).mean()
        
        # 计算MACD线
        macd_line = ema_fast - ema_slow
        
        # 计算信号线
        signal_line = macd_line.ewm(span=signal, adjust=False).mean()
        
        # 计算MACD柱状图
        macd_hist = macd_line - signal_line
        
        return macd_hist
        
    def kdj(self, window: int = 9, k_period: int = 3, d_period: int = 3) -> pd.Series:
        """KDJ因子"""
        high = self.data['high']
        low = self.data['low']
        close = self.data['close']
        
        # 计算RSV
        low_min = low.rolling(window).min()
        high_max = high.rolling(window).max()
        rsv = 100 * (close - low_min) / (high_max - low_min)
        
        # 计算K值
        k = rsv.ewm(span=k_period, adjust=False).mean()
        
        # 计算D值
        d = k.ewm(span=d_period, adjust=False).mean()
        
        # 计算J值
        j = 3 * k - 2 * d
        
        return j
        
    def bollinger_bands(self, window: int = 20, num_std: float = 2.0) -> pd.Series:
        """布林带因子"""
        close = self.data['close']
        
        # 计算中轨线（简单移动平均线）
        middle = close.rolling(window).mean()
        
        # 计算标准差
        std = close.rolling(window).std()
        
        # 计算上轨线和下轨线
        upper = middle + num_std * std
        lower = middle - num_std * std
        
        # 计算价格在带宽中的位置
        bb_position = (close - lower) / (upper - lower)
        
        return bb_position
        
    def pattern_score(self, window: int = 20) -> pd.Series:
        """形态得分因子"""
        close = self.data['close']
        high = self.data['high']
        low = self.data['low']
        
        # 初始化得分
        score = pd.Series(0, index=close.index)
        
        # 寻找局部极值点
        peaks, _ = find_peaks(close)
        troughs, _ = find_peaks(-close)
        
        # 计算形态得分
        for i in range(len(peaks)-1):
            # 双顶形态
            if abs(close[peaks[i]] - close[peaks[i+1]]) / close[peaks[i]] < 0.02:
                score[peaks[i+1]] += 1
            # 头肩顶形态
            if i < len(peaks)-2:
                if close[peaks[i]] > close[peaks[i+1]] < close[peaks[i+2]]:
                    score[peaks[i+1]] -= 1
                    
        for i in range(len(troughs)-1):
            # 双底形态
            if abs(close[troughs[i]] - close[troughs[i+1]]) / close[troughs[i]] < 0.02:
                score[troughs[i+1]] -= 1
            # 头肩底形态
            if i < len(troughs)-2:
                if close[troughs[i]] < close[troughs[i+1]] > close[troughs[i+2]]:
                    score[troughs[i+1]] += 1
        
        return score
        
    def support_resistance(self, window: int = 20) -> pd.Series:
        """支撑阻力因子"""
        close = self.data['close']
        high = self.data['high']
        low = self.data['low']
        
        def find_levels(series: pd.Series, window: int) -> Tuple[float, float]:
            """寻找支撑位和阻力位"""
            rolling_min = series.rolling(window).min()
            rolling_max = series.rolling(window).max()
            
            support = rolling_min.rolling(window).mean()
            resistance = rolling_max.rolling(window).mean()
            
            return support, resistance
        
        # 计算支撑位和阻力位
        support, resistance = find_levels(close, window)
        
        # 计算价格在支撑阻力区间的位置
        position = (close - support) / (resistance - support)
        
        return position
        
    def hurst_exponent(self, window: int = 30) -> pd.Series:
        """
        计算Hurst指数（修正版）
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为30
            
        Returns
        -------
        pd.Series
            Hurst指数序列
        """
        def _calculate_hurst(series: pd.Series) -> float:
            """计算单个序列的Hurst指数"""
            try:
                # 计算累积偏差
                deviations = series - series.mean()
                cumulative_deviations = deviations.cumsum()
                
                # 计算极差
                R = cumulative_deviations.max() - cumulative_deviations.min()
                
                # 计算标准差
                S = series.std()
                
                # 计算Hurst指数
                if S == 0:
                    return 0.5  # 返回中性值
                
                # 使用修正公式
                n = len(series)
                hurst = np.log(R/S) / np.log(n)
                
                # 应用统计修正
                if n < 50:
                    hurst = hurst * (1 + 0.1/n)
                
                return max(0, min(1, hurst))  # 确保结果在[0,1]范围内
            except Exception:
                return 0.5  # 发生错误时返回中性值
        
        # 对收盘价序列应用滚动窗口计算
        hurst_values = self.data['close'].rolling(window=window).apply(_calculate_hurst)
        return hurst_values
        
    def lyapunov_exponent(self, window: int) -> pd.Series:
        """Lyapunov指数因子"""
        close = self.data['close']
        returns = close.pct_change()
        
        # 计算Lyapunov指数
        def lyap(ts):
            n = len(ts)
            if n < window:
                return np.nan
                
            # 重构相空间
            emb_dim = 2
            tau = 1
            emb = np.array([ts[i:i+emb_dim*tau:tau] for i in range(n-emb_dim*tau+1)])
            
            # 计算最近邻点
            dist = squareform(pdist(emb))
            np.fill_diagonal(dist, np.inf)
            min_dist = np.min(dist, axis=1)
            
            # 计算Lyapunov指数
            lyap = np.mean(np.log(min_dist))
            
            return lyap
            
        lyap_values = returns.rolling(window).apply(lyap)
        
        return lyap_values
        
    def entropy(self, window: int = 30) -> pd.Series:
        """熵因子"""
        close = self.data['close']
        returns = close.pct_change()
        
        def calculate_entropy(series: pd.Series) -> float:
            """计算单个序列的熵"""
            try:
                # 计算直方图
                hist, _ = np.histogram(series, bins=20, density=True)
                
                # 计算熵
                entropy_value = -np.sum(hist * np.log2(hist + 1e-10))
                
                return entropy_value
            except Exception:
                return 0.0
        
        # 对收益率序列应用滚动窗口计算
        entropy_values = returns.rolling(window).apply(calculate_entropy)
        return entropy_values
        
    def phase_space_entropy(self, window: int = 30, m: int = 2, tau: int = 1) -> pd.Series:
        """
        计算相空间熵因子
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为30
        m : int, optional
            嵌入维数，默认为2
        tau : int, optional
            延迟时间，默认为1
            
        Returns
        -------
        pd.Series
            相空间熵序列
        """
        def _calculate_entropy(series: pd.Series) -> float:
            """计算单个序列的相空间熵"""
            try:
                # 重构相空间
                n = len(series)
                if n < m * tau:
                    return 0.0
                
                # 创建相空间向量
                vectors = []
                for i in range(n - (m-1)*tau):
                    vector = [series[i + j*tau] for j in range(m)]
                    vectors.append(vector)
                
                # 计算向量间的距离
                distances = pdist(vectors)
                
                # 计算距离的直方图
                hist, _ = np.histogram(distances, bins=20, density=True)
                
                # 计算熵
                entropy = -np.sum(hist * np.log2(hist + 1e-10))
                
                return entropy
            except Exception:
                return 0.0
        
        # 对收盘价序列应用滚动窗口计算
        entropy_values = self.data['close'].rolling(window=window).apply(_calculate_entropy)
        return entropy_values
        
    def multifractal_spectrum_width(self, window: int = 30, q_min: float = -5, q_max: float = 5, q_step: float = 0.5) -> pd.Series:
        """
        计算多重分形谱宽度因子
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为30
        q_min : float, optional
            q的最小值，默认为-5
        q_max : float, optional
            q的最大值，默认为5
        q_step : float, optional
            q的步长，默认为0.5
            
        Returns
        -------
        pd.Series
            多重分形谱宽度序列
        """
        def _calculate_spectrum_width(series: pd.Series) -> float:
            """计算单个序列的多重分形谱宽度"""
            try:
                # 计算局部波动
                returns = np.diff(np.log(series))
                local_volatility = np.abs(returns)
                
                # 计算q阶矩
                q_values = np.arange(q_min, q_max + q_step, q_step)
                moments = []
                
                for q in q_values:
                    if q == 0:
                        moment = np.mean(np.log(local_volatility))
                    else:
                        moment = np.log(np.mean(local_volatility ** q)) / q
                    moments.append(moment)
                
                # 计算α(q)
                alpha = np.gradient(moments, q_values)
                
                # 计算f(α)
                f_alpha = q_values * alpha - moments
                
                # 计算谱宽度
                width = np.max(alpha) - np.min(alpha)
                
                return width
            except Exception:
                return 0.0
        
        # 对收盘价序列应用滚动窗口计算
        spectrum_width = self.data['close'].rolling(window=window).apply(_calculate_spectrum_width)
        return spectrum_width
        
    def persistent_homology(self, window: int = 30, epsilon: float = 0.1) -> pd.Series:
        """
        计算持久同调因子
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为30
        epsilon : float, optional
            距离阈值，默认为0.1
            
        Returns
        -------
        pd.Series
            持久同调序列
        """
        def _calculate_persistence(series: pd.Series) -> float:
            """计算单个序列的持久同调"""
            try:
                # 将价格序列转换为点云
                points = np.column_stack((np.arange(len(series)), series))
                
                # 计算点之间的距离矩阵
                dist_matrix = squareform(pdist(points))
                
                # 计算连通性
                connected = dist_matrix <= epsilon
                
                # 计算连通分量的数量
                n_components = 0
                visited = np.zeros(len(series), dtype=bool)
                
                for i in range(len(series)):
                    if not visited[i]:
                        n_components += 1
                        queue = [i]
                        visited[i] = True
                        
                        while queue:
                            current = queue.pop(0)
                            for j in range(len(series)):
                                if connected[current, j] and not visited[j]:
                                    visited[j] = True
                                    queue.append(j)
                
                # 计算持久性
                persistence = n_components / len(series)
                
                return persistence
            except Exception:
                return 0.0
        
        # 对收盘价序列应用滚动窗口计算
        persistence_values = self.data['close'].rolling(window=window).apply(_calculate_persistence)
        return persistence_values
        
    def wasserstein_distance(self, window: int = 30) -> pd.Series:
        """
        计算Wasserstein距离因子
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为30
            
        Returns
        -------
        pd.Series
            Wasserstein距离序列
        """
        def _calculate_wasserstein(series: pd.Series) -> float:
            """计算单个序列的Wasserstein距离"""
            try:
                # 计算价格变化
                changes = np.diff(series)
                
                # 计算经验分布
                hist1, bins = np.histogram(changes, bins=20, density=True)
                
                # 计算标准正态分布
                x = (bins[:-1] + bins[1:]) / 2
                hist2 = stats.norm.pdf(x, loc=0, scale=np.std(changes))
                
                # 计算累积分布函数
                cdf1 = np.cumsum(hist1) * np.diff(bins)[0]
                cdf2 = np.cumsum(hist2) * np.diff(bins)[0]
                
                # 计算Wasserstein距离
                distance = np.sum(np.abs(cdf1 - cdf2)) * np.diff(bins)[0]
                
                return distance
            except Exception:
                return 0.0
        
        # 对收盘价序列应用滚动窗口计算
        wasserstein_values = self.data['close'].rolling(window=window).apply(_calculate_wasserstein)
        return wasserstein_values
        
    def fisher_information(self, window: int = 30) -> pd.Series:
        """
        计算Fisher信息因子
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为30
            
        Returns
        -------
        pd.Series
            Fisher信息序列
        """
        def _calculate_fisher(series: pd.Series) -> float:
            """计算单个序列的Fisher信息"""
            try:
                # 计算价格变化
                changes = np.diff(series)
                
                # 计算概率密度函数
                hist, bins = np.histogram(changes, bins=20, density=True)
                
                # 计算概率密度函数的导数
                dx = np.diff(bins)[0]
                derivative = np.gradient(hist, dx)
                
                # 计算Fisher信息
                fisher = np.sum(derivative**2 / (hist + 1e-10)) * dx
                
                return fisher
            except Exception:
                return 0.0
        
        # 对收盘价序列应用滚动窗口计算
        fisher_values = self.data['close'].rolling(window=window).apply(_calculate_fisher)
        return fisher_values
        
    def exponential_family_curvature(self, window: int = 30) -> pd.Series:
        """
        计算指数族曲率因子
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为30
            
        Returns
        -------
        pd.Series
            指数族曲率序列
        """
        def _calculate_curvature(series: pd.Series) -> float:
            """计算单个序列的指数族曲率"""
            try:
                # 计算价格变化
                changes = np.diff(series)
                
                # 计算均值和方差
                mu = np.mean(changes)
                sigma2 = np.var(changes)
                
                if sigma2 == 0:
                    return 0.0
                
                # 计算自然参数
                theta1 = mu / sigma2
                theta2 = -1 / (2 * sigma2)
                
                # 计算Fisher信息矩阵
                I11 = 1 / sigma2
                I12 = mu / sigma2**2
                I22 = (mu**2 + sigma2) / (2 * sigma2**3)
                
                # 计算曲率
                curvature = np.sqrt(I11 * I22 - I12**2)
                
                return curvature
            except Exception:
                return 0.0
        
        # 对收盘价序列应用滚动窗口计算
        curvature_values = self.data['close'].rolling(window=window).apply(_calculate_curvature)
        return curvature_values
        
    # 短线专用技术指标方法
    def short_term_ma(self, window: int) -> pd.Series:
        """
        短期移动平均线
        
        Parameters
        ----------
        window : int
            移动平均窗口大小
            
        Returns
        -------
        pd.Series
            移动平均线序列
        """
        close = self.data['close']
        ma = close.rolling(window).mean()
        
        # 计算价格相对于移动平均线的偏离度
        deviation = (close - ma) / ma
        
        return deviation
        
    def short_term_rsi(self, window: int) -> pd.Series:
        """
        短期RSI指标
        
        Parameters
        ----------
        window : int
            RSI计算窗口
            
        Returns
        -------
        pd.Series
            RSI序列
        """
        close = self.data['close']
        delta = close.diff()
        
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        avg_gain = gain.rolling(window).mean()
        avg_loss = loss.rolling(window).mean()
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
        
    def fast_macd(self, fast: int = 5, slow: int = 10, signal: int = 3) -> pd.Series:
        """
        快速MACD指标
        
        Parameters
        ----------
        fast : int, optional
            快线周期，默认为5
        slow : int, optional
            慢线周期，默认为10
        signal : int, optional
            信号线周期，默认为3
            
        Returns
        -------
        pd.Series
            MACD柱状图序列
        """
        close = self.data['close']
        
        ema_fast = close.ewm(span=fast, adjust=False).mean()
        ema_slow = close.ewm(span=slow, adjust=False).mean()
        
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal, adjust=False).mean()
        macd_hist = macd_line - signal_line
        
        return macd_hist
        
    def short_bollinger_bands(self, window: int = 10, num_std: float = 1.5) -> pd.Series:
        """
        短期布林带指标
        
        Parameters
        ----------
        window : int, optional
            布林带窗口，默认为10
        num_std : float, optional
            标准差倍数，默认为1.5
            
        Returns
        -------
        pd.Series
            价格在布林带中的位置
        """
        close = self.data['close']
        
        middle = close.rolling(window).mean()
        std = close.rolling(window).std()
        
        upper = middle + num_std * std
        lower = middle - num_std * std
        
        # 计算价格在带宽中的位置
        bb_position = (close - lower) / (upper - lower)
        
        return bb_position
        
    def price_deviation_rate(self, window: int = 5) -> pd.Series:
        """
        价格偏离率
        
        Parameters
        ----------
        window : int, optional
            计算窗口，默认为5
            
        Returns
        -------
        pd.Series
            价格偏离率序列
        """
        close = self.data['close']
        high = self.data['high']
        low = self.data['low']
        
        # 计算价格中位数
        median_price = (high + low + close) / 3
        
        # 计算移动平均
        ma = median_price.rolling(window).mean()
        
        # 计算偏离率
        deviation_rate = (close - ma) / ma * 100
        
        return deviation_rate