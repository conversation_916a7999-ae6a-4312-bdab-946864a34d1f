#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
标准化引擎模块 - 解决数据过度平滑化问题

本模块提供多种标准化策略，旨在保持数据的原始差异性，
避免传统标准化方法导致的评分趋同问题。

主要功能：
1. 自适应标准化：根据数据分布特征选择最佳标准化方法
2. 差异保持：确保数据标准化后仍保持足够的区分度
3. 多尺度评分：同时提供多种标准化结果供选择
4. 质量监控：实时监控标准化效果

Author: AI Assistant
Date: 2024
"""

import numpy as np
import pandas as pd
from scipy import stats
from sklearn.preprocessing import RobustScaler, QuantileTransformer
import logging
from typing import Union, Tuple, Dict, Optional
import warnings
warnings.filterwarnings('ignore')


class DistributionAnalyzer:
    """数据分布分析器"""
    
    @staticmethod
    def analyze_distribution(data: pd.Series) -> Dict[str, float]:
        """分析数据分布特征"""
        if data.empty or data.isna().all():
            return {
                'skewness': 0,
                'kurtosis': 0,
                'cv': 0,
                'range_ratio': 0,
                'concentration': 1.0
            }
        
        # 清理数据
        clean_data = data.dropna()
        if len(clean_data) < 2:
            return {
                'skewness': 0,
                'kurtosis': 0,
                'cv': 0,
                'range_ratio': 0,
                'concentration': 1.0
            }
        
        try:
            # 偏度和峰度
            skewness = stats.skew(clean_data)
            kurtosis = stats.kurtosis(clean_data)
            
            # 变异系数
            mean_val = clean_data.mean()
            std_val = clean_data.std()
            cv = std_val / abs(mean_val) if abs(mean_val) > 1e-8 else 0
            
            # 范围比率（极值与中位数的比率）
            median_val = clean_data.median()
            range_val = clean_data.max() - clean_data.min()
            range_ratio = range_val / abs(median_val) if abs(median_val) > 1e-8 else 0
            
            # 数据集中度（基于四分位距）
            q75, q25 = np.percentile(clean_data, [75, 25])
            iqr = q75 - q25
            concentration = 1.0 / (1.0 + iqr / abs(median_val)) if abs(median_val) > 1e-8 else 1.0
            
            return {
                'skewness': float(skewness) if not np.isnan(skewness) else 0,
                'kurtosis': float(kurtosis) if not np.isnan(kurtosis) else 0,
                'cv': float(cv) if not np.isnan(cv) else 0,
                'range_ratio': float(range_ratio) if not np.isnan(range_ratio) else 0,
                'concentration': float(concentration) if not np.isnan(concentration) else 1.0
            }
        except Exception as e:
            logger = get_logger(__name__)
            logger.warning(f"分布分析异常: {e}")
            return {
                'skewness': 0,
                'kurtosis': 0,
                'cv': 0,
                'range_ratio': 0,
                'concentration': 1.0
            }


class DifferenceEnhancer:
    """差异增强器 - 核心优化模块"""
    
    @staticmethod
    def enhance_differences(data: pd.Series, enhancement_factor: float = 1.5) -> pd.Series:
        """增强数据差异性"""
        if data.empty or data.isna().all():
            return data
        
        try:
            # 计算中心值
            center = data.median()
            
            # 计算偏离度
            deviations = data - center
            
            # 非线性增强：使用幂函数放大差异
            enhanced_deviations = np.sign(deviations) * np.power(np.abs(deviations), enhancement_factor)
            
            # 重新组合
            enhanced_data = center + enhanced_deviations
            
            return enhanced_data
        except Exception as e:
            logger = get_logger(__name__)
            logger.warning(f"差异增强异常: {e}")
            return data
    
    @staticmethod
    def sigmoid_stretch(data: pd.Series, steepness: float = 2.0) -> pd.Series:
        """使用sigmoid函数拉伸数据分布"""
        if data.empty or data.isna().all():
            return data
        
        try:
            # 标准化到[-1, 1]
            data_norm = 2 * (data - data.min()) / (data.max() - data.min()) - 1
            
            # 应用sigmoid拉伸
            stretched = np.tanh(steepness * data_norm)
            
            # 重新映射到原始范围
            result = data.min() + (stretched + 1) / 2 * (data.max() - data.min())
            
            return result
        except Exception as e:
            logger = get_logger(__name__)
            logger.warning(f"Sigmoid拉伸异常: {e}")
            return data
    
    @staticmethod
    def adaptive_contrast_enhancement(data: pd.Series, target_std_ratio: float = 0.8) -> pd.Series:
        """自适应对比度增强 - 保持指定的标准差比例"""
        if data.empty or data.isna().all():
            return data
        
        try:
            original_std = data.std()
            if original_std == 0:
                return data
            
            # 计算目标标准差
            target_std = original_std * target_std_ratio
            
            # 中心化
            centered = data - data.mean()
            
            # 计算当前标准差
            current_std = centered.std()
            
            # 调整标准差
            if current_std > 0:
                scaling_factor = target_std / current_std
                enhanced = data.mean() + centered * scaling_factor
            else:
                enhanced = data
            
            return enhanced
        except Exception as e:
            logger = get_logger(__name__)
            logger.warning(f"对比度增强异常: {e}")
            return data
    
    @staticmethod
    def percentile_based_enhancement(data: pd.Series, stretch_factor: float = 1.5) -> pd.Series:
        """基于分位数的增强 - 拉伸分位数间距"""
        if data.empty or data.isna().all():
            return data
        
        try:
            # 计算关键分位数
            p25, p50, p75 = np.percentile(data, [25, 50, 75])
            
            # 计算四分位距
            iqr = p75 - p25
            
            if iqr == 0:
                return data
            
            # 对每个数据点进行分段增强
            enhanced = data.copy()
            
            # 下四分位数以下：向下拉伸
            mask_low = data <= p25
            if mask_low.any():
                enhanced[mask_low] = p50 - (p50 - data[mask_low]) * stretch_factor
            
            # 上四分位数以上：向上拉伸
            mask_high = data >= p75
            if mask_high.any():
                enhanced[mask_high] = p50 + (data[mask_high] - p50) * stretch_factor
            
            # 中间部分：轻微拉伸
            mask_mid = (data > p25) & (data < p75)
            if mask_mid.any():
                enhanced[mask_mid] = p50 + (data[mask_mid] - p50) * (1 + 0.3 * stretch_factor)
            
            return enhanced
        except Exception as e:
            logger = get_logger(__name__)
            logger.warning(f"分位数增强异常: {e}")
            return data


class QualityMonitor:
    """标准化质量监控器"""
    
    @staticmethod
    def assess_normalization_quality(original: pd.Series, normalized: pd.Series) -> Dict[str, float]:
        """评估标准化质量 - 基于scikit-learn最佳实践优化"""
        try:
            if original.empty or normalized.empty:
                return {'overall_score': 0.0}
            
            # 数据预处理
            orig_clean = original.dropna()
            norm_clean = normalized.dropna()
            
            if len(orig_clean) < 2 or len(norm_clean) < 2:
                return {'overall_score': 0.0}
            
            # 1. 改进的区分度保持评分 (0-1) - 使用相对变异系数
            orig_cv = orig_clean.std() / abs(orig_clean.mean()) if orig_clean.mean() != 0 else 0
            norm_cv = norm_clean.std() / abs(norm_clean.mean()) if norm_clean.mean() != 0 else 0
            
            if orig_cv > 0:
                # 使用变异系数比值，更稳定的区分度指标
                cv_ratio = min(norm_cv / orig_cv, 2.0)  # 允许适度增强
                discrimination_score = min(cv_ratio, 1.0) if cv_ratio <= 1.0 else max(0.8, 1.0 - (cv_ratio - 1.0) * 0.5)
            else:
                # 原始数据无变异，评估是否成功创造了合理的差异
                if norm_cv > 0:
                    # 成功创造差异，但不应过度
                    discrimination_score = min(0.8, norm_cv * 20)  # 适度奖励
                else:
                    discrimination_score = 0.0
            
            # 2. 排序保持评分 (0-1) - 增强稳定性
            try:
                from scipy.stats import spearmanr, kendalltau
                spearman_corr, _ = spearmanr(orig_clean, norm_clean)
                kendall_corr, _ = kendalltau(orig_clean, norm_clean)
                
                # 使用两种相关性的平均值，提高稳定性
                spearman_score = max(spearman_corr, 0.0) if not np.isnan(spearman_corr) else 0.0
                kendall_score = max(kendall_corr, 0.0) if not np.isnan(kendall_corr) else 0.0
                ranking_score = (spearman_score + kendall_score) / 2
            except:
                ranking_score = 0.6  # 保守估计
            
            # 3. 分布相似性评分 (0-1) - 使用KS测试替代直方图方差
            try:
                from scipy.stats import ks_2samp
                # 标准化到相同尺度进行比较
                orig_scaled = (orig_clean - orig_clean.min()) / (orig_clean.max() - orig_clean.min()) if orig_clean.max() != orig_clean.min() else orig_clean
                norm_scaled = (norm_clean - norm_clean.min()) / (norm_clean.max() - norm_clean.min()) if norm_clean.max() != norm_clean.min() else norm_clean
                
                ks_stat, _ = ks_2samp(orig_scaled, norm_scaled)
                # KS统计量越小，分布越相似
                distribution_score = max(0.0, 1.0 - ks_stat)
            except:
                # 备用方法：使用分位数比较
                try:
                    orig_q = np.percentile(orig_clean, [25, 50, 75])
                    norm_q = np.percentile(norm_clean, [25, 50, 75])
                    orig_q_norm = (orig_q - orig_q.min()) / (orig_q.max() - orig_q.min()) if orig_q.max() != orig_q.min() else orig_q
                    norm_q_norm = (norm_q - norm_q.min()) / (norm_q.max() - norm_q.min()) if norm_q.max() != norm_q.min() else norm_q
                    distribution_score = max(0.0, 1.0 - np.mean(np.abs(orig_q_norm - norm_q_norm)))
                except:
                    distribution_score = 0.5
            
            # 4. 数据完整性评分 (0-1) - 替代异常值处理评分
            try:
                # 检查数据范围的合理性
                norm_range = norm_clean.max() - norm_clean.min()
                range_score = 1.0 if norm_range > 0 else 0.0
                
                # 检查是否有异常的极值
                q1, q3 = np.percentile(norm_clean, [25, 75])
                iqr = q3 - q1
                if iqr > 0:
                    outlier_bounds = [q1 - 1.5 * iqr, q3 + 1.5 * iqr]
                    outlier_ratio = len(norm_clean[(norm_clean < outlier_bounds[0]) | (norm_clean > outlier_bounds[1])]) / len(norm_clean)
                    outlier_score = max(0.0, 1.0 - outlier_ratio * 2)  # 适度惩罚异常值
                else:
                    outlier_score = 0.8  # 无IQR但有range，给予中等分数
                
                integrity_score = (range_score + outlier_score) / 2
            except:
                integrity_score = 0.5
            
            # 5. 优化的权重分配 - 针对金融数据特点
            weights = {
                'discrimination': 0.5,   # 区分度最重要，提高权重
                'ranking': 0.3,          # 排序保持很重要
                'distribution': 0.15,    # 分布相似性，降低权重
                'integrity': 0.05        # 数据完整性
            }
            
            # 6. 数据量调整因子 - 小样本给予更宽松的评分
            sample_size_factor = min(1.0, len(orig_clean) / 50)  # 50个样本以上给予满分
            adjustment = 0.1 * (1 - sample_size_factor)  # 小样本最多加0.1分
            
            overall_score = (
                weights['discrimination'] * discrimination_score +
                weights['ranking'] * ranking_score +
                weights['distribution'] * distribution_score +
                weights['integrity'] * integrity_score
            ) + adjustment
            
            overall_score = min(1.0, overall_score)  # 确保不超过1.0
            
            return {
                'overall_score': float(overall_score),
                'discrimination_score': float(discrimination_score),
                'ranking_score': float(ranking_score),
                'distribution_score': float(distribution_score),
                'integrity_score': float(integrity_score),
                'std_preservation_ratio': float(norm_clean.std() / orig_clean.std()) if orig_clean.std() > 0 else 0.0,
                'cv_preservation_ratio': float(norm_cv / orig_cv) if orig_cv > 0 else 0.0,
                'sample_size_factor': float(sample_size_factor)
            }
            
        except Exception as e:
            logger = get_logger(__name__)
            logger.warning(f"质量评估异常: {e}")
            return {'overall_score': 0.0}
    
    @staticmethod
    def suggest_improvement(quality_scores: Dict[str, float]) -> str:
        """根据质量评分建议改进方案 - 优先推荐scikit-learn方法"""
        try:
            discrimination_score = quality_scores.get('discrimination_score', 0.0)
            ranking_score = quality_scores.get('ranking_score', 0.0)
            distribution_score = quality_scores.get('distribution_score', 0.0)
            integrity_score = quality_scores.get('integrity_score', 0.0)
            cv_ratio = quality_scores.get('cv_preservation_ratio', 1.0)
            
            # 基于scikit-learn最佳实践的改进建议
            
            # 1. 区分度严重不足
            if discrimination_score < 0.3:
                if cv_ratio < 0.1:  # 变异系数损失严重
                    return 'adaptive_enhanced'  # 使用自适应增强
                else:
                    return 'sklearn_quantile_uniform'  # 使用QuantileTransformer
            
            # 2. 排序保持不佳
            elif ranking_score < 0.5:
                return 'sklearn_robust'  # RobustScaler对排序保持较好
            
            # 3. 分布相似性差
            elif distribution_score < 0.4:
                if discrimination_score > 0.7:  # 区分度好但分布差
                    return 'sklearn_quantile_normal'  # 转换为正态分布
                else:
                    return 'sklearn_power'  # 使用PowerTransformer
            
            # 4. 数据完整性问题
            elif integrity_score < 0.5:
                return 'sklearn_robust'  # 对异常值最稳健
            
            # 5. 综合质量中等，选择最适合的方法
            elif discrimination_score < 0.6:
                return 'difference_enhanced'  # 增强区分度
            elif ranking_score < 0.7:
                return 'sklearn_standard'  # 标准化通常保持排序较好
            elif distribution_score < 0.6:
                return 'sklearn_quantile_uniform'  # 改善分布
            else:
                return 'sklearn_robust'  # 默认最稳健的方法
        except Exception as e:
            logger = get_logger(__name__)
            logger.warning(f"改进建议异常: {e}")
            return 'sklearn_robust'  # 默认最稳健的方法


class AdaptiveNormalizer:
    """自适应标准化器 - 核心引擎"""
    
    def __init__(self, target_range: Tuple[float, float] = (0, 100)):
        self.target_range = target_range
        self.analyzer = DistributionAnalyzer()
        self.enhancer = DifferenceEnhancer()
        self.monitor = QualityMonitor()
        from utils.logging_config import get_logger
        self.logger = get_logger(__name__)
        self.quality_threshold = 0.45  # 降低质量阈值，减少误报
        self.quality_levels = {
            'excellent': 0.8,
            'good': 0.65,
            'acceptable': 0.45,
            'poor': 0.3
        }
    
    def normalize(self, data: pd.Series, method: str = 'auto') -> pd.Series:
        """自适应标准化主函数 - 带质量监控和自动重试"""
        if data.empty or data.isna().all():
            return pd.Series(np.mean(self.target_range), index=data.index)
        
        # 处理缺失值
        data_clean = data.fillna(data.median())
        
        # 自动选择方法
        if method == 'auto':
            method = self._select_best_method(data_clean)
        
        # 执行标准化
        normalized = self._apply_normalization(data_clean, method)
        
        # 映射到目标范围
        result = self._map_to_target_range(normalized)
        
        # 质量监控和自适应重试
        result = self._adaptive_quality_control(result, data_clean, method)
        
        return result
    
    def _adaptive_quality_control(self, result: pd.Series, original: pd.Series, method: str) -> pd.Series:
        """自适应质量控制 - 渐进式改进机制"""
        try:
            # 评估当前标准化质量
            quality_scores = self.monitor.assess_normalization_quality(original, result)
            overall_quality = quality_scores.get('overall_score', 0.0)
            
            # 确定质量等级
            quality_level = self._get_quality_level(overall_quality)
            
            self.logger.info(
                f"标准化质量评估 - 方法: {method}, "
                f"总分: {overall_quality:.3f}({quality_level}), "
                f"区分度: {quality_scores.get('discrimination_score', 0):.3f}, "
                f"排序保持: {quality_scores.get('ranking_score', 0):.3f}"
            )
            
            # 如果质量可接受，直接返回
            if overall_quality >= self.quality_threshold:
                return result
            
            # 质量不达标，实施渐进式改进
            self.logger.warning(f"标准化质量{quality_level} ({overall_quality:.3f} < {self.quality_threshold})，开始渐进式改进")
            
            # 根据质量等级选择改进策略
            improved_result = self._progressive_improvement(original, result, quality_scores, quality_level, method)
            
            # 重新评估改进后的质量
            final_quality = self.monitor.assess_normalization_quality(original, improved_result)
            final_score = final_quality.get('overall_score', 0.0)
            final_level = self._get_quality_level(final_score)
            
            improvement = final_score - overall_quality
            if improvement > 0.05:  # 有明显改进
                self.logger.info(f"质量改进成功: {overall_quality:.3f}({quality_level}) -> {final_score:.3f}({final_level})")
            else:
                self.logger.warning(f"质量改进有限: {overall_quality:.3f} -> {final_score:.3f}")
            
            return improved_result
            
        except Exception as e:
            self.logger.warning(f"质量控制异常: {e}")
            return result
    
    def _get_quality_level(self, score: float) -> str:
        """获取质量等级"""
        if score >= self.quality_levels['excellent']:
            return 'excellent'
        elif score >= self.quality_levels['good']:
            return 'good'
        elif score >= self.quality_levels['acceptable']:
            return 'acceptable'
        else:
            return 'poor'
    
    def _progressive_improvement(self, original: pd.Series, normalized: pd.Series, 
                               quality_metrics: Dict[str, float], quality_level: str, method: str) -> pd.Series:
        """渐进式质量改进"""
        try:
            # 根据质量等级选择改进强度
            if quality_level == 'poor':
                # 质量很差，需要强力改进
                return self._apply_strong_enhancement(original, normalized, quality_metrics)
            else:
                # 质量一般，温和改进
                return self._apply_gentle_enhancement(original, normalized, quality_metrics, method)
            
        except Exception as e:
            self.logger.error(f"渐进式改进异常: {e}")
            return normalized
    
    def _apply_strong_enhancement(self, original: pd.Series, normalized: pd.Series, 
                                quality_metrics: Dict[str, float]) -> pd.Series:
        """强力增强 - 用于质量很差的情况"""
        try:
            # 优先解决最严重的问题
            discrimination_score = quality_metrics.get('discrimination_score', 0.0)
            ranking_score = quality_metrics.get('ranking_score', 0.0)
            
            if discrimination_score < 0.3:
                # 区分度严重不足，使用强化差异增强
                enhanced_data = self.enhancer.percentile_based_enhancement(normalized, stretch_factor=2.5)
                return self._map_to_target_range(
                    (enhanced_data - enhanced_data.min()) / (enhanced_data.max() - enhanced_data.min())
                )
            elif ranking_score < 0.5:
                # 排序保持不佳，尝试保序变换
                return self._preserve_ranking_transform(original, normalized)
            else:
                # 使用自适应增强
                return self._adaptive_enhanced_normalize(original)
            
        except Exception as e:
            self.logger.error(f"强力增强异常: {e}")
            return normalized
    
    def _apply_gentle_enhancement(self, original: pd.Series, normalized: pd.Series, 
                                quality_metrics: Dict[str, float], method: str) -> pd.Series:
        """温和增强 - 用于质量一般的情况"""
        try:
            # 获取改进建议
            suggested_method = self.monitor.suggest_improvement(quality_metrics)
            
            if suggested_method != method and suggested_method != 'auto':
                self.logger.info(f"尝试改进方法: {suggested_method}")
                
                # 重新标准化
                improved_normalized = self._apply_normalization(original, suggested_method)
                improved_result = self._map_to_target_range(improved_normalized)
                
                # 评估改进效果
                improved_quality = self.monitor.assess_normalization_quality(original, improved_result)
                improved_overall = improved_quality.get('overall_score', 0.0)
                original_overall = quality_metrics.get('overall_score', 0.0)
                
                if improved_overall > original_overall:
                    self.logger.info(f"方法改进成功: {improved_overall:.3f} > {original_overall:.3f}")
                    return improved_result
                else:
                    self.logger.info(f"方法改进效果不明显，使用轻微增强")
            
            # 轻微增强区分度
            discrimination_score = quality_metrics.get('discrimination_score', 0.0)
            if discrimination_score < 0.6:
                enhanced_data = self.enhancer.enhance_differences(normalized, enhancement_factor=1.3)
                return self._map_to_target_range(
                    (enhanced_data - enhanced_data.min()) / (enhanced_data.max() - enhanced_data.min())
                )
            
            return normalized
            
        except Exception as e:
            self.logger.error(f"温和增强异常: {e}")
            return normalized
    
    def _preserve_ranking_transform(self, original: pd.Series, normalized: pd.Series) -> pd.Series:
        """保序变换 - 确保排序一致性"""
        try:
            # 获取原始数据的排序
            orig_ranks = original.rank(method='min')
            
            # 将标准化数据按原始排序重新分配
            sorted_norm = np.sort(normalized.values)
            result = pd.Series(index=original.index, dtype=float)
            
            for i, rank in enumerate(orig_ranks):
                rank_idx = int(rank - 1)  # 转换为0基索引
                if rank_idx < len(sorted_norm):
                    result.iloc[i] = sorted_norm[rank_idx]
                else:
                    result.iloc[i] = sorted_norm[-1]
            
            return result
            
        except Exception as e:
            self.logger.error(f"保序变换异常: {e}")
            return normalized
    
    def _apply_emergency_enhancement(self, result: pd.Series, original: pd.Series, 
                                   quality_scores: Dict[str, float]) -> pd.Series:
        """应用紧急增强措施"""
        try:
            self.logger.info("应用紧急增强措施")
            
            # 根据具体问题选择增强策略
            if quality_scores.get('discrimination_score', 0) < 0.3:
                # 区分度严重不足，使用强化差异增强
                enhanced = self.enhancer.percentile_based_enhancement(result, stretch_factor=2.0)
                enhanced = self._map_to_target_range(
                    (enhanced - enhanced.min()) / (enhanced.max() - enhanced.min())
                )
                self.logger.info("应用强化差异增强")
                return enhanced
            
            elif quality_scores.get('std_preservation_ratio', 0) < 0.2:
                # 标准差保持严重不足，使用对比度增强
                enhanced = self.enhancer.adaptive_contrast_enhancement(result, target_std_ratio=0.6)
                enhanced = self._map_to_target_range(
                    (enhanced - enhanced.min()) / (enhanced.max() - enhanced.min())
                )
                self.logger.info("应用对比度增强")
                return enhanced
            
            else:
                # 一般性增强
                enhanced = self.enhancer.enhance_differences(result, enhancement_factor=1.8)
                enhanced = self._map_to_target_range(
                    (enhanced - enhanced.min()) / (enhanced.max() - enhanced.min())
                )
                self.logger.info("应用一般性增强")
                return enhanced
                
        except Exception as e:
            self.logger.warning(f"紧急增强异常: {e}")
            return result
    
    def _select_best_method(self, data: pd.Series) -> str:
        """根据数据分布特征选择最佳标准化方法 - 集成scikit-learn方法"""
        try:
            if len(data) < 2:
                return 'sklearn_minmax'
            
            # 计算数据分布特征
            clean_data = data.dropna()
            if len(clean_data) < 2:
                return 'sklearn_minmax'
            
            dist_info = self.analyzer.analyze_distribution(clean_data)
            concentration = dist_info['concentration']
            cv = dist_info['cv']
            skewness = abs(dist_info['skewness'])
            kurtosis = abs(dist_info['kurtosis'])
            range_ratio = dist_info['range_ratio']
            sample_size = len(clean_data)
            
            self.logger.debug(
                f"数据特征 - 样本量: {sample_size}, 集中度: {concentration:.3f}, "
                f"变异系数: {cv:.3f}, 偏度: {skewness:.3f}, 峰度: {kurtosis:.3f}, 范围比: {range_ratio:.3f}"
            )
            
            # 基于scikit-learn最佳实践的决策逻辑
            
            # 1. 小样本处理
            if sample_size < 30:
                if cv < 0.2:  # 小样本且低变异
                    return 'adaptive_enhanced'
                else:
                    return 'sklearn_robust'  # 小样本用RobustScaler更稳定
            
            # 2. 极度集中的数据
            if concentration > 0.9:
                return 'adaptive_enhanced'
            elif concentration > 0.7:
                return 'percentile_enhanced'
            
            # 3. 高偏度数据 - 优先使用QuantileTransformer
            if skewness > 2.5 or kurtosis > 7:
                return 'sklearn_quantile_uniform'  # 转换为均匀分布
            elif skewness > 1.5:
                return 'sklearn_quantile_normal'   # 转换为正态分布
            
            # 4. 异常值较多的数据
            if range_ratio > 15:  # 范围很大，可能有异常值
                return 'sklearn_robust'
            elif range_ratio > 8:
                return 'quantile_enhanced'
            
            # 5. 变异系数分析
            if cv < 0.05:  # 极低变异
                return 'adaptive_enhanced'
            elif cv < 0.2:  # 低变异
                return 'difference_enhanced'
            elif cv > 2.0:  # 高变异
                return 'sklearn_robust'
            elif cv > 1.0:  # 中高变异
                return 'sklearn_quantile_uniform'
            
            # 6. 正态性检验
            try:
                from scipy.stats import normaltest
                _, p_value = normaltest(clean_data)
                if p_value > 0.05:  # 接近正态分布
                    return 'sklearn_standard'
                else:
                    return 'sklearn_quantile_normal'
            except:
                pass
            
            # 7. 默认策略 - 根据数据量选择
            if sample_size > 1000:
                return 'sklearn_quantile_uniform'  # 大样本用QuantileTransformer
            elif sample_size > 100:
                return 'sklearn_standard'          # 中等样本用StandardScaler
            else:
                return 'sklearn_robust'            # 小样本用RobustScaler
                
        except Exception as e:
            self.logger.warning(f"方法选择异常: {e}")
            return 'sklearn_robust'  # 默认使用最稳定的方法
    
    def _apply_normalization(self, data: pd.Series, method: str) -> pd.Series:
        """应用具体的标准化方法 - 集成scikit-learn方法"""
        try:
            # 数据预处理
            clean_data = data.dropna()
            if len(clean_data) < 2:
                self.logger.warning("数据量不足，使用默认标准化")
                return self._safe_normalize_fallback(data)
            
            # 零方差检测和预处理
            if data.std() == 0 or data.nunique() == 1:
                self.logger.info(f"检测到零方差数据，应用差异创造策略")
                return self._create_variance_for_uniform_data(data)
            
            # scikit-learn标准化方法
            if method == 'sklearn_standard':
                return self._sklearn_standard_normalize(data)
            elif method == 'sklearn_minmax':
                return self._sklearn_minmax_normalize(data)
            elif method == 'sklearn_robust':
                return self._sklearn_robust_normalize(data)
            elif method == 'sklearn_quantile_uniform':
                return self._sklearn_quantile_normalize(data, output_distribution='uniform')
            elif method == 'sklearn_quantile_normal':
                return self._sklearn_quantile_normalize(data, output_distribution='normal')
            elif method == 'sklearn_power':
                return self._sklearn_power_normalize(data)
            # 原有增强方法
            elif method == 'quantile':
                return self._quantile_normalize(data)
            elif method == 'robust':
                return self._robust_normalize(data)
            elif method == 'quantile_enhanced':
                return self._quantile_enhanced_normalize(data)
            elif method == 'difference_enhanced':
                return self._difference_enhanced_normalize(data)
            elif method == 'percentile':
                return self._percentile_normalize(data)
            elif method == 'percentile_enhanced':
                return self._percentile_enhanced_normalize(data)
            elif method == 'adaptive_enhanced':
                return self._adaptive_enhanced_normalize(data)
            else:
                # 默认使用sklearn robust
                return self._sklearn_robust_normalize(data)
        except Exception as e:
            self.logger.warning(f"标准化方法 {method} 执行异常: {e}，使用备用方法")
            return self._safe_normalize(data)
    
    def _sklearn_standard_normalize(self, data: pd.Series) -> pd.Series:
        """使用sklearn StandardScaler标准化"""
        try:
            from sklearn.preprocessing import StandardScaler
            
            # 处理缺失值
            clean_data = data.dropna()
            if len(clean_data) < 2:
                return data
            
            scaler = StandardScaler()
            normalized = scaler.fit_transform(clean_data.values.reshape(-1, 1)).flatten()
            
            # 创建结果Series，保持原索引
            result = pd.Series(index=data.index, dtype=float)
            result.loc[clean_data.index] = normalized
            
            # 填充缺失值
            if data.isna().any():
                result = result.fillna(result.median())
            
            return result
            
        except Exception as e:
            self.logger.error(f"sklearn StandardScaler失败: {e}")
            return self._safe_normalize_fallback(data)
    
    def _sklearn_minmax_normalize(self, data: pd.Series) -> pd.Series:
        """使用sklearn MinMaxScaler标准化"""
        try:
            from sklearn.preprocessing import MinMaxScaler
            
            clean_data = data.dropna()
            if len(clean_data) < 2:
                return data
            
            scaler = MinMaxScaler()
            normalized = scaler.fit_transform(clean_data.values.reshape(-1, 1)).flatten()
            
            result = pd.Series(index=data.index, dtype=float)
            result.loc[clean_data.index] = normalized
            
            if data.isna().any():
                result = result.fillna(result.median())
            
            return result
            
        except Exception as e:
            self.logger.error(f"sklearn MinMaxScaler失败: {e}")
            return self._safe_normalize_fallback(data)
    
    def _sklearn_robust_normalize(self, data: pd.Series) -> pd.Series:
        """使用sklearn RobustScaler标准化"""
        try:
            from sklearn.preprocessing import RobustScaler
            
            clean_data = data.dropna()
            if len(clean_data) < 2:
                return data
            
            scaler = RobustScaler()
            normalized = scaler.fit_transform(clean_data.values.reshape(-1, 1)).flatten()
            
            result = pd.Series(index=data.index, dtype=float)
            result.loc[clean_data.index] = normalized
            
            if data.isna().any():
                result = result.fillna(result.median())
            
            return result
            
        except Exception as e:
            self.logger.error(f"sklearn RobustScaler失败: {e}")
            return self._safe_normalize_fallback(data)
    
    def _sklearn_quantile_normalize(self, data: pd.Series, output_distribution: str = 'uniform') -> pd.Series:
        """使用sklearn QuantileTransformer标准化"""
        try:
            from sklearn.preprocessing import QuantileTransformer
            
            clean_data = data.dropna()
            if len(clean_data) < 2:
                return data
            
            # 根据数据量调整分位数数量
            n_quantiles = min(1000, max(10, len(clean_data) // 2))
            
            scaler = QuantileTransformer(
                output_distribution=output_distribution,
                n_quantiles=n_quantiles,
                random_state=42
            )
            normalized = scaler.fit_transform(clean_data.values.reshape(-1, 1)).flatten()
            
            result = pd.Series(index=data.index, dtype=float)
            result.loc[clean_data.index] = normalized
            
            if data.isna().any():
                result = result.fillna(result.median())
            
            return result
            
        except Exception as e:
            self.logger.error(f"sklearn QuantileTransformer失败: {e}")
            return self._safe_normalize_fallback(data)
    
    def _sklearn_power_normalize(self, data: pd.Series) -> pd.Series:
        """使用sklearn PowerTransformer标准化"""
        try:
            from sklearn.preprocessing import PowerTransformer
            
            clean_data = data.dropna()
            if len(clean_data) < 2:
                return data
            
            # 确保数据为正值（PowerTransformer要求）
            if (clean_data <= 0).any():
                # 平移到正值
                shifted_data = clean_data - clean_data.min() + 1e-6
            else:
                shifted_data = clean_data
            
            scaler = PowerTransformer(method='yeo-johnson', standardize=True)
            normalized = scaler.fit_transform(shifted_data.values.reshape(-1, 1)).flatten()
            
            result = pd.Series(index=data.index, dtype=float)
            result.loc[clean_data.index] = normalized
            
            if data.isna().any():
                result = result.fillna(result.median())
            
            return result
            
        except Exception as e:
            self.logger.error(f"sklearn PowerTransformer失败: {e}")
            return self._safe_normalize_fallback(data)
    
    def _quantile_normalize(self, data: pd.Series) -> pd.Series:
        """分位数标准化（保持分布形状）- 改进零方差处理"""
        try:
            # 零方差检测
            if data.std() == 0 or data.nunique() == 1:
                return self._create_variance_for_uniform_data(data)
            
            transformer = QuantileTransformer(output_distribution='uniform', random_state=42)
            normalized = transformer.fit_transform(data.values.reshape(-1, 1)).flatten()
            return pd.Series(normalized, index=data.index)
        except:
            return self._safe_normalize(data)
    
    def _robust_normalize(self, data: pd.Series) -> pd.Series:
        """鲁棒标准化（抗异常值）- 改进零方差处理"""
        try:
            # 零方差检测
            if data.std() == 0 or data.nunique() == 1:
                return self._create_variance_for_uniform_data(data)
            
            scaler = RobustScaler()
            normalized = scaler.fit_transform(data.values.reshape(-1, 1)).flatten()
            # 映射到[0,1]
            if normalized.std() > 0:
                normalized = (normalized - normalized.min()) / (normalized.max() - normalized.min())
            else:
                # 如果RobustScaler也产生了零方差，创造差异
                return self._create_variance_for_uniform_data(data)
            return pd.Series(normalized, index=data.index)
        except:
            return self._safe_normalize(data)
    
    def _quantile_enhanced_normalize(self, data: pd.Series) -> pd.Series:
        """增强分位数标准化（针对集中数据）"""
        try:
            # 先进行差异增强
            enhanced_data = self.enhancer.enhance_differences(data, enhancement_factor=1.8)
            # 再进行分位数标准化
            return self._quantile_normalize(enhanced_data)
        except:
            return self._safe_normalize(data)
    
    def _difference_enhanced_normalize(self, data: pd.Series) -> pd.Series:
        """差异增强标准化"""
        try:
            # 使用sigmoid拉伸增强差异
            stretched_data = self.enhancer.sigmoid_stretch(data, steepness=2.5)
            # 标准化到[0,1]
            normalized = (stretched_data - stretched_data.min()) / (stretched_data.max() - stretched_data.min())
            return pd.Series(normalized, index=data.index)
        except:
            return self._safe_normalize(data)
    
    def _percentile_normalize(self, data: pd.Series) -> pd.Series:
        """百分位数标准化 - 改进零方差处理"""
        try:
            # 零方差检测
            if data.std() == 0 or data.nunique() == 1:
                return self._create_variance_for_uniform_data(data)
            
            # 使用5%和95%分位数作为边界
            p5, p95 = np.percentile(data, [5, 95])
            if p95 - p5 == 0:  # 分位数范围为0
                return self._create_variance_for_uniform_data(data)
            
            clipped_data = data.clip(p5, p95)
            normalized = (clipped_data - p5) / (p95 - p5)
            return pd.Series(normalized, index=data.index)
        except:
            return self._safe_normalize(data)
    
    def _percentile_enhanced_normalize(self, data: pd.Series) -> pd.Series:
        """增强百分位数标准化 - 针对极度集中的数据"""
        try:
            # 先应用分位数增强
            enhanced_data = self.enhancer.percentile_based_enhancement(data, stretch_factor=2.5)
            
            # 再进行百分位数标准化，使用更宽的范围
            p2, p98 = np.percentile(enhanced_data, [2, 98])
            if p98 - p2 > 0:
                clipped_data = enhanced_data.clip(p2, p98)
                normalized = (clipped_data - p2) / (p98 - p2)
            else:
                # 如果范围为0，使用对比度增强
                enhanced_data = self.enhancer.adaptive_contrast_enhancement(data, target_std_ratio=0.8)
                normalized = (enhanced_data - enhanced_data.min()) / (enhanced_data.max() - enhanced_data.min())
            
            return pd.Series(normalized, index=data.index)
        except:
            return self._safe_normalize(data)
    
    def _adaptive_enhanced_normalize(self, data: pd.Series) -> pd.Series:
        """自适应增强标准化 - 多策略组合"""
        try:
            # 分析数据特征
            dist_info = self.analyzer.analyze_distribution(data)
            
            # 根据特征选择增强策略
            if dist_info['cv'] < 0.05:  # 极低变异
                # 使用强化对比度增强
                enhanced = self.enhancer.adaptive_contrast_enhancement(data, target_std_ratio=1.2)
            elif dist_info['concentration'] > 0.9:  # 极度集中
                # 使用分位数增强
                enhanced = self.enhancer.percentile_based_enhancement(data, stretch_factor=3.0)
            else:
                # 使用sigmoid拉伸
                enhanced = self.enhancer.sigmoid_stretch(data, steepness=3.0)
            
            # 标准化到[0,1]
            if enhanced.std() > 0:
                normalized = (enhanced - enhanced.min()) / (enhanced.max() - enhanced.min())
            else:
                # 如果仍然没有差异，强制创建差异
                normalized = pd.Series(np.linspace(0, 1, len(enhanced)), index=enhanced.index)
                # 添加基于索引的微小扰动
                perturbation = np.random.normal(0, 0.01, len(normalized))
                normalized = normalized + perturbation
                normalized = normalized.clip(0, 1)
            
            return normalized
        except Exception as e:
            self.logger.warning(f"自适应增强标准化异常: {e}")
            return self._safe_normalize(data)
    
    def _create_variance_for_uniform_data(self, data: pd.Series) -> pd.Series:
        """为零方差数据创造差异 - 确定性方法"""
        try:
            n = len(data)
            if n <= 1:
                return pd.Series([0.5], index=data.index)
            
            # 策略1：基于索引位置创建线性差异
            linear_diff = np.linspace(0, 1, n)
            
            # 策略2：添加基于索引的正弦波动，增加自然感
            sine_component = 0.1 * np.sin(np.linspace(0, 2*np.pi, n))
            
            # 组合策略，创造既有趋势又有波动的差异
            created_variance = linear_diff + sine_component
            
            # 标准化到[0,1]范围
            created_variance = (created_variance - created_variance.min()) / (created_variance.max() - created_variance.min())
            
            result = pd.Series(created_variance, index=data.index)
            self.logger.info(f"为{n}个相同值创造差异，新标准差: {result.std():.4f}")
            return result
            
        except Exception as e:
            self.logger.warning(f"差异创造异常: {e}，使用备用方法")
            return self._safe_normalize_fallback(data)
    
    def _safe_normalize(self, data: pd.Series) -> pd.Series:
        """安全的备用标准化方法 - 改进零方差处理"""
        try:
            if data.std() == 0 or data.nunique() == 1:
                # 使用差异创造策略而不是返回固定值
                return self._create_variance_for_uniform_data(data)
            
            # 简单的min-max标准化，但保留更多差异
            normalized = (data - data.min()) / (data.max() - data.min())
            return normalized.fillna(0.5)
        except:
            return self._safe_normalize_fallback(data)
    
    def _safe_normalize_fallback(self, data: pd.Series) -> pd.Series:
        """最终备用方法"""
        try:
            return pd.Series(0.5, index=data.index)
        except:
            return pd.Series([0.5])
    
    def _map_to_target_range(self, normalized: pd.Series) -> pd.Series:
        """映射到目标范围"""
        try:
            min_target, max_target = self.target_range
            mapped = min_target + normalized * (max_target - min_target)
            return mapped
        except:
            return pd.Series(np.mean(self.target_range), index=normalized.index)
    
    def _quality_check_and_enhance(self, result: pd.Series, original: pd.Series) -> pd.Series:
        """质量检查和增强"""
        try:
            # 检查区分度
            std_ratio = result.std() / original.std() if original.std() > 0 else 1
            
            # 如果标准化后区分度严重下降，进行补偿
            if std_ratio < 0.3:  # 区分度下降超过70%
                self.logger.info(f"检测到区分度下降，进行补偿增强")
                # 重新应用差异增强
                enhanced = self.enhancer.enhance_differences(result, enhancement_factor=1.3)
                return self._map_to_target_range(
                    (enhanced - enhanced.min()) / (enhanced.max() - enhanced.min())
                )
            
            return result
        except Exception as e:
            self.logger.warning(f"质量检查异常: {e}")
            return result
    
    def get_normalization_stats(self, original: pd.Series, normalized: pd.Series) -> Dict[str, float]:
        """获取标准化统计信息"""
        try:
            original_stats = self.analyzer.analyze_distribution(original)
            normalized_stats = self.analyzer.analyze_distribution(normalized)
            
            return {
                'original_std': float(original.std()),
                'normalized_std': float(normalized.std()),
                'std_preservation_ratio': float(normalized.std() / original.std()) if original.std() > 0 else 1,
                'original_range': float(original.max() - original.min()),
                'normalized_range': float(normalized.max() - normalized.min()),
                'concentration_change': float(normalized_stats['concentration'] - original_stats['concentration'])
            }
        except Exception as e:
            self.logger.warning(f"统计信息计算异常: {e}")
            return {}


class NormalizationEngine:
    """标准化引擎主类"""
    
    def __init__(self, target_range: Tuple[float, float] = (0, 100)):
        self.normalizer = AdaptiveNormalizer(target_range)
        from utils.logging_config import get_logger
        self.logger = get_logger(__name__)
        self.stats_history = []
    
    def normalize_series(self, data: pd.Series, method: str = 'auto', 
                        track_stats: bool = True) -> pd.Series:
        """标准化单个序列"""
        result = self.normalizer.normalize(data, method)
        
        if track_stats:
            stats = self.normalizer.get_normalization_stats(data, result)
            self.stats_history.append(stats)
            
            # 记录关键统计信息
            if stats:
                self.logger.info(
                    f"标准化完成 - 方法: {method}, "
                    f"区分度保持率: {stats.get('std_preservation_ratio', 0):.2f}, "
                    f"标准化后标准差: {stats.get('normalized_std', 0):.2f}"
                )
        
        return result
    
    def normalize_dataframe(self, df: pd.DataFrame, columns: list = None, 
                          method: str = 'auto') -> pd.DataFrame:
        """标准化DataFrame中的指定列"""
        if columns is None:
            columns = df.select_dtypes(include=[np.number]).columns.tolist()
        
        result_df = df.copy()
        
        for col in columns:
            if col in df.columns:
                try:
                    result_df[col] = self.normalize_series(df[col], method, track_stats=False)
                except Exception as e:
                    self.logger.error(f"列 {col} 标准化失败: {e}")
                    result_df[col] = df[col]  # 保持原值
        
        return result_df
    
    def get_performance_summary(self) -> Dict[str, float]:
        """获取性能摘要"""
        if not self.stats_history:
            return {}
        
        try:
            avg_preservation = np.mean([s.get('std_preservation_ratio', 0) for s in self.stats_history])
            avg_normalized_std = np.mean([s.get('normalized_std', 0) for s in self.stats_history])
            
            return {
                'average_std_preservation_ratio': float(avg_preservation),
                'average_normalized_std': float(avg_normalized_std),
                'total_normalizations': len(self.stats_history)
            }
        except Exception as e:
            self.logger.warning(f"性能摘要计算异常: {e}")
            return {}


# 便捷函数
def adaptive_normalize(data: Union[pd.Series, pd.DataFrame], 
                      target_range: Tuple[float, float] = (0, 100),
                      method: str = 'auto') -> Union[pd.Series, pd.DataFrame]:
    """便捷的自适应标准化函数"""
    engine = NormalizationEngine(target_range)
    
    if isinstance(data, pd.Series):
        return engine.normalize_series(data, method)
    elif isinstance(data, pd.DataFrame):
        return engine.normalize_dataframe(data, method=method)
    else:
        raise ValueError("数据类型必须是 pandas Series 或 DataFrame")


if __name__ == "__main__":
    # 测试代码
    import matplotlib.pyplot as plt
    
    # 创建测试数据
    np.random.seed(42)
    test_data = pd.Series(np.random.normal(50, 5, 100))  # 集中分布的数据
    
    # 测试不同标准化方法
    engine = NormalizationEngine()
    
    methods = ['auto', 'quantile', 'robust', 'quantile_enhanced', 'difference_enhanced']
    
    print("原始数据统计:")
    print(f"均值: {test_data.mean():.2f}, 标准差: {test_data.std():.2f}")
    print(f"最小值: {test_data.min():.2f}, 最大值: {test_data.max():.2f}")
    print()
    
    for method in methods:
        normalized = engine.normalize_series(test_data, method=method)
        print(f"方法 {method}:")
        print(f"均值: {normalized.mean():.2f}, 标准差: {normalized.std():.2f}")
        print(f"最小值: {normalized.min():.2f}, 最大值: {normalized.max():.2f}")
        print()
    
    print("性能摘要:")
    print(engine.get_performance_summary())