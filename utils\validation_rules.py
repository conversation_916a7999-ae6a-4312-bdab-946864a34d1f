#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证规则定义模块
定义股票分析系统中各种数据的验证规则

作者: LilyBullRider Team
创建时间: 2024
"""

import re
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Union
from .data_validator import (
    BaseValidator, DataTypeValidator, RangeValidator, 
    RegexValidator, DateValidator, ValidationLevel, 
    ValidationResult, DataValidator
)

class StockCodeValidator(BaseValidator):
    """股票代码验证器"""
    
    def __init__(self, level: ValidationLevel = ValidationLevel.WARNING):
        super().__init__("StockCode", level)
        # 支持多种股票代码格式
        self.patterns = {
            'A股': re.compile(r'^[0-9]{6}$'),  # 6位数字
            'A股带前缀': re.compile(r'^(SH|SZ)[0-9]{6}$'),  # SH/SZ + 6位数字
            'A股带点': re.compile(r'^[0-9]{6}\.(SH|SZ)$'),  # 6位数字 + .SH/.SZ
            '港股': re.compile(r'^[0-9]{5}$'),  # 5位数字
            '美股': re.compile(r'^[A-Z]{1,5}$'),  # 1-5位大写字母
        }
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> tuple:
        if value is None:
            return ValidationResult.FAIL, value, "股票代码不能为空"
        
        code_str = str(value).strip().upper()
        
        # 检查是否匹配任何已知格式
        for format_name, pattern in self.patterns.items():
            if pattern.match(code_str):
                # 标准化格式
                if format_name == 'A股' and len(code_str) == 6:
                    # 根据代码范围添加交易所前缀
                    if code_str.startswith(('000', '001', '002', '003', '300')):
                        standardized = f"{code_str}.SZ"
                    elif code_str.startswith(('600', '601', '603', '605', '688')):
                        standardized = f"{code_str}.SH"
                    else:
                        standardized = code_str  # 保持原格式
                    return ValidationResult.FIXED, standardized, f"已标准化股票代码格式"
                
                return ValidationResult.PASS, code_str, None
        
        return ValidationResult.FAIL, value, f"无效的股票代码格式: {code_str}"

class PriceValidator(BaseValidator):
    """股票价格验证器"""
    
    def __init__(self, min_price: float = 0.01, max_price: float = 10000.0,
                 level: ValidationLevel = ValidationLevel.WARNING):
        super().__init__("Price", level)
        self.min_price = min_price
        self.max_price = max_price
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> tuple:
        if value is None:
            return ValidationResult.FAIL, value, "价格不能为空"
        
        try:
            price = float(value)
        except (ValueError, TypeError):
            return ValidationResult.FAIL, value, f"无法转换为有效价格: {value}"
        
        if price < 0:
            return ValidationResult.FAIL, value, f"价格不能为负数: {price}"
        
        if price < self.min_price:
            if self.level == ValidationLevel.LENIENT:
                return ValidationResult.FIXED, self.min_price, f"价格过低，已调整为最小值: {self.min_price}"
            else:
                return ValidationResult.WARNING, price, f"价格异常偏低: {price}"
        
        if price > self.max_price:
            if self.level == ValidationLevel.LENIENT:
                return ValidationResult.FIXED, self.max_price, f"价格过高，已调整为最大值: {self.max_price}"
            else:
                return ValidationResult.WARNING, price, f"价格异常偏高: {price}"
        
        # 检查价格精度（通常股票价格保留2位小数）
        rounded_price = round(price, 2)
        if abs(price - rounded_price) > 1e-6:
            return ValidationResult.FIXED, rounded_price, f"价格精度已调整为2位小数"
        
        return ValidationResult.PASS, rounded_price, None

class VolumeValidator(BaseValidator):
    """成交量验证器"""
    
    def __init__(self, min_volume: int = 0, max_volume: int = 1000000000,
                 level: ValidationLevel = ValidationLevel.WARNING):
        super().__init__("Volume", level)
        self.min_volume = min_volume
        self.max_volume = max_volume
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> tuple:
        if value is None:
            return ValidationResult.FAIL, value, "成交量不能为空"
        
        try:
            volume = int(float(value))  # 支持从浮点数转换
        except (ValueError, TypeError):
            return ValidationResult.FAIL, value, f"无法转换为有效成交量: {value}"
        
        if volume < self.min_volume:
            return ValidationResult.WARNING, volume, f"成交量异常偏低: {volume}"
        
        if volume > self.max_volume:
            return ValidationResult.WARNING, volume, f"成交量异常偏高: {volume}"
        
        return ValidationResult.PASS, volume, None

class MarketCapValidator(BaseValidator):
    """市值验证器"""
    
    def __init__(self, min_cap: float = 1e6, max_cap: float = 1e13,
                 level: ValidationLevel = ValidationLevel.WARNING):
        super().__init__("MarketCap", level)
        self.min_cap = min_cap  # 最小市值100万
        self.max_cap = max_cap  # 最大市值10万亿
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> tuple:
        if value is None:
            return ValidationResult.WARNING, value, "市值为空"
        
        try:
            market_cap = float(value)
        except (ValueError, TypeError):
            return ValidationResult.FAIL, value, f"无法转换为有效市值: {value}"
        
        if market_cap <= 0:
            return ValidationResult.FAIL, value, f"市值必须为正数: {market_cap}"
        
        if market_cap < self.min_cap:
            return ValidationResult.WARNING, market_cap, f"市值异常偏低: {market_cap:,.0f}"
        
        if market_cap > self.max_cap:
            return ValidationResult.WARNING, market_cap, f"市值异常偏高: {market_cap:,.0f}"
        
        return ValidationResult.PASS, market_cap, None

class FinancialRatioValidator(BaseValidator):
    """财务比率验证器"""
    
    def __init__(self, ratio_name: str, min_value: float = None, max_value: float = None,
                 allow_negative: bool = True, level: ValidationLevel = ValidationLevel.WARNING):
        super().__init__(f"FinancialRatio_{ratio_name}", level)
        self.ratio_name = ratio_name
        self.min_value = min_value
        self.max_value = max_value
        self.allow_negative = allow_negative
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> tuple:
        if value is None:
            return ValidationResult.WARNING, value, f"{self.ratio_name}为空"
        
        try:
            ratio = float(value)
        except (ValueError, TypeError):
            return ValidationResult.FAIL, value, f"无法转换为有效的{self.ratio_name}: {value}"
        
        # 检查无穷大和NaN
        if not (-float('inf') < ratio < float('inf')):
            return ValidationResult.FAIL, value, f"{self.ratio_name}值无效: {ratio}"
        
        if not self.allow_negative and ratio < 0:
            return ValidationResult.WARNING, ratio, f"{self.ratio_name}为负数: {ratio}"
        
        if self.min_value is not None and ratio < self.min_value:
            return ValidationResult.WARNING, ratio, f"{self.ratio_name}低于预期范围: {ratio}"
        
        if self.max_value is not None and ratio > self.max_value:
            return ValidationResult.WARNING, ratio, f"{self.ratio_name}高于预期范围: {ratio}"
        
        return ValidationResult.PASS, ratio, None

class TradingDateValidator(BaseValidator):
    """交易日期验证器"""
    
    def __init__(self, level: ValidationLevel = ValidationLevel.WARNING):
        super().__init__("TradingDate", level)
        # 定义非交易日（简化版，实际应该从交易日历获取）
        self.non_trading_weekdays = [5, 6]  # 周六、周日
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> tuple:
        if value is None:
            return ValidationResult.FAIL, value, "交易日期不能为空"
        
        try:
            if isinstance(value, str):
                # 尝试多种日期格式
                for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y%m%d']:
                    try:
                        trade_date = datetime.strptime(value, fmt).date()
                        break
                    except ValueError:
                        continue
                else:
                    return ValidationResult.FAIL, value, f"无法解析日期格式: {value}"
            elif isinstance(value, datetime):
                trade_date = value.date()
            elif isinstance(value, date):
                trade_date = value
            else:
                return ValidationResult.FAIL, value, f"不支持的日期类型: {type(value)}"
        except Exception as e:
            return ValidationResult.FAIL, value, f"日期解析错误: {str(e)}"
        
        # 检查日期范围（不能是未来日期）
        today = date.today()
        if trade_date > today:
            return ValidationResult.FAIL, value, f"交易日期不能是未来日期: {trade_date}"
        
        # 检查是否过于久远（超过30年）
        thirty_years_ago = today - timedelta(days=30*365)
        if trade_date < thirty_years_ago:
            return ValidationResult.WARNING, trade_date, f"交易日期过于久远: {trade_date}"
        
        # 检查是否为周末（简单检查）
        if trade_date.weekday() in self.non_trading_weekdays:
            return ValidationResult.WARNING, trade_date, f"日期为周末: {trade_date}"
        
        return ValidationResult.PASS, trade_date, None

class DataConsistencyValidator(BaseValidator):
    """数据一致性验证器"""
    
    def __init__(self, level: ValidationLevel = ValidationLevel.WARNING):
        super().__init__("DataConsistency", level)
    
    def validate(self, value: Any, context: Optional[Dict] = None) -> tuple:
        """验证记录内部数据的一致性"""
        if not isinstance(value, dict):
            return ValidationResult.PASS, value, None
        
        errors = []
        
        # 检查价格一致性
        if all(k in value for k in ['open', 'high', 'low', 'close']):
            open_price = float(value['open'])
            high_price = float(value['high'])
            low_price = float(value['low'])
            close_price = float(value['close'])
            
            if not (low_price <= open_price <= high_price):
                errors.append(f"开盘价 {open_price} 不在最高价 {high_price} 和最低价 {low_price} 之间")
            
            if not (low_price <= close_price <= high_price):
                errors.append(f"收盘价 {close_price} 不在最高价 {high_price} 和最低价 {low_price} 之间")
        
        # 检查成交金额和成交量的一致性
        if all(k in value for k in ['volume', 'amount', 'close']):
            volume = float(value['volume'])
            amount = float(value['amount'])
            close_price = float(value['close'])
            
            if volume > 0 and amount > 0:
                avg_price = amount / volume
                # 平均价格应该在合理范围内
                if not (low_price * 0.8 <= avg_price <= high_price * 1.2):
                    errors.append(f"平均价格 {avg_price:.2f} 与价格范围不一致")
        
        # 检查财务指标的逻辑性
        if all(k in value for k in ['pe_ratio', 'eps']):
            pe_ratio = value['pe_ratio']
            eps = value['eps']
            
            if pe_ratio is not None and eps is not None:
                pe_ratio = float(pe_ratio)
                eps = float(eps)
                
                if eps > 0 and pe_ratio < 0:
                    errors.append(f"EPS为正 {eps} 但PE为负 {pe_ratio}")
                elif eps < 0 and pe_ratio > 0:
                    errors.append(f"EPS为负 {eps} 但PE为正 {pe_ratio}")
        
        if errors:
            return ValidationResult.WARNING, value, "; ".join(errors)
        
        return ValidationResult.PASS, value, None

class StockDataValidatorFactory:
    """股票数据验证器工厂类"""
    
    @staticmethod
    def create_basic_stock_validator(level: ValidationLevel = ValidationLevel.WARNING) -> DataValidator:
        """创建基础股票数据验证器"""
        validator = DataValidator(level)
        
        # 股票代码验证
        validator.add_field_validator('code', [
            StockCodeValidator(level)
        ])
        
        # 股票名称验证
        validator.add_field_validator('name', [
            DataTypeValidator(str, allow_none=False, level=level),
            RegexValidator(r'^.{1,20}$', level=level)  # 1-20个字符
        ])
        
        # 价格相关验证
        for price_field in ['open', 'high', 'low', 'close', 'pre_close']:
            validator.add_field_validator(price_field, [
                DataTypeValidator(float, allow_none=False, level=level),
                PriceValidator(level=level)
            ])
        
        # 成交量验证
        validator.add_field_validator('volume', [
            DataTypeValidator(int, allow_none=False, level=level),
            VolumeValidator(level=level)
        ])
        
        # 成交金额验证
        validator.add_field_validator('amount', [
            DataTypeValidator(float, allow_none=False, level=level),
            RangeValidator(min_value=0, level=level)
        ])
        
        # 日期验证
        validator.add_field_validator('date', [
            TradingDateValidator(level)
        ])
        
        # 添加数据一致性验证
        validator.add_global_validator(DataConsistencyValidator(level))
        
        return validator
    
    @staticmethod
    def create_financial_data_validator(level: ValidationLevel = ValidationLevel.WARNING) -> DataValidator:
        """创建财务数据验证器"""
        validator = DataValidator(level)
        
        # 股票代码验证
        validator.add_field_validator('code', [
            StockCodeValidator(level)
        ])
        
        # 财务指标验证
        financial_ratios = {
            'pe_ratio': (0, 1000),      # 市盈率
            'pb_ratio': (0, 50),        # 市净率
            'ps_ratio': (0, 100),       # 市销率
            'roe': (-100, 100),         # 净资产收益率
            'roa': (-100, 100),         # 总资产收益率
            'gross_margin': (0, 100),   # 毛利率
            'net_margin': (-100, 100),  # 净利率
            'debt_ratio': (0, 100),     # 负债率
            'current_ratio': (0, 10),   # 流动比率
            'quick_ratio': (0, 10),     # 速动比率
        }
        
        for ratio_name, (min_val, max_val) in financial_ratios.items():
            validator.add_field_validator(ratio_name, [
                DataTypeValidator(float, allow_none=True, level=level),
                FinancialRatioValidator(ratio_name, min_val, max_val, level=level)
            ])
        
        # 市值验证
        validator.add_field_validator('market_cap', [
            DataTypeValidator(float, allow_none=True, level=level),
            MarketCapValidator(level=level)
        ])
        
        # 报告期验证
        validator.add_field_validator('report_date', [
            DateValidator(level=level)
        ])
        
        return validator
    
    @staticmethod
    def create_realtime_data_validator(level: ValidationLevel = ValidationLevel.WARNING) -> DataValidator:
        """创建实时数据验证器"""
        validator = StockDataValidatorFactory.create_basic_stock_validator(level)
        
        # 实时数据特有字段
        validator.add_field_validator('timestamp', [
            DataTypeValidator(datetime, allow_none=False, level=level)
        ])
        
        # 涨跌幅验证
        validator.add_field_validator('change_pct', [
            DataTypeValidator(float, allow_none=True, level=level),
            RangeValidator(min_value=-20, max_value=20, level=level)  # 涨跌幅限制±20%
        ])
        
        # 换手率验证
        validator.add_field_validator('turnover_rate', [
            DataTypeValidator(float, allow_none=True, level=level),
            RangeValidator(min_value=0, max_value=100, level=level)
        ])
        
        return validator

# 预定义的验证器实例
BASIC_STOCK_VALIDATOR = StockDataValidatorFactory.create_basic_stock_validator()
FINANCIAL_DATA_VALIDATOR = StockDataValidatorFactory.create_financial_data_validator()
REALTIME_DATA_VALIDATOR = StockDataValidatorFactory.create_realtime_data_validator()

# 严格模式验证器
STRICT_STOCK_VALIDATOR = StockDataValidatorFactory.create_basic_stock_validator(ValidationLevel.STRICT)
STRICT_FINANCIAL_VALIDATOR = StockDataValidatorFactory.create_financial_data_validator(ValidationLevel.STRICT)

# 宽松模式验证器
LENIENT_STOCK_VALIDATOR = StockDataValidatorFactory.create_basic_stock_validator(ValidationLevel.LENIENT)
LENIENT_FINANCIAL_VALIDATOR = StockDataValidatorFactory.create_financial_data_validator(ValidationLevel.LENIENT)