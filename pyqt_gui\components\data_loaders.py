from PyQt6.QtCore import QTimer
from PyQt6.QtWidgets import QDialog, QLabel, QVBoxLayout
from PyQt6.QtCore import Qt
from utils.logging_config import get_logger

from pyqt_gui.progress_widgets import LoadingProgressDialog
from pyqt_gui.threads.data_threads import (
    InitThread, LoadDataThread, MoneyEffectThread, ConstituentThread,
    HistoryDataThread, StockDataThread, AnalyzeStockThread,
    ModelLoadThread
)
from pyqt_gui.components.data_handlers import DataHandlers
from pyqt_gui.components.status_manager import StatusManager
from pyqt_gui.components.progress_adapters import ProgressAdapters

class DataLoaders:
    """封装数据初始化和加载功能"""

    @staticmethod
    def init_data_modules(self):
        """延迟初始化数据模块"""
        if self.data_loading or self.data_loaded:
            return

        self.data_loading = True
        self.loading_progress = 0
        StatusManager.update_status(self, "正在初始化数据模块...", "loading", 0)

        # 创建加载进度对话框
        self.progress_dialog = LoadingProgressDialog(
            self, "初始化数据", "正在初始化数据模块，请稍候...", True
        )

        # 创建并启动线程
        self.init_thread = InitThread(self)
        self.init_thread.finished_signal.connect(lambda: DataLoaders.on_init_finished(self))
        self.init_thread.progress_signal.connect(lambda progress, message: ProgressAdapters.on_init_progress(self, progress, message))
        self.init_thread.start()

        # 显示进度对话框
        self.progress_dialog.exec()

    @staticmethod
    def on_init_finished(self):
        """数据模块初始化完成回调"""
        self.data_loading = False
        self.data_loaded = True
        StatusManager.update_status(self, "数据模块初始化完成，准备加载数据...", "success")

        # 关闭进度对话框
        if hasattr(self, 'progress_dialog') and self.progress_dialog.isVisible():
            self.progress_dialog.accept()

        # 加载初始数据
        QTimer.singleShot(100, self.load_initial_data)

        # 数据加载完成

    @staticmethod
    def load_initial_data(self):
        """加载初始数据"""
        # 检查数据模块是否已初始化
        if not self.data_loaded:
            StatusManager.update_status(self, "数据模块尚未初始化，请稍候...", "error")
            QTimer.singleShot(1000, lambda: DataLoaders.init_data_modules(self))
            return

        # 显示加载状态
        StatusManager.update_status(self, "正在加载数据，请稍候...", "loading", 0)

        # 创建加载进度对话框
        self.data_progress_dialog = LoadingProgressDialog(
            self, "加载数据", "正在获取板块排名数据，请稍候...", True
        )

        # 创建并启动线程
        self.load_thread = LoadDataThread(self)
        self.load_thread.finished_signal.connect(lambda data: DataLoaders.on_data_loaded(self, data))
        self.load_thread.error_signal.connect(lambda error: DataLoaders.on_data_load_error(self, error))
        self.load_thread.progress_signal.connect(lambda progress, message: ProgressAdapters.on_data_load_progress(self, progress, message))
        self.load_thread.start()

        # 显示进度对话框
        self.data_progress_dialog.exec()

    @staticmethod
    def on_data_loaded(self, data):
        """数据加载完成回调"""
        # 更新UI
        DataHandlers.update_tree_view(self.industry_tree, data['industry_df'])
        DataHandlers.update_tree_view(self.concept_tree, data['concept_df'])

        # 关闭进度对话框
        if hasattr(self, 'data_progress_dialog') and self.data_progress_dialog.isVisible():
            self.data_progress_dialog.accept()

        # 更新状态栏
        from datetime import datetime
        StatusManager.update_status(
            self,
            f"数据更新成功 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "success",
            100
        )

        # 异步获取赚钱效应评分
        QTimer.singleShot(100, self.update_money_effect_score)

        # 3秒后隐藏进度条
        QTimer.singleShot(3000, lambda: StatusManager.update_status(
            self,
            f"数据更新成功 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "success"
        ))

    @staticmethod
    def on_data_load_error(self, error_msg):
        """数据加载错误回调"""
        # 关闭进度对话框
        if hasattr(self, 'data_progress_dialog') and self.data_progress_dialog.isVisible():
            self.data_progress_dialog.accept()

        # 更新状态栏显示错误
        StatusManager.update_status(self, f"数据加载失败: {error_msg}", "error")

    @staticmethod
    def update_money_effect_score(self):
        """异步更新赚钱效应评分"""
        # 显示加载状态
        self.money_effect_text.setPlainText("正在获取赚钱效应评分数据...")
        StatusManager.update_status(self, "正在获取赚钱效应评分...", "loading", 0)

        # 创建加载线程
        self.money_effect_thread = MoneyEffectThread(self)
        self.money_effect_thread.finished_signal.connect(lambda report: DataLoaders.on_money_effect_loaded(self, report))
        self.money_effect_thread.error_signal.connect(lambda error: DataLoaders.on_money_effect_error(self, error))
        self.money_effect_thread.progress_signal.connect(lambda progress, message: ProgressAdapters.on_money_effect_progress(self, progress, message))
        self.money_effect_thread.start()

    @staticmethod
    def on_money_effect_loaded(self, report):
        """赚钱效应评分加载完成回调"""
        # 更新状态栏
        StatusManager.update_status(self, "赚钱效应评分获取完成", "success", 100)

        # 3秒后隐藏进度条
        QTimer.singleShot(3000, lambda: StatusManager.update_status(self, "赚钱效应评分获取完成", "success"))

        # 更新赚钱效应评分文本
        DataHandlers.update_money_effect_text(self, report)

    @staticmethod
    def on_money_effect_error(self, error_msg):
        """赚钱效应评分加载错误回调"""
        self.money_effect_text.setPlainText(f"获取赚钱效应评分失败: {error_msg}")
        StatusManager.update_status(self, f"获取赚钱效应评分失败: {error_msg}", "error")

    @staticmethod
    def load_constituents(self, sector_name, sector_type):
        """加载板块成分股数据"""
        # 显示加载状态
        StatusManager.update_status(self, f"正在加载 {sector_name} 成分股数据...", "loading", 0)
        self.constituent_table.setRowCount(0)

        # 创建并启动线程
        self.constituent_thread = ConstituentThread(self, sector_name, sector_type)
        self.constituent_thread.finished_signal.connect(lambda df: DataLoaders.on_constituents_loaded(self, df, sector_name))
        self.constituent_thread.error_signal.connect(lambda error: DataLoaders.on_constituents_error(self, error))
        self.constituent_thread.progress_signal.connect(lambda progress, message: ProgressAdapters.on_constituent_progress(self, progress, message))
        self.constituent_thread.start()

    @staticmethod
    def on_constituents_loaded(self, constituents, sector_name):
        """成分股数据加载完成回调"""
        # 更新成分股表格
        DataHandlers.update_constituent_table(self, constituents)

        # 更新状态栏
        StatusManager.update_status(self, f"已加载 {sector_name} 成分股数据，正在获取历史行情...", "success", 100)

        # 自动获取成分股历史数据
        stock_codes = [str(row['代码']).split('.')[0] for _, row in constituents.iterrows()]

        # 创建历史数据加载线程
        self.history_thread = HistoryDataThread(self, stock_codes)
        self.history_thread.progress_signal.connect(lambda progress, message: ProgressAdapters.on_history_data_progress(self, progress, message))
        self.history_thread.finished_signal.connect(lambda: DataLoaders.on_history_data_finished(self))
        self.history_thread.start()

    @staticmethod
    def on_history_data_finished(self):
        """历史数据加载完成回调"""
        StatusManager.update_status(self, "历史数据获取完成", "success", 100)
        # 3秒后隐藏进度条
        QTimer.singleShot(3000, lambda: StatusManager.update_status(self, "历史数据获取完成", "success"))

    @staticmethod
    def on_constituents_error(self, error_msg):
        """成分股数据加载错误回调"""
        StatusManager.update_status(self, f"获取成分股失败: {error_msg}", "error")
        self.constituent_table.setRowCount(0)

    @staticmethod
    def load_stock_data(self, stock_code, stock_name):
        """加载股票数据"""
        # 更新状态栏，提示正在加载数据
        StatusManager.update_status(self, f"正在加载 {stock_name}({stock_code}) 的数据...", "loading")

        # 创建并启动线程
        self.stock_data_thread = StockDataThread(self, stock_code, stock_name)
        self.stock_data_thread.finished_signal.connect(lambda code, name, df: DataLoaders.on_stock_data_loaded(self, code, name, df))
        self.stock_data_thread.error_signal.connect(lambda error: DataLoaders.on_stock_data_error(self, error))
        self.stock_data_thread.progress_signal.connect(lambda progress, message: ProgressAdapters.on_stock_data_progress(self, progress, message))
        self.stock_data_thread.start()

    @staticmethod
    def on_stock_data_loaded(self, code, name, df):
        """股票数据加载完成回调"""
        # 更新状态栏
        StatusManager.update_status(self, f"已加载 {name}({code}) 的历史数据", "success", 100)

        # 填充股票代码输入框
        self.stock_code_input.setText(code)

        try:
            # 更新股票详情
            self.stock_code_label.setText(f"股票代码: {code}")
            self.stock_name_label.setText(f"股票名称: {name}")

            if not df.empty:
                # 计算关键指标
                price_column = '收盘' if '收盘' in df.columns else 'close'
                latest_price = df[price_column].iloc[-1]
                prev_price = df[price_column].iloc[-2]
                change_pct = (latest_price - prev_price) / prev_price * 100

                # 获取最近交易日
                date_column = '日期' if '日期' in df.columns else 'date'
                trade_date = df[date_column].iloc[-1]
                if isinstance(trade_date, str):
                    trade_date_str = trade_date
                else:
                    # 如果是datetime，格式化为字符串
                    try:
                        trade_date_str = trade_date.strftime('%Y-%m-%d')
                    except:
                        trade_date_str = str(trade_date)

                # 更新UI
                self.latest_price_label.setText(f"最新价: {latest_price:.2f}")
                self.change_label.setText(f"涨跌幅: {change_pct:+.2f}%")
                self.trade_date_label.setText(f"日期: {trade_date_str}")

                # 设置颜色
                if change_pct > 0:
                    self.change_label.setStyleSheet("color: #e63946;")  # 红色
                elif change_pct < 0:
                    self.change_label.setStyleSheet("color: #2a9d8f;")  # 绿色
                else:
                    self.change_label.setStyleSheet("")

                # 执行技术分析，显示在文本框中
                self.analysis_text.clear()
                self.analysis_text.append(f"【{name}({code})技术分析】\n")
                DataHandlers.display_technical_analysis(self, df, code)

        except Exception as e:
            import traceback
            from utils.logging_config import get_logger
            logger = get_logger(__name__)
            logger.error(f"处理股票数据异常: {str(e)}\n{traceback.format_exc()}")
            StatusManager.update_status(self, f"处理股票数据失败: {str(e)}", "error")

        # 3秒后隐藏进度条
        QTimer.singleShot(3000, lambda: StatusManager.update_status(self, f"已加载 {name}({code}) 的历史数据", "success"))

    @staticmethod
    def on_stock_data_error(self, error_msg):
        """股票数据加载错误回调"""
        StatusManager.update_status(self, f"加载股票数据失败: {error_msg}", "error")

    @staticmethod
    def analyze_stock(self):
        """异步分析股票"""
        code = self.stock_code_input.text().strip()

        # 验证股票代码格式
        if not code:
            self.analysis_text.setPlainText("请输入股票代码")
            return

        # 显示加载状态
        self.analysis_text.setPlainText(f"正在分析股票 {code}，请稍候...")

        # 创建分析线程
        self.analyze_thread = AnalyzeStockThread(self, code)
        self.analyze_thread.finished_signal.connect(lambda data: DataLoaders.on_analysis_completed(self, code, data))
        self.analyze_thread.error_signal.connect(lambda error: DataLoaders.on_analysis_error(self, error))
        self.analyze_thread.start()

    @staticmethod
    def on_analysis_completed(self, code, data):
        """股票分析完成回调"""
        realtime_data = data['realtime_data']
        df = data['history_data']

        # 清空文本框
        self.analysis_text.clear()

        # 显示基本信息
        self.analysis_text.setHtml(f"<h3>股票分析: {code} {realtime_data.get('name', '')}</h3>")
        self.analysis_text.append(f"最新价: {realtime_data.get('price', 'N/A')}")
        self.analysis_text.append(f"涨跌幅: {realtime_data.get('change_percent', 'N/A')}%")
        self.analysis_text.append(f"成交量: {DataHandlers.format_volume(realtime_data.get('volume', 0))}")
        self.analysis_text.append(f"换手率: {realtime_data.get('turnover_rate', 'N/A')}%")
        self.analysis_text.append(f"量比: {realtime_data.get('volume_ratio', 'N/A')}")

        # 显示技术分析
        if not df.empty:
            DataHandlers.display_technical_analysis(self, df, code)
        else:
            self.analysis_text.append("\n无法获取历史数据，请稍后再试。")

    @staticmethod
    def on_analysis_error(self, error_msg):
        """股票分析错误回调"""
        self.analysis_text.setPlainText(f"分析失败: {error_msg}")

    
    # - 初始化文本处理组件
    # - 设置模型加载进度回调

    @staticmethod
    def on_models_loaded(self):
        """基础分析模块加载完成回调"""
        try:
            self.models_loading = False
            self.models_loaded = True

            # 关闭模型加载对话框
            if hasattr(self, 'model_loading_dialog') and self.model_loading_dialog.isVisible():
                try:
                    self.model_loading_dialog.accept()
                except Exception as e:
                    from utils.logging_config import get_logger
                    logger = get_logger(__name__)
                    logger.error(f"关闭模型加载对话框失败: {str(e)}")

            # 更新状态栏
            StatusManager.update_status(self, "基础分析模块加载完成", "success")

            # 数据加载完成
        except Exception as e:
            from utils.logging_config import get_logger
            logger = get_logger(__name__)
            logger.error(f"处理模型加载完成回调时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            StatusManager.update_status(self, f"处理模型加载完成回调时出错: {str(e)}", "error")