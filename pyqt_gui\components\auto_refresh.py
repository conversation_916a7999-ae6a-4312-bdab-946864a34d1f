from PyQt6.QtCore import QTimer
from pyqt_gui.threads.countdown_thread import CountdownThread

class AutoRefresh:
    """处理自动刷新相关功能"""
    
    @staticmethod
    def toggle_auto_refresh(self, checked):
        """切换自动刷新状态"""
        self.auto_refresh_enabled = checked
        
        if checked:
            # 确保数据模块已初始化
            if not self.data_loaded:
                self.update_status("正在初始化数据模块，请稍候...", "loading")
                self.init_data_modules()
                # 延迟启动自动刷新
                QTimer.singleShot(2000, lambda: self.toggle_auto_refresh(True))
                return
                
            AutoRefresh.start_auto_refresh(self)
        else:
            AutoRefresh.stop_auto_refresh(self)
    
    @staticmethod
    def update_refresh_interval(self, text):
        """更新刷新间隔"""
        intervals = {
            "5秒": 5,
            "10秒": 10,
            "30秒": 30,
            "1分钟": 60,
            "3分钟": 180,
            "5分钟": 300,
            "10分钟": 600,
            "30分钟": 1800,
            "1小时": 3600
        }
        
        self.refresh_interval = intervals.get(text, 60)
        
        # 如果自动刷新已启用，重新启动倒计时
        if self.auto_refresh_enabled:
            AutoRefresh.stop_auto_refresh(self)
            AutoRefresh.start_auto_refresh(self)
    
    @staticmethod
    def start_auto_refresh(self):
        """启动自动刷新"""
        if hasattr(self, 'countdown_thread') and self.countdown_thread and self.countdown_thread.isRunning():
            self.countdown_thread.stop()
        
        self.countdown_thread = CountdownThread(self.refresh_interval)
        self.countdown_thread.update_signal.connect(self.update_countdown)
        self.countdown_thread.finished_signal.connect(self.on_countdown_finished)
        self.countdown_thread.start()
    
    @staticmethod
    def stop_auto_refresh(self):
        """停止自动刷新"""
        if hasattr(self, 'countdown_thread') and self.countdown_thread and self.countdown_thread.isRunning():
            self.countdown_thread.stop()
            self.countdown_thread.wait()
        
        self.countdown_label.setText("")
    
    @staticmethod
    def update_countdown(self, seconds):
        """更新倒计时显示"""
        self.countdown_label.setText(f"刷新倒计时: {seconds}秒")
    
    @staticmethod
    def on_countdown_finished(self):
        """倒计时结束处理"""
        if self.auto_refresh_enabled:
            # 刷新数据
            self.load_initial_data()
            # 重新启动倒计时
            AutoRefresh.start_auto_refresh(self)
            
            # 如果赚钱效应评分也开启了自动刷新，重置它的倒计时以保持同步
            if hasattr(self, 'money_effect_auto_refresh') and self.money_effect_auto_refresh.isChecked():
                AutoRefresh.stop_money_effect_auto_refresh(self)
                AutoRefresh.start_money_effect_auto_refresh(self)
    
    @staticmethod
    def toggle_money_effect_auto_refresh(self, checked):
        """切换赚钱效应评分自动刷新状态"""
        if checked:
            # 确保数据模块已初始化
            if not self.data_loaded:
                self.update_status("正在初始化数据模块，请稍候...", "loading")
                self.init_data_modules()
                # 延迟启动自动刷新
                QTimer.singleShot(2000, lambda: self.toggle_money_effect_auto_refresh(True))
                return
                
            AutoRefresh.start_money_effect_auto_refresh(self)
        else:
            AutoRefresh.stop_money_effect_auto_refresh(self)
    
    @staticmethod
    def start_money_effect_auto_refresh(self):
        """启动赚钱效应评分自动刷新"""
        # 创建倒计时线程，使用赚钱效应评分专用的刷新间隔
        self.money_effect_countdown_thread = CountdownThread(self.money_effect_refresh_interval)
        self.money_effect_countdown_thread.update_signal.connect(self.update_money_effect_countdown)
        self.money_effect_countdown_thread.finished_signal.connect(self.on_money_effect_countdown_finished)
        self.money_effect_countdown_thread.start()
    
    @staticmethod
    def stop_money_effect_auto_refresh(self):
        """停止赚钱效应评分自动刷新"""
        if hasattr(self, 'money_effect_countdown_thread') and self.money_effect_countdown_thread.isRunning():
            self.money_effect_countdown_thread.stop()
            self.money_effect_countdown_thread.wait()
        
        self.money_effect_countdown_label.setText("")
    
    @staticmethod
    def update_money_effect_countdown(self, seconds):
        """更新赚钱效应评分倒计时显示"""
        self.money_effect_countdown_label.setText(f"{seconds}秒")
    
    @staticmethod
    def on_money_effect_countdown_finished(self):
        """赚钱效应评分倒计时结束处理"""
        if self.money_effect_auto_refresh.isChecked():
            # 刷新数据
            self.update_money_effect_score()
            # 重新启动倒计时
            AutoRefresh.start_money_effect_auto_refresh(self)
    
    @staticmethod
    def update_money_effect_refresh_interval(self, text):
        """更新赚钱效应评分刷新间隔"""
        intervals = {
            "5秒": 5,
            "10秒": 10,
            "30秒": 30,
            "1分钟": 60,
            "3分钟": 180,
            "5分钟": 300,
            "10分钟": 600,
            "30分钟": 1800,
            "1小时": 3600
        }
        
        self.money_effect_refresh_interval = intervals.get(text, 60)
        
        # 如果自动刷新已启用，重新启动倒计时
        if self.money_effect_auto_refresh.isChecked():
            AutoRefresh.stop_money_effect_auto_refresh(self)
            AutoRefresh.start_money_effect_auto_refresh(self)
    
    # 快讯自动刷新功能已移除