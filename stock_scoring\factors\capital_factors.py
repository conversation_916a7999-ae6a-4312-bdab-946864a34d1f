import numpy as np
import pandas as pd
from typing import Union, List, Tuple, Dict, Optional
from scipy import stats
from sklearn.preprocessing import StandardScaler

class CapitalFactors:
    """资金面因子计算类"""
    
    def __init__(self, data: pd.DataFrame):
        """
        初始化资金面因子计算类
        
        Parameters
        ----------
        data : pd.DataFrame
            包含资金流数据的DataFrame，必须包含以下列：
            - 日期
            - 收盘价
            - 涨跌幅
            - 主力净流入-净额
            - 主力净流入-净占比
            - 超大单净流入-净额
            - 超大单净流入-净占比
            - 大单净流入-净额
            - 大单净流入-净占比
            - 中单净流入-净额
            - 中单净流入-净占比
            - 小单净流入-净额
            - 小单净流入-净占比
        """
        self.data = data
        self._validate_data()
        
    def _validate_data(self):
        """验证输入数据的完整性"""
        required_columns = [
            '日期', '收盘价', '涨跌幅', 
            '主力净流入-净额', '主力净流入-净占比',
            '超大单净流入-净额', '超大单净流入-净占比',
            '大单净流入-净额', '大单净流入-净占比',
            '中单净流入-净额', '中单净流入-净占比',
            '小单净流入-净额', '小单净流入-净占比'
        ]
        missing_columns = [col for col in required_columns if col not in self.data.columns]
        if missing_columns:
            raise ValueError(f"数据缺少必要的列: {missing_columns}")
            
    def calculate_all_factors(self) -> pd.DataFrame:
        """计算所有资金面因子"""
        factors = pd.DataFrame(index=self.data.index)
        
        # 计算各类因子
        factors['smart_money_flow'] = self.smart_money_flow()
        factors['accumulation_index'] = self.accumulation_index()
        factors['fund_flow_strength'] = self.fund_flow_strength()
        factors['fund_flow_consistency'] = self.fund_flow_consistency()
        factors['fund_flow_momentum'] = self.fund_flow_momentum()
        factors['fund_flow_divergence'] = self.fund_flow_divergence()
        factors['fund_flow_concentration'] = self.fund_flow_concentration()
        factors['fund_flow_volatility'] = self.fund_flow_volatility()
        
        return factors
    
    def smart_money_flow(self, window: int = 5) -> pd.Series:
        """
        计算聪明资金流因子
        
        基于主力资金和超大单资金的净流入，结合价格变动计算聪明资金流强度
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为5
            
        Returns
        -------
        pd.Series
            聪明资金流因子序列
        """
        # 获取主力资金和超大单资金净流入
        main_net_inflow = self.data['主力净流入-净额'].values
        super_net_inflow = self.data['超大单净流入-净额'].values
        
        # 计算价格变动
        price_change = self.data['涨跌幅'].values / 100
        
        # 计算聪明资金流
        smart_flow = np.zeros(len(self.data))
        for i in range(window, len(self.data)):
            # 计算资金流与价格变动的相关性
            flow_corr = np.corrcoef(
                main_net_inflow[i-window:i] + super_net_inflow[i-window:i],
                price_change[i-window:i]
            )[0, 1]
            
            # 计算资金流强度
            flow_strength = np.sum(main_net_inflow[i-window:i] + super_net_inflow[i-window:i])
            
            # 综合评分
            smart_flow[i] = flow_corr * np.sign(flow_strength) * np.log1p(np.abs(flow_strength))
            
        return pd.Series(smart_flow, index=self.data.index)
    
    def accumulation_index(self, window: int = 20) -> pd.Series:
        """
        计算筹码集中度指标
        
        基于资金流向和价格变动计算筹码集中度
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为20
            
        Returns
        -------
        pd.Series
            筹码集中度指标序列
        """
        # 获取资金流数据
        main_net_inflow = self.data['主力净流入-净额'].values
        super_net_inflow = self.data['超大单净流入-净额'].values
        big_net_inflow = self.data['大单净流入-净额'].values
        
        # 计算价格变动
        price_change = self.data['涨跌幅'].values / 100
        
        # 计算筹码集中度
        acc_index = np.zeros(len(self.data))
        for i in range(window, len(self.data)):
            # 计算资金流入强度
            inflow_strength = np.sum(main_net_inflow[i-window:i] + 
                                    super_net_inflow[i-window:i] + 
                                    big_net_inflow[i-window:i])
            
            # 计算价格趋势
            price_trend = np.sum(price_change[i-window:i])
            
            # 计算筹码集中度
            acc_index[i] = np.sign(inflow_strength) * np.log1p(np.abs(inflow_strength)) * (1 + np.sign(price_trend))
            
        return pd.Series(acc_index, index=self.data.index)
    
    def fund_flow_strength(self, window: int = 5) -> pd.Series:
        """
        计算资金流强度因子
        
        综合评估各类资金流的强度
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为5
            
        Returns
        -------
        pd.Series
            资金流强度因子序列
        """
        # 获取各类资金流数据
        main_net_inflow = self.data['主力净流入-净额'].values
        super_net_inflow = self.data['超大单净流入-净额'].values
        big_net_inflow = self.data['大单净流入-净额'].values
        mid_net_inflow = self.data['中单净流入-净额'].values
        small_net_inflow = self.data['小单净流入-净额'].values
        
        # 计算资金流强度
        flow_strength = np.zeros(len(self.data))
        for i in range(window, len(self.data)):
            # 计算各类资金流的加权和
            weighted_flow = (
                2.0 * np.sum(main_net_inflow[i-window:i]) +
                1.5 * np.sum(super_net_inflow[i-window:i]) +
                1.0 * np.sum(big_net_inflow[i-window:i]) +
                0.5 * np.sum(mid_net_inflow[i-window:i]) +
                0.2 * np.sum(small_net_inflow[i-window:i])
            )
            
            # 标准化
            flow_strength[i] = weighted_flow / window
            
        return pd.Series(flow_strength, index=self.data.index)
    
    def fund_flow_consistency(self, window: int = 10) -> pd.Series:
        """
        计算资金流一致性因子
        
        评估资金流向的一致性，高一致性通常表示更强的趋势
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为10
            
        Returns
        -------
        pd.Series
            资金流一致性因子序列
        """
        # 获取资金流数据
        main_net_inflow = self.data['主力净流入-净额'].values
        super_net_inflow = self.data['超大单净流入-净额'].values
        big_net_inflow = self.data['大单净流入-净额'].values
        
        # 计算资金流一致性
        consistency = np.zeros(len(self.data))
        for i in range(window, len(self.data)):
            # 计算各类资金流的方向
            main_direction = np.sign(main_net_inflow[i-window:i])
            super_direction = np.sign(super_net_inflow[i-window:i])
            big_direction = np.sign(big_net_inflow[i-window:i])
            
            # 计算一致性得分
            main_consistency = np.sum(main_direction == np.sign(np.sum(main_direction))) / window
            super_consistency = np.sum(super_direction == np.sign(np.sum(super_direction))) / window
            big_consistency = np.sum(big_direction == np.sign(np.sum(big_direction))) / window
            
            # 综合评分
            consistency[i] = (main_consistency + super_consistency + big_consistency) / 3
            
        return pd.Series(consistency, index=self.data.index)
    
    def fund_flow_momentum(self, window: int = 5) -> pd.Series:
        """
        计算资金流动量因子
        
        评估资金流的动量特征，捕捉资金流的加速或减速趋势
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为5
            
        Returns
        -------
        pd.Series
            资金流动量因子序列
        """
        # 获取资金流数据
        main_net_inflow = self.data['主力净流入-净额'].values
        
        # 计算资金流动量
        momentum = np.zeros(len(self.data))
        for i in range(window, len(self.data)):
            # 计算资金流的加速度
            flow_values = main_net_inflow[i-window:i]
            flow_diff = np.diff(flow_values)
            
            # 计算动量得分
            if len(flow_diff) > 0:
                momentum[i] = np.mean(flow_diff) * np.sign(np.sum(flow_values))
            else:
                momentum[i] = 0
                
        return pd.Series(momentum, index=self.data.index)
    
    def fund_flow_divergence(self, window: int = 10) -> pd.Series:
        """
        计算资金流背离因子
        
        检测资金流与价格变动之间的背离，可能预示趋势反转
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为10
            
        Returns
        -------
        pd.Series
            资金流背离因子序列
        """
        # 获取资金流和价格数据
        main_net_inflow = self.data['主力净流入-净额'].values
        price_change = self.data['涨跌幅'].values / 100
        
        # 计算资金流背离
        divergence = np.zeros(len(self.data))
        for i in range(window, len(self.data)):
            # 计算资金流和价格变动的相关性
            flow_values = main_net_inflow[i-window:i]
            price_values = price_change[i-window:i]
            
            # 标准化
            flow_norm = (flow_values - np.mean(flow_values)) / (np.std(flow_values) + 1e-10)
            price_norm = (price_values - np.mean(price_values)) / (np.std(price_values) + 1e-10)
            
            # 计算背离得分
            correlation = np.corrcoef(flow_norm, price_norm)[0, 1]
            divergence[i] = -correlation  # 负相关表示背离
            
        return pd.Series(divergence, index=self.data.index)
    
    def fund_flow_concentration(self, window: int = 5) -> pd.Series:
        """
        计算资金流集中度因子
        
        评估资金流向的集中程度，高集中度通常表示更强的趋势
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为5
            
        Returns
        -------
        pd.Series
            资金流集中度因子序列
        """
        # 获取各类资金流数据
        main_net_inflow = self.data['主力净流入-净额'].values
        super_net_inflow = self.data['超大单净流入-净额'].values
        big_net_inflow = self.data['大单净流入-净额'].values
        mid_net_inflow = self.data['中单净流入-净额'].values
        small_net_inflow = self.data['小单净流入-净额'].values
        
        # 计算资金流集中度
        concentration = np.zeros(len(self.data))
        for i in range(window, len(self.data)):
            # 计算各类资金流的绝对值
            main_abs = np.abs(np.sum(main_net_inflow[i-window:i]))
            super_abs = np.abs(np.sum(super_net_inflow[i-window:i]))
            big_abs = np.abs(np.sum(big_net_inflow[i-window:i]))
            mid_abs = np.abs(np.sum(mid_net_inflow[i-window:i]))
            small_abs = np.abs(np.sum(small_net_inflow[i-window:i]))
            
            # 计算总资金流
            total_flow = main_abs + super_abs + big_abs + mid_abs + small_abs
            
            # 计算集中度得分 (使用赫芬达尔指数)
            if total_flow > 0:
                hhi = (main_abs/total_flow)**2 + (super_abs/total_flow)**2 + \
                      (big_abs/total_flow)**2 + (mid_abs/total_flow)**2 + \
                      (small_abs/total_flow)**2
                concentration[i] = hhi
            else:
                concentration[i] = 0
                
        return pd.Series(concentration, index=self.data.index)
    
    def fund_flow_volatility(self, window: int = 10) -> pd.Series:
        """
        计算资金流波动率因子
        
        评估资金流的波动性，高波动性可能表示市场情绪不稳定
        
        Parameters
        ----------
        window : int, optional
            计算窗口大小，默认为10
            
        Returns
        -------
        pd.Series
            资金流波动率因子序列
        """
        # 获取资金流数据
        main_net_inflow = self.data['主力净流入-净额'].values
        
        # 计算资金流波动率
        volatility = np.zeros(len(self.data))
        for i in range(window, len(self.data)):
            # 计算资金流的标准差
            flow_values = main_net_inflow[i-window:i]
            flow_std = np.std(flow_values)
            
            # 计算波动率得分
            volatility[i] = flow_std / (np.abs(np.mean(flow_values)) + 1e-10)
            
        return pd.Series(volatility, index=self.data.index)
    
    @staticmethod
    def get_fund_flow_data(stock_code: str, market: str = "sh", 
                          start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """
        获取个股资金流数据
        
        使用AKShare的stock_individual_fund_flow接口获取东方财富网-数据中心-个股资金流向数据
        单次获取指定市场和股票的近100个交易日的资金流数据
        
        Parameters
        ----------
        stock_code : str
            股票代码，例如："000425"
        market : str, optional
            市场代码，默认为"sh"（上海证券交易所），可选"sz"（深圳证券交易所）
        start_date : str, optional
            开始日期，格式为"YYYY-MM-DD"
        end_date : str, optional
            结束日期，格式为"YYYY-MM-DD"
            
        Returns
        -------
        pd.DataFrame
            个股资金流数据，包含以下列：
            - 日期
            - 收盘价
            - 涨跌幅 (注意单位: %)
            - 主力净流入-净额
            - 主力净流入-净占比 (注意单位: %)
            - 超大单净流入-净额
            - 超大单净流入-净占比 (注意单位: %)
            - 大单净流入-净额
            - 大单净流入-净占比 (注意单位: %)
            - 中单净流入-净额
            - 中单净流入-净占比 (注意单位: %)
            - 小单净流入-净额
            - 小单净流入-净占比 (注意单位: %)
        """
        try:
            import akshare as ak
            
            # 获取资金流数据
            df = ak.stock_individual_fund_flow(stock=stock_code, market=market)
            
            # 如果指定了日期范围，进行过滤
            if start_date and end_date:
                df['日期'] = pd.to_datetime(df['日期'])
                
                # 转换日期格式
                start = pd.to_datetime(start_date)
                end = pd.to_datetime(end_date)
                
                mask = (df['日期'] >= start) & (df['日期'] <= end)
                df = df[mask]
                
            # 确保所有的金额列是数值类型
            numeric_columns = [
                '收盘价', '涨跌幅', 
                '主力净流入-净额', '主力净流入-净占比',
                '超大单净流入-净额', '超大单净流入-净占比',
                '大单净流入-净额', '大单净流入-净占比',
                '中单净流入-净额', '中单净流入-净占比',
                '小单净流入-净额', '小单净流入-净占比'
            ]
            
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            return df
            
        except ImportError:
            print("请安装AKShare库: pip install akshare")
            return pd.DataFrame()
        except Exception as e:
            print(f"获取资金流数据失败: {e}")
            return pd.DataFrame() 