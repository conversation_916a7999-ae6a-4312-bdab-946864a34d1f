# 技术分析模块

本模块提供了股票技术指标计算和分析功能，是AI股票分析系统的核心组件之一。

## 模块结构

```
technical_analysis/
├── stock_analyzer.py      # 股票技术分析器
├── historical_data.py     # 历史数据处理
├── fast_csv_reader.py     # 快速CSV读取工具
└── __init__.py
```

## 主要功能

### 1. 股票技术分析器 (stock_analyzer.py)

提供丰富的技术指标计算和分析功能。

**主要功能**：
- 趋势指标：移动平均线(MA)、指数移动平均线(EMA)、MACD、KDJ、RSI等
- 波动指标：布林带(BOLL)、ATR、标准差带等
- 成交量指标：成交量(Volume)、OBV、CMF等
- 自定义指标：综合动量指标、价格波动率指标等
- 形态识别：头肩顶/底、双顶/双底、三重顶/底、黄金交叉/死亡交叉等
- 技术信号：生成基于技术指标的买入卖出信号

### 2. 历史数据处理 (historical_data.py)

负责获取、清洗和管理股票历史数据。

**主要功能**：
- 数据获取：从多种数据源获取股票历史数据
- 数据缓存：实现本地数据缓存，提高读取效率
- 数据清洗：处理缺失值、异常值等
- 数据格式化：统一数据格式，便于后续分析
- 数据更新：定期自动更新历史数据

### 3. 快速CSV读取工具 (fast_csv_reader.py)

针对大型CSV文件的高性能读取工具。

**主要功能**：
- 流式读取：支持大文件的流式读取，降低内存占用
- 多线程处理：并行读取与处理数据
- 数据筛选：在读取过程中进行数据筛选
- 性能优化：使用mmap和numpy实现高效读取

## 使用示例

### 基本技术分析

```python
from technical_analysis import StockAnalyzer

# 创建分析器实例
analyzer = StockAnalyzer(stock_code="600519")

# 计算常用技术指标
indicators = analyzer.calculate_indicators(
    indicators=["MA", "MACD", "KDJ", "RSI", "BOLL"],
    periods={"MA": [5, 10, 20, 60], "RSI": [6, 14]}
)

# 获取分析结果
result = analyzer.analyze_indicators(indicators)

# 输出技术分析结论
print(f"股票代码: {result['stock_code']}")
print(f"技术评分: {result['technical_score']}")
print(f"趋势评价: {result['trend']}")
print(f"支撑位: {result['support_levels']}")
print(f"阻力位: {result['resistance_levels']}")
print(f"技术信号: {result['signals']}")
```

### 获取历史数据

```python
from technical_analysis import HistoricalData

# 创建历史数据对象
history = HistoricalData()

# 获取股票数据
data = history.get_stock_data("600519", start_date="2023-01-01", end_date="2023-12-31")

# 获取日K线数据
daily_data = history.get_daily_kline("600519", days=60)

# 获取分钟级数据
minute_data = history.get_minute_data("600519", minutes=30)
```

### 使用快速CSV读取工具

```python
from technical_analysis import FastCSVReader

# 创建读取器
reader = FastCSVReader("data/huge_stock_data.csv")

# 按条件筛选数据
filtered_data = reader.filter_by(
    conditions={"close": "> 100", "volume": "> 1000000"}
)

# 获取指定列
selected_columns = reader.select_columns(["date", "open", "high", "low", "close", "volume"])

# 批量处理数据
results = reader.batch_process(
    chunk_size=10000,
    process_func=lambda chunk: chunk["close"].mean()
)
```

## 高级功能

### 1. 形态识别

```python
from technical_analysis import StockAnalyzer

analyzer = StockAnalyzer("000651")
patterns = analyzer.detect_patterns(
    pattern_types=["HEAD_SHOULDER", "DOUBLE_TOP", "TRIANGLE"]
)

for pattern in patterns:
    print(f"发现形态: {pattern['type']}")
    print(f"形成位置: {pattern['position']}")
    print(f"可信度: {pattern['confidence']}")
    print(f"预期目标价: {pattern['target_price']}")
```

### 2. 多周期分析

```python
from technical_analysis import StockAnalyzer

analyzer = StockAnalyzer("600036")
multi_periods = analyzer.multi_period_analysis(
    periods=["日线", "周线", "月线"],
    indicators=["MA", "MACD", "RSI"]
)

print(f"多周期分析结果: {multi_periods['conclusion']}")
print(f"各周期一致性: {multi_periods['consistency']}")
```

### 3. 高级信号系统

```python
from technical_analysis import StockAnalyzer

analyzer = StockAnalyzer("601318")
advanced_signals = analyzer.generate_advanced_signals(
    signal_types=["趋势突破", "反转信号", "动量变化"],
    lookback_days=60
)

for signal in advanced_signals:
    print(f"信号类型: {signal['type']}")
    print(f"触发时间: {signal['time']}")
    print(f"信号强度: {signal['strength']}")
    print(f"建议操作: {signal['action']}")
```

## 性能优化

- 使用了numpy和pandas进行向量化计算，大幅提升计算速度
- 实现了智能缓存机制，避免重复计算
- 支持多线程并行计算多只股票的技术指标

## 注意事项

1. 本模块提供的技术分析结果仅供参考，不构成投资建议
2. 技术指标的有效性会受到市场环境变化的影响
3. 建议结合基本面分析和市场情绪等多维度数据进行决策 