from PyQt6.QtCore import QObject, QThreadPool, QRunnable, pyqtSignal, pyqtSlot
import logging
from typing import Callable, Any, Dict
from enum import Enum

class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 0
    NORMAL = 1
    HIGH = 2

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = 0
    RUNNING = 1
    COMPLETED = 2
    FAILED = 3
    CANCELLED = 4

class Task(QRunnable):
    """任务基类，封装具体的异步操作"""
    
    class Signals(QObject):
        started = pyqtSignal()
        finished = pyqtSignal(object)
        error = pyqtSignal(str)
        progress = pyqtSignal(int)
        status_changed = pyqtSignal(TaskStatus)
    
    def __init__(self, func: Callable, *args, priority: TaskPriority = TaskPriority.NORMAL, **kwargs):
        super().__init__()
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.priority = priority
        self.signals = self.Signals()
        self._status = TaskStatus.PENDING
        self.setAutoDelete(True)
    
    @property
    def status(self) -> TaskStatus:
        return self._status
    
    @status.setter
    def status(self, value: TaskStatus):
        if self._status != value:
            self._status = value
            self.signals.status_changed.emit(value)
    
    def run(self):
        try:
            self.status = TaskStatus.RUNNING
            self.signals.started.emit()
            result = self.func(*self.args, **self.kwargs)
            self.status = TaskStatus.COMPLETED
            self.signals.finished.emit(result)
        except Exception as e:
            self.status = TaskStatus.FAILED
            logging.error(f"Task execution failed: {str(e)}")
            self.signals.error.emit(str(e))

class ThreadManager(QObject):
    """线程池管理器，负责管理所有异步任务"""
    
    def __init__(self, max_thread_count: int = None):
        super().__init__()
        self.thread_pool = QThreadPool.globalInstance()
        if max_thread_count is not None:
            self.thread_pool.setMaxThreadCount(max_thread_count)
        self.active_tasks: Dict[int, Task] = {}
        logging.info(f"Thread pool initialized with {self.thread_pool.maxThreadCount()} threads")
    
    def submit_task(self, task: Task) -> None:
        """提交任务到线程池"""
        # 设置任务优先级
        task.setAutoDelete(True)
        if task.priority == TaskPriority.HIGH:
            self.thread_pool.start(task, QThreadPool.HighPriority)
        elif task.priority == TaskPriority.LOW:
            self.thread_pool.start(task, QThreadPool.LowPriority)
        else:
            self.thread_pool.start(task)
        
        # 记录活动任务
        task_id = id(task)
        self.active_tasks[task_id] = task
        
        # 清理已完成的任务
        def cleanup(result=None):
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
        
        task.signals.finished.connect(cleanup)
        task.signals.error.connect(lambda _: cleanup())
    
    def create_task(self, func: Callable, *args, priority: TaskPriority = TaskPriority.NORMAL,
                    on_started: Callable = None, on_finished: Callable = None,
                    on_error: Callable = None, on_progress: Callable = None,
                    **kwargs) -> Task:
        """创建并提交任务到线程池"""
        task = Task(func, *args, priority=priority, **kwargs)
        
        if on_started:
            task.signals.started.connect(on_started)
        if on_finished:
            task.signals.finished.connect(on_finished)
        if on_error:
            task.signals.error.connect(on_error)
        if on_progress:
            task.signals.progress.connect(on_progress)
        
        self.submit_task(task)
        return task
    
    def active_task_count(self) -> int:
        """获取当前活动任务数量"""
        return len(self.active_tasks)
    
    def wait_for_done(self) -> None:
        """等待所有任务完成"""
        self.thread_pool.waitForDone()