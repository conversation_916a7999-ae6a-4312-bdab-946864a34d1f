#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
健壮的CSV加载模块

提供可靠的CSV文件加载功能，处理各种编码和格式问题
"""

import os
import csv
import logging
import pandas as pd
from typing import Dict, List, Any, Union, Optional

# 导入日志配置模块
from utils.logging_config import get_logger

# 配置日志
logger = get_logger('utils.robust_csv_loader')

# 尝试的编码列表
ENCODINGS = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1', 'iso-8859-1', 'utf-16', 'utf-32']

def load_csv_robust(file_path: str, 
                   delimiter: str = ',', 
                   encoding: Optional[str] = None,
                   use_pandas: bool = True,
                   **kwargs) -> Union[Dict[str, Any], pd.DataFrame, List[Dict[str, Any]]]:
    """
    健壮的CSV文件加载函数，自动处理编码和格式问题
    
    Args:
        file_path: CSV文件路径
        delimiter: 分隔符，默认为逗号
        encoding: 指定编码，如果为None则自动尝试多种编码
        use_pandas: 是否使用pandas加载，默认为True
        **kwargs: 传递给pandas.read_csv或csv.DictReader的额外参数
        
    Returns:
        加载的CSV数据，根据use_pandas参数返回DataFrame或字典列表
    """
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        return {} if not use_pandas else pd.DataFrame()
    
    # 如果指定了编码，直接尝试加载
    if encoding:
        try:
            if use_pandas:
                return pd.read_csv(file_path, delimiter=delimiter, encoding=encoding, **kwargs)
            else:
                with open(file_path, 'r', encoding=encoding) as f:
                    reader = csv.DictReader(f, delimiter=delimiter)
                    return list(reader)
        except Exception as e:
            logger.warning(f"使用指定编码 {encoding} 加载CSV文件失败: {str(e)}")
    
    # 尝试多种编码
    for enc in ENCODINGS:
        try:
            if use_pandas:
                df = pd.read_csv(file_path, delimiter=delimiter, encoding=enc, **kwargs)
                logger.info(f"成功使用编码 {enc} 加载CSV文件: {file_path}")
                return df
            else:
                with open(file_path, 'r', encoding=enc) as f:
                    reader = csv.DictReader(f, delimiter=delimiter)
                    data = list(reader)
                    logger.info(f"成功使用编码 {enc} 加载CSV文件: {file_path}")
                    return data
        except UnicodeDecodeError:
            # 编码错误，尝试下一种编码
            continue
        except Exception as e:
            # 其他错误，记录并尝试下一种编码
            logger.warning(f"使用编码 {enc} 加载CSV文件时出错: {str(e)}")
            continue
    
    # 所有编码都失败，尝试二进制模式
    try:
        if use_pandas:
            # 使用pandas的二进制模式
            df = pd.read_csv(file_path, delimiter=delimiter, **kwargs)
            logger.info(f"成功使用二进制模式加载CSV文件: {file_path}")
            return df
        else:
            # 使用Python标准库的二进制模式
            with open(file_path, 'rb') as f:
                # 尝试检测编码
                import chardet
                raw_data = f.read()
                result = chardet.detect(raw_data)
                detected_encoding = result['encoding']
                
                # 使用检测到的编码重新打开
                with open(file_path, 'r', encoding=detected_encoding) as f2:
                    reader = csv.DictReader(f2, delimiter=delimiter)
                    data = list(reader)
                    logger.info(f"成功使用检测到的编码 {detected_encoding} 加载CSV文件: {file_path}")
                    return data
    except Exception as e:
        logger.error(f"所有尝试都失败，无法加载CSV文件 {file_path}: {str(e)}")
        # 返回空数据
        return {} if not use_pandas else pd.DataFrame()

def load_csv_as_dict(file_path: str, 
                    key_column: str, 
                    value_column: Optional[str] = None,
                    delimiter: str = ',', 
                    encoding: Optional[str] = None,
                    **kwargs) -> Dict[str, Any]:
    """
    将CSV文件加载为字典格式
    
    Args:
        file_path: CSV文件路径
        key_column: 作为字典键的列名
        value_column: 作为字典值的列名，如果为None则整行作为值
        delimiter: 分隔符，默认为逗号
        encoding: 指定编码，如果为None则自动尝试多种编码
        **kwargs: 传递给load_csv_robust的额外参数
        
    Returns:
        字典格式的CSV数据
    """
    # 使用健壮的CSV加载函数加载数据
    data = load_csv_robust(file_path, delimiter=delimiter, encoding=encoding, use_pandas=True, **kwargs)
    
    if isinstance(data, pd.DataFrame) and not data.empty:
        # 检查key_column是否存在
        if key_column not in data.columns:
            logger.error(f"键列 {key_column} 不存在于CSV文件中")
            return {}
        
        # 如果指定了value_column，检查是否存在
        if value_column and value_column not in data.columns:
            logger.error(f"值列 {value_column} 不存在于CSV文件中")
            return {}
        
        # 转换为字典
        if value_column:
            # 使用指定的值列
            result = dict(zip(data[key_column], data[value_column]))
        else:
            # 使用整行作为值
            result = {row[key_column]: row.to_dict() for _, row in data.iterrows()}
        
        return result
    
    return {}
