#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证配置管理模块
提供验证规则的配置化管理、动态加载和热更新功能

作者: LilyBullRider Team
创建时间: 2024
"""

import json
import yaml
from typing import Dict, List, Any, Optional, Union, Callable
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from threading import Lock
import os

from .data_validator import ValidationLevel, DataValidator
from .validation_rules import (
    StockCodeValidator, PriceValidator, VolumeValidator,
    MarketCapValidator, FinancialRatioValidator, TradingDateValidator,
    DataConsistencyValidator, StockDataValidatorFactory
)
from .data_cleaner import CleaningStrategy, OutlierDetectionMethod, CleaningRule

# 配置日志
logger = logging.getLogger(__name__)

class ConfigFormat(Enum):
    """配置文件格式枚举"""
    JSON = "json"
    YAML = "yaml"
    YML = "yml"

class ValidationMode(Enum):
    """验证模式枚举"""
    STRICT = "strict"        # 严格模式
    NORMAL = "normal"        # 正常模式
    LENIENT = "lenient"      # 宽松模式
    CUSTOM = "custom"        # 自定义模式

@dataclass
class ValidationRuleConfig:
    """验证规则配置"""
    rule_name: str
    rule_type: str
    enabled: bool = True
    level: str = "ERROR"
    parameters: Dict[str, Any] = None
    description: str = ""
    created_time: str = ""
    updated_time: str = ""
    
    def __post_init__(self):
        if not self.created_time:
            self.created_time = datetime.now().isoformat()
        if not self.updated_time:
            self.updated_time = datetime.now().isoformat()
        if self.parameters is None:
            self.parameters = {}

@dataclass
class CleaningRuleConfig:
    """清洗规则配置"""
    rule_name: str
    field_name: str
    strategy: str
    enabled: bool = True
    priority: int = 0
    parameters: Dict[str, Any] = None
    condition: Optional[str] = None
    description: str = ""
    created_time: str = ""
    updated_time: str = ""
    
    def __post_init__(self):
        if not self.created_time:
            self.created_time = datetime.now().isoformat()
        if not self.updated_time:
            self.updated_time = datetime.now().isoformat()
        if self.parameters is None:
            self.parameters = {}

@dataclass
class ValidationConfig:
    """完整的验证配置"""
    config_name: str
    version: str = "1.0.0"
    mode: str = "normal"
    validation_rules: List[ValidationRuleConfig] = None
    cleaning_rules: List[CleaningRuleConfig] = None
    global_settings: Dict[str, Any] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.validation_rules is None:
            self.validation_rules = []
        if self.cleaning_rules is None:
            self.cleaning_rules = []
        if self.global_settings is None:
            self.global_settings = {}
        if self.metadata is None:
            self.metadata = {
                'created_time': datetime.now().isoformat(),
                'updated_time': datetime.now().isoformat(),
                'author': 'LilyBullRider',
                'description': ''
            }

class ValidationConfigManager:
    """验证配置管理器"""
    
    def __init__(self, config_dir: str = "config/validation"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self.current_config: Optional[ValidationConfig] = None
        self.config_cache: Dict[str, ValidationConfig] = {}
        self.file_watchers: Dict[str, float] = {}  # 文件修改时间缓存
        self._lock = Lock()
        
        # 默认配置文件路径
        self.default_config_file = self.config_dir / "default_validation_config.yaml"
        
        # 初始化默认配置
        self._ensure_default_config()
    
    def _ensure_default_config(self):
        """确保默认配置文件存在"""
        if not self.default_config_file.exists():
            default_config = self._create_default_config()
            self.save_config(default_config, self.default_config_file)
            logger.info(f"创建默认验证配置文件: {self.default_config_file}")
    
    def _create_default_config(self) -> ValidationConfig:
        """创建默认验证配置"""
        validation_rules = [
            # 股票代码验证
            ValidationRuleConfig(
                rule_name="stock_code_validation",
                rule_type="StockCodeValidator",
                level="ERROR",
                parameters={"allow_empty": False},
                description="验证股票代码格式"
            ),
            
            # 价格验证
            ValidationRuleConfig(
                rule_name="price_validation",
                rule_type="PriceValidator",
                level="ERROR",
                parameters={"min_price": 0.01, "max_price": 10000.0},
                description="验证股票价格范围"
            ),
            
            # 成交量验证
            ValidationRuleConfig(
                rule_name="volume_validation",
                rule_type="VolumeValidator",
                level="WARNING",
                parameters={"min_volume": 0, "max_volume": 1000000000},
                description="验证成交量范围"
            ),
            
            # 交易日期验证
            ValidationRuleConfig(
                rule_name="trading_date_validation",
                rule_type="TradingDateValidator",
                level="ERROR",
                parameters={"check_weekend": True, "check_holiday": False},
                description="验证交易日期有效性"
            ),
            
            # 财务比率验证
            ValidationRuleConfig(
                rule_name="financial_ratio_validation",
                rule_type="FinancialRatioValidator",
                level="WARNING",
                parameters={"pe_range": [-100, 1000], "pb_range": [0, 50]},
                description="验证财务比率合理性"
            ),
        ]
        
        cleaning_rules = [
            # 价格字段清洗
            CleaningRuleConfig(
                rule_name="price_missing_fill",
                field_name="close",
                strategy="fill_forward",
                priority=10,
                description="价格缺失值前向填充"
            ),
            
            # 成交量异常值处理
            CleaningRuleConfig(
                rule_name="volume_outlier_cap",
                field_name="volume",
                strategy="cap_outliers",
                priority=8,
                parameters={
                    "detection_method": "iqr",
                    "multiplier": 2.0
                },
                description="成交量异常值截断"
            ),
            
            # PE比率异常值处理
            CleaningRuleConfig(
                rule_name="pe_ratio_outlier_cap",
                field_name="pe_ratio",
                strategy="cap_outliers",
                priority=7,
                parameters={
                    "detection_method": "iqr",
                    "multiplier": 3.0
                },
                description="PE比率异常值截断"
            ),
        ]
        
        global_settings = {
            "max_validation_time": 30.0,  # 最大验证时间（秒）
            "enable_caching": True,       # 启用验证结果缓存
            "cache_ttl": 300,            # 缓存生存时间（秒）
            "enable_logging": True,       # 启用详细日志
            "log_level": "INFO",         # 日志级别
            "parallel_validation": True,  # 并行验证
            "max_workers": 4,            # 最大工作线程数
            "enable_metrics": True,      # 启用性能指标收集
        }
        
        return ValidationConfig(
            config_name="default",
            version="1.0.0",
            mode="normal",
            validation_rules=validation_rules,
            cleaning_rules=cleaning_rules,
            global_settings=global_settings,
            metadata={
                'created_time': datetime.now().isoformat(),
                'updated_time': datetime.now().isoformat(),
                'author': 'LilyBullRider',
                'description': '默认数据验证和清洗配置'
            }
        )
    
    def save_config(self, config: ValidationConfig, file_path: Union[str, Path]):
        """保存配置到文件"""
        file_path = Path(file_path)
        
        # 更新时间戳
        config.metadata['updated_time'] = datetime.now().isoformat()
        
        # 转换为字典
        config_dict = asdict(config)
        
        try:
            with self._lock:
                if file_path.suffix.lower() in ['.yaml', '.yml']:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        yaml.dump(config_dict, f, default_flow_style=False, 
                                allow_unicode=True, indent=2)
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(config_dict, f, indent=2, ensure_ascii=False)
                
                # 更新文件监控
                self.file_watchers[str(file_path)] = file_path.stat().st_mtime
                
                logger.info(f"配置已保存到: {file_path}")
        
        except Exception as e:
            logger.error(f"保存配置失败 {file_path}: {str(e)}")
            raise
    
    def load_config(self, file_path: Union[str, Path]) -> ValidationConfig:
        """从文件加载配置"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {file_path}")
        
        try:
            with self._lock:
                if file_path.suffix.lower() in ['.yaml', '.yml']:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config_dict = yaml.safe_load(f)
                else:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config_dict = json.load(f)
                
                # 转换为配置对象
                config = self._dict_to_config(config_dict)
                
                # 缓存配置
                self.config_cache[config.config_name] = config
                
                # 更新文件监控
                self.file_watchers[str(file_path)] = file_path.stat().st_mtime
                
                logger.info(f"配置已加载: {file_path}")
                return config
        
        except Exception as e:
            logger.error(f"加载配置失败 {file_path}: {str(e)}")
            raise
    
    def _dict_to_config(self, config_dict: Dict[str, Any]) -> ValidationConfig:
        """将字典转换为配置对象"""
        # 转换验证规则
        validation_rules = []
        for rule_dict in config_dict.get('validation_rules', []):
            validation_rules.append(ValidationRuleConfig(**rule_dict))
        
        # 转换清洗规则
        cleaning_rules = []
        for rule_dict in config_dict.get('cleaning_rules', []):
            cleaning_rules.append(CleaningRuleConfig(**rule_dict))
        
        return ValidationConfig(
            config_name=config_dict.get('config_name', 'unnamed'),
            version=config_dict.get('version', '1.0.0'),
            mode=config_dict.get('mode', 'normal'),
            validation_rules=validation_rules,
            cleaning_rules=cleaning_rules,
            global_settings=config_dict.get('global_settings', {}),
            metadata=config_dict.get('metadata', {})
        )
    
    def load_default_config(self) -> ValidationConfig:
        """加载默认配置"""
        return self.load_config(self.default_config_file)
    
    def set_current_config(self, config: ValidationConfig):
        """设置当前使用的配置"""
        with self._lock:
            self.current_config = config
            logger.info(f"当前配置已设置为: {config.config_name}")
    
    def get_current_config(self) -> Optional[ValidationConfig]:
        """获取当前配置"""
        if self.current_config is None:
            self.current_config = self.load_default_config()
        return self.current_config
    
    def check_config_updates(self) -> List[str]:
        """检查配置文件更新"""
        updated_files = []
        
        with self._lock:
            for file_path, cached_mtime in self.file_watchers.items():
                try:
                    current_mtime = Path(file_path).stat().st_mtime
                    if current_mtime > cached_mtime:
                        updated_files.append(file_path)
                        self.file_watchers[file_path] = current_mtime
                except FileNotFoundError:
                    # 文件被删除
                    updated_files.append(file_path)
                    del self.file_watchers[file_path]
        
        return updated_files
    
    def reload_config_if_changed(self, file_path: Union[str, Path]) -> bool:
        """如果配置文件有变化则重新加载"""
        file_path = Path(file_path)
        str_path = str(file_path)
        
        if str_path in self.file_watchers:
            try:
                current_mtime = file_path.stat().st_mtime
                if current_mtime > self.file_watchers[str_path]:
                    self.load_config(file_path)
                    logger.info(f"配置文件已热更新: {file_path}")
                    return True
            except FileNotFoundError:
                logger.warning(f"配置文件不存在: {file_path}")
                del self.file_watchers[str_path]
        
        return False
    
    def create_validator_from_config(self, config: Optional[ValidationConfig] = None) -> DataValidator:
        """根据配置创建数据验证器"""
        if config is None:
            config = self.get_current_config()
        
        # 根据模式选择验证器
        if config.mode == "strict":
            validator = StockDataValidatorFactory.create_strict_validator()
        elif config.mode == "lenient":
            validator = StockDataValidatorFactory.create_lenient_validator()
        elif config.mode == "custom":
            validator = DataValidator()
            # 根据配置添加自定义验证规则
            for rule_config in config.validation_rules:
                if rule_config.enabled:
                    validator_instance = self._create_validator_instance(rule_config)
                    if validator_instance:
                        validator.add_validator(validator_instance)
        else:  # normal
            validator = StockDataValidatorFactory.create_basic_stock_validator()
        
        return validator
    
    def _create_validator_instance(self, rule_config: ValidationRuleConfig):
        """根据规则配置创建验证器实例"""
        validator_classes = {
            'StockCodeValidator': StockCodeValidator,
            'PriceValidator': PriceValidator,
            'VolumeValidator': VolumeValidator,
            'MarketCapValidator': MarketCapValidator,
            'FinancialRatioValidator': FinancialRatioValidator,
            'TradingDateValidator': TradingDateValidator,
            'DataConsistencyValidator': DataConsistencyValidator,
        }
        
        validator_class = validator_classes.get(rule_config.rule_type)
        if validator_class:
            try:
                # 获取验证级别
                level = getattr(ValidationLevel, rule_config.level, ValidationLevel.ERROR)
                
                # 创建验证器实例
                if rule_config.parameters:
                    return validator_class(level=level, **rule_config.parameters)
                else:
                    return validator_class(level=level)
            except Exception as e:
                logger.error(f"创建验证器失败 {rule_config.rule_type}: {str(e)}")
        
        return None
    
    def create_cleaning_rules_from_config(self, config: Optional[ValidationConfig] = None) -> List[CleaningRule]:
        """根据配置创建清洗规则"""
        if config is None:
            config = self.get_current_config()
        
        cleaning_rules = []
        
        for rule_config in config.cleaning_rules:
            if rule_config.enabled:
                try:
                    # 转换策略枚举
                    strategy = getattr(CleaningStrategy, rule_config.strategy.upper(), None)
                    if strategy is None:
                        logger.warning(f"未知的清洗策略: {rule_config.strategy}")
                        continue
                    
                    # 创建清洗规则
                    cleaning_rule = CleaningRule(
                        field_name=rule_config.field_name,
                        strategy=strategy,
                        parameters=rule_config.parameters,
                        priority=rule_config.priority
                    )
                    
                    cleaning_rules.append(cleaning_rule)
                
                except Exception as e:
                    logger.error(f"创建清洗规则失败 {rule_config.rule_name}: {str(e)}")
        
        return cleaning_rules
    
    def export_config_template(self, file_path: Union[str, Path], format_type: ConfigFormat = ConfigFormat.YAML):
        """导出配置模板"""
        template_config = self._create_default_config()
        template_config.config_name = "template"
        template_config.metadata['description'] = "配置模板文件"
        
        file_path = Path(file_path)
        if format_type == ConfigFormat.JSON:
            file_path = file_path.with_suffix('.json')
        else:
            file_path = file_path.with_suffix('.yaml')
        
        self.save_config(template_config, file_path)
        logger.info(f"配置模板已导出: {file_path}")
    
    def validate_config(self, config: ValidationConfig) -> List[str]:
        """验证配置的有效性"""
        errors = []
        
        # 检查基本字段
        if not config.config_name:
            errors.append("配置名称不能为空")
        
        if not config.version:
            errors.append("版本号不能为空")
        
        if config.mode not in ['strict', 'normal', 'lenient', 'custom']:
            errors.append(f"无效的验证模式: {config.mode}")
        
        # 检查验证规则
        for i, rule in enumerate(config.validation_rules):
            if not rule.rule_name:
                errors.append(f"验证规则 {i} 缺少规则名称")
            
            if not rule.rule_type:
                errors.append(f"验证规则 {rule.rule_name} 缺少规则类型")
            
            if rule.level not in ['DEBUG', 'INFO', 'WARNING', 'ERROR']:
                errors.append(f"验证规则 {rule.rule_name} 无效的级别: {rule.level}")
        
        # 检查清洗规则
        for i, rule in enumerate(config.cleaning_rules):
            if not rule.rule_name:
                errors.append(f"清洗规则 {i} 缺少规则名称")
            
            if not rule.field_name:
                errors.append(f"清洗规则 {rule.rule_name} 缺少字段名称")
            
            if not rule.strategy:
                errors.append(f"清洗规则 {rule.rule_name} 缺少清洗策略")
        
        return errors
    
    def list_available_configs(self) -> List[Dict[str, Any]]:
        """列出可用的配置文件"""
        configs = []
        
        for file_path in self.config_dir.glob('*.yaml'):
            try:
                config = self.load_config(file_path)
                configs.append({
                    'name': config.config_name,
                    'version': config.version,
                    'mode': config.mode,
                    'file_path': str(file_path),
                    'metadata': config.metadata
                })
            except Exception as e:
                logger.warning(f"无法加载配置文件 {file_path}: {str(e)}")
        
        for file_path in self.config_dir.glob('*.json'):
            try:
                config = self.load_config(file_path)
                configs.append({
                    'name': config.config_name,
                    'version': config.version,
                    'mode': config.mode,
                    'file_path': str(file_path),
                    'metadata': config.metadata
                })
            except Exception as e:
                logger.warning(f"无法加载配置文件 {file_path}: {str(e)}")
        
        return configs

# 全局配置管理器实例
config_manager = ValidationConfigManager()

# 便捷函数
def get_current_validator() -> DataValidator:
    """获取当前配置的验证器"""
    return config_manager.create_validator_from_config()

def get_current_cleaning_rules() -> List[CleaningRule]:
    """获取当前配置的清洗规则"""
    return config_manager.create_cleaning_rules_from_config()

def reload_config():
    """重新加载配置"""
    config_manager.current_config = None
    return config_manager.get_current_config()