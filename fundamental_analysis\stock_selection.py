# stock_selection.py
import concurrent.futures
import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime
import argparse
from scipy import stats
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import warnings
warnings.filterwarnings('ignore')
from statsmodels.tsa.stattools import adfuller
from .normalization_engine import NormalizationEngine, adaptive_normalize
from .technical_indicators_optimized import TechnicalIndicatorsOptimized
from numba import jit
import time
import os
import json

# 使用统一的日志配置系统
from utils.logging_config import get_logger

# 获取配置好的logger实例
logger = get_logger(__name__)


class SectorStrengthCalculator:
    """板块强度动态计算引擎"""

    def __init__(self, use_basic_analysis=True):
        self.use_basic_analysis = use_basic_analysis
        self.time_weights = {
            'morning': {'main_ratio': 0.4, 'super': 0.3, 'change': 0.2, 'retail': 0.1},
            'midday': {'main_ratio': 0.35, 'super': 0.25, 'change': 0.3, 'retail': 0.1},
            'late': {'main_ratio': 0.3, 'super': 0.2, 'change': 0.4, 'retail': 0.1}
        }

        # 多层次时间维度评分 (扩展)
        self.enhanced_time_weights = {
            'open_auction': {'main_ratio': 0.5, 'super': 0.3, 'change': 0.1, 'retail': 0.1},  # 开盘集合竞价
            'early_morning': {'main_ratio': 0.45, 'super': 0.3, 'change': 0.15, 'retail': 0.1},  # 9:30-10:00
            'morning': {'main_ratio': 0.4, 'super': 0.3, 'change': 0.2, 'retail': 0.1},  # 10:00-11:30
            'midday': {'main_ratio': 0.35, 'super': 0.25, 'change': 0.3, 'retail': 0.1},  # 13:00-14:00
            'late': {'main_ratio': 0.3, 'super': 0.2, 'change': 0.4, 'retail': 0.1},  # 14:00-14:45
            'closing': {'main_ratio': 0.25, 'super': 0.15, 'change': 0.5, 'retail': 0.1}  # 14:45-15:00
        }

        # 动态权重分配矩阵 (修复版 - 重新校准)
        self.weight_matrix = {
            'high_volatility': {
                'capital': 0.35,      # 资金流权重：0-100范围
                'technical': 0.25,    # 技术指标权重：0-100范围
                'fundamental': 0.20,  # 基本面权重：0-100范围
                'basic_analysis': 0.15, # 基础分析权重：0-100范围
                'risk': -0.05         # 风险惩罚权重：-50~0范围，负值表示惩罚
            },
            'low_volatility': {
                'capital': 0.30,
                'technical': 0.20,
                'fundamental': 0.25,
                'basic_analysis': 0.20,
                'risk': -0.05
            },
            'bull_trend': {
                'capital': 0.40,
                'technical': 0.30,
                'fundamental': 0.15,
                'basic_analysis': 0.12,
                'risk': -0.03         # 牛市时风险权重较小
            },
            'bear_trend': {
                'capital': 0.25,
                'technical': 0.15,
                'fundamental': 0.30,
                'basic_analysis': 0.25,
                'risk': -0.05         # 熊市时风险权重较大
            },
            'default': {
                'capital': 0.35,
                'technical': 0.25,
                'fundamental': 0.20,
                'basic_analysis': 0.15,
                'risk': -0.05
            }
        }

        # 基础分析模块已加载

        # 历史数据缓存
        self.historical_data = {}
        self.factor_memory = {}
        
        # 初始化优化的技术指标计算器
        self.technical_optimizer = TechnicalIndicatorsOptimized(self.historical_data)

        logger.info("使用基础分析模式 + 优化技术指标系统")

    # 辅助函数: Z-Score标准化
    def _zscore(self, series):
        """自适应标准化数据 - 修复版：保持数据差异性"""
        if series.isna().all():
            return pd.Series(50, index=series.index)  # 使用0-100范围的中性值

        try:
            # 使用新的标准化引擎
            if not hasattr(self, '_normalizer'):
                self._normalizer = NormalizationEngine(target_range=(0, 100))
            
            # 自适应标准化，保持数据差异性
            normalized = self._normalizer.normalize_series(series, method='auto', track_stats=True)
            
            # 确保结果有效
            if normalized.isna().any():
                normalized = normalized.fillna(50)  # 使用中性值填充
            
            return normalized
            
        except Exception as e:
            logger.warning(f"自适应标准化异常: {e}，使用备用方法")
            # 备用方法：改进的min-max标准化
            return self._fallback_normalize(series)
    
    def _fallback_normalize(self, series):
        """备用标准化方法 - 改进的min-max标准化"""
        try:
            # 缺失值处理
            series = series.fillna(series.median())
            
            if series.std() == 0:
                return pd.Series(50, index=series.index)
            
            # 使用分位数边界而非极值，减少异常值影响
            q5, q95 = np.percentile(series, [5, 95])
            clipped_series = series.clip(q5, q95)
            
            # 标准化到[0,100]区间
            if q95 - q5 > 0:
                normalized = (clipped_series - q5) / (q95 - q5) * 100
            else:
                normalized = pd.Series(50, index=series.index)
            
            # 确保结果在合理范围内
            normalized = normalized.clip(0, 100)
            
            if normalized.isna().any():
                normalized = normalized.fillna(50)
            
            return normalized
            
        except Exception as e:
            logger.error(f"备用标准化也失败: {e}")
            return pd.Series(50, index=series.index)

    # 检测市场状态
    def _detect_market_condition(self, df):
        """分析市场状态: 波动性和趋势"""
        condition = {
            'volatility': 'normal',
            'trend': 'neutral'
        }

        try:
            # 基于板块涨跌幅分布计算波动率
            changes = df['change_percent'].dropna()
            if len(changes) < 5:
                return condition

            volatility = changes.std()

            # 波动率状态
            if volatility > 2.5:
                condition['volatility'] = 'high'
            elif volatility < 1.0:
                condition['volatility'] = 'low'

            # 趋势判断 (涨跌家数比)
            up_count = (changes > 0).sum()
            down_count = (changes < 0).sum()

            if len(changes) > 0:
                up_ratio = up_count / len(changes)
                if up_ratio > 0.65:
                    condition['trend'] = 'bull'
                elif up_ratio < 0.35:
                    condition['trend'] = 'bear'

            logger.info(f"市场状态检测: 波动={condition['volatility']}, 趋势={condition['trend']}")

        except Exception as e:
            logger.error(f"市场状态检测异常: {str(e)}")

        return condition

    # 获取动态权重
    def _get_dynamic_weights(self, market_state):
        """根据市场状态动态调整权重"""
        # 优先匹配波动率状态
        if market_state['volatility'] == 'high':
            return self.weight_matrix['high_volatility']
        elif market_state['volatility'] == 'low':
            return self.weight_matrix['low_volatility']

        # 其次匹配趋势状态
        if market_state['trend'] == 'bull':
            return self.weight_matrix['bull_trend']
        elif market_state['trend'] == 'bear':
            return self.weight_matrix['bear_trend']

        return self.weight_matrix['default']

    # 过滤异常点与数据清洗
    def _data_cleaning(self, df):
        """数据清洗与预处理"""
        try:
            # 处理缺失值
            required_cols = ['main_net_ratio', 'super_large_amount', 'change_percent', 'small_ratio']
            for col in required_cols:
                if col in df.columns and df[col].isna().any():
                    df[col] = df[col].fillna(df[col].median())

            # 异常值处理 (3σ原则)
            for col in required_cols:
                if col in df.columns:
                    mean = df[col].mean()
                    std = df[col].std()
                    if std > 0:  # 避免除以零
                        df[col] = df[col].clip(mean - 3*std, mean + 3*std)

            return df
        except Exception as e:
            logger.error(f"数据清洗异常: {str(e)}")
            return df

    @staticmethod
    @jit(nopython=True)
    def _numba_weighted_sum(factors, weights):
        """使用Numba加速的加权求和计算"""
        n = len(factors)
        result = 0.0
        for i in range(n):
            result += factors[i] * weights[i]
        return result

    def calculate_strength(self, df: pd.DataFrame) -> pd.DataFrame:
        """增强版板块强度计算引擎"""
        current_time = datetime.now().time()

        # 确保数据有效
        if df is None or df.empty:
            logger.warning("收到空数据集，返回空DataFrame")
            return pd.DataFrame(columns=['strength_score'])

        # 数据列检查
        required_cols = ['main_net_ratio', 'super_large_amount', 'change_percent', 'small_ratio']
        for col in required_cols:
            if col not in df.columns:
                logger.warning(f"数据中缺少所需列: {col}，返回原始DataFrame")
                df['strength_score'] = 0.0
                return df

        # 数据清洗和预处理
        df = self._data_cleaning(df)

        # 转换主力资金单位 (原有代码保留)
        if 'main_net_amount' in df.columns:
            df['main_net_amount'] = df['main_net_amount'].apply(
                lambda x: round(x / 1e8, 2) if pd.notnull(x) else 0
            )
            df['main_net_str'] = df['main_net_amount'].apply(
                lambda x: f"{x:.2f}亿" if x >= 0 else f"-{abs(x):.2f}亿"
            )

        # 1. 市场状态分析
        market_condition = self._detect_market_condition(df)

        # 2. 多因子计算
        logger.info("开始计算多维度因子...")
        df = self._calculate_capital_factors(df)         # 资金流因子
        df = self._calculate_technical_factors(df)       # 技术因子
        df = self._calculate_fundamental_factors(df)     # 基本面因子
        df = self._calculate_risk_factors(df)            # 风险因子

        # 3. 基础分析因子 (标准化修复版)
        logger.info("计算基础分析因子...")
        df = self._calculate_basic_analysis_factors(df)

        # 4. 动态权重分配 - 修复版：根据因子质量动态调整权重
        logger.info("根据因子质量动态分配权重...")
        dynamic_weights = self._calculate_dynamic_weights(df)
        market_weights = self._get_dynamic_weights(market_condition)
        
        # 结合因子质量权重和市场状态权重
        final_weights = self._combine_weights(dynamic_weights, market_weights)

        # 5. 综合评分计算 - 修复版：使用动态权重
        logger.info("计算综合评分...")
        
        # 调试：输出各因子的统计信息
        logger.info(f"资金流因子统计: 均值={df['capital_score'].mean():.2f}, 标准差={df['capital_score'].std():.2f}, 区分度={df['capital_score'].max()-df['capital_score'].min():.2f}")
        logger.info(f"技术因子统计: 均值={df['technical_score'].mean():.2f}, 标准差={df['technical_score'].std():.2f}, 区分度={df['technical_score'].max()-df['technical_score'].min():.2f}")
        logger.info(f"基本面因子统计: 均值={df['fundamental_score'].mean():.2f}, 标准差={df['fundamental_score'].std():.2f}, 区分度={df['fundamental_score'].max()-df['fundamental_score'].min():.2f}")
        logger.info(f"基础分析因子统计: 均值={df['basic_analysis_score'].mean():.2f}, 标准差={df['basic_analysis_score'].std():.2f}, 区分度={df['basic_analysis_score'].max()-df['basic_analysis_score'].min():.2f}")
        logger.info(f"风险惩罚统计: 均值={df['risk_penalty'].mean():.2f}, 标准差={df['risk_penalty'].std():.2f}, 区分度={df['risk_penalty'].max()-df['risk_penalty'].min():.2f}")
        logger.info(f"动态权重分配: {final_weights}")
        
        df['strength_score'] = (
            final_weights['capital'] * df['capital_score'] +
            final_weights['technical'] * df['technical_score'] +
            final_weights['fundamental'] * df['fundamental_score'] +
            final_weights['basic_analysis'] * df['basic_analysis_score'] +
            final_weights['risk'] * df['risk_penalty']  # 风险惩罚：-50~0范围
        ).round(2)

        # 6. 趋势强化
        df = self._apply_trend_reinforcement(df)

        # 7. 保存历史数据用于未来分析
        self.update_historical_data(df)

        # 结果排序
        result_df = df.sort_values('strength_score', ascending=False)

        # 记录前5名板块信息
        sector_col = 'sector_name' if 'sector_name' in df.columns else 'concept_name'
        if sector_col in df.columns:
            top5 = result_df.head(5)
            logger.info(f"前5名板块强度评分: {[(row[sector_col], row['strength_score']) for _, row in top5.iterrows()]}")

        return result_df

    def _get_time_weights(self, current_time):
        """增强版时间权重"""
        # 辅助函数：判断当前时间是否在范围内
        def time_in_range(start, end, current):
            if isinstance(start, str):
                start = datetime.strptime(start, "%H:%M").time()
            if isinstance(end, str):
                end = datetime.strptime(end, "%H:%M").time()
            return start <= current <= end

        # 开盘集合竞价
        if time_in_range("09:15", "09:30", current_time):
            return self.enhanced_time_weights['open_auction']

        # 开盘早期
        if time_in_range("09:30", "10:00", current_time):
            return self.enhanced_time_weights['early_morning']

        # 上午交易时段
        if time_in_range("10:00", "11:30", current_time):
            return self.enhanced_time_weights['morning']

        # 下午交易前半段
        if time_in_range("13:00", "14:00", current_time):
            return self.enhanced_time_weights['midday']

        # 下午交易后半段
        if time_in_range("14:00", "14:45", current_time):
            return self.enhanced_time_weights['late']

        # 收盘前集合竞价阶段
        if time_in_range("14:45", "15:00", current_time):
            return self.enhanced_time_weights['closing']

        # 默认使用中午权重
        return self.enhanced_time_weights['midday']

    @staticmethod
    def _time_in_range(start, end, current):
        """时间范围判断 (兼容旧代码)"""
        start = datetime.strptime(start, "%H:%M").time()
        end = datetime.strptime(end, "%H:%M").time()
        return start <= current <= end

    # 基础因子计算 - 修复版：简化非线性转换，保持数据差异
    def _calculate_capital_factors(self, df):
        """资金流因子 - 修复版：减少过度复杂的非线性转换"""
        try:
            # 主力净比因子 - 简化为线性分段函数，保持原始差异
            def linear_transform_main_ratio(x):
                if x >= 5:  # 强流入
                    return 80 + min(20, (x - 5) * 2)  # 80-100分
                elif x >= 0:  # 正流入
                    return 50 + x * 6  # 50-80分
                elif x >= -3:  # 轻微流出
                    return 50 + x * 10  # 20-50分
                else:  # 大幅流出
                    return max(0, 20 + (x + 3) * 5)  # 0-20分
            
            df['main_ratio_score'] = df['main_net_ratio'].apply(linear_transform_main_ratio)

            # 超大单因子 - 简化为直接线性映射
            # 标准化超大单金额到0-100分
            df['super_amount_score'] = self._zscore(df['super_large_amount'])

            # 涨跌幅因子 - 简化为分段线性函数
            def linear_transform_change(x):
                if x >= 8:  # 大涨
                    return 90 + min(10, (x - 8) * 2)  # 90-100分
                elif x >= 3:  # 上涨
                    return 70 + (x - 3) * 4  # 70-90分
                elif x >= 0:  # 微涨
                    return 50 + x * 6.67  # 50-70分
                elif x >= -3:  # 微跌
                    return 50 + x * 10  # 20-50分
                else:  # 大跌
                    return max(0, 20 + (x + 3) * 5)  # 0-20分
            
            df['change_score'] = df['change_percent'].apply(linear_transform_change)

            # 散户惩罚因子 - 简化为线性惩罚
            df['retail_penalty'] = np.where(
                df['small_ratio'] > 15,  # 散户占比过高
                -(df['small_ratio'] - 15) * 2,  # 线性惩罚
                0
            ).clip(-30, 0)  # 最大惩罚30分

            # 资金流连续性因子(如果有历史数据)
            sector_col = 'sector_name' if 'sector_name' in df.columns else 'concept_name'
            if sector_col in df.columns and self.historical_data:
                df['flow_consistency'] = df.apply(
                    lambda row: self._calculate_flow_consistency(row[sector_col]),
                    axis=1
                )
            else:
                # 无历史数据时，使用当日数据估算连续性
                df['flow_consistency'] = 50  # 中性值

            # 计算资金流综合评分 - 简化权重计算
            # 确保所有子因子都在0-100范围内
            flow_consistency_normalized = df['flow_consistency']
            if df['flow_consistency'].max() <= 1:  # 如果是0-1范围，转换为0-100
                flow_consistency_normalized = df['flow_consistency'] * 100
            
            df['capital_score'] = (
                0.4 * df['main_ratio_score'] +  # 主力资金权重40%
                0.25 * df['super_amount_score'] +  # 超大单权重25%
                0.2 * df['change_score'] +  # 涨跌幅权重20%
                0.1 * flow_consistency_normalized +  # 连续性权重10%
                0.05 * df['retail_penalty']  # 散户惩罚权重5%
            ).clip(0, 100).round(2)

            return df
        except Exception as e:
            logger.error(f"资金流因子计算异常: {str(e)}")
            df['capital_score'] = 50  # 异常时使用中性值
            return df

    # 技术指标因子 - 优化版：使用短线化技术指标系统
    def _calculate_technical_factors(self, df):
        """技术指标综合计算 - 优化版：集成6日RSI、双动量系统和波动率预测"""
        try:
            sector_col = 'sector_name' if 'sector_name' in df.columns else 'concept_name'
            
            # 更新技术指标优化器的历史数据
            self.technical_optimizer.update_historical_data(self.historical_data)
            
            # 初始化结果列
            df['rsi_score'] = 50.0
            df['momentum_score'] = 50.0
            df['volatility_score'] = 50.0
            df['momentum_signal'] = 'neutral'
            df['predicted_volatility'] = 0.03
            df['risk_level'] = 'medium'
            
            if sector_col in df.columns:
                # 使用优化的技术指标系统计算各项指标
                for idx, row in df.iterrows():
                    sector_name = row[sector_col]
                    if pd.notna(sector_name):
                        try:
                            # 计算综合技术指标
                            tech_result = self.technical_optimizer.calculate_comprehensive_technical_score(sector_name)
                            
                            # 更新DataFrame
                            df.loc[idx, 'rsi_score'] = tech_result['rsi_score']
                            df.loc[idx, 'momentum_score'] = tech_result['momentum_score']
                            df.loc[idx, 'volatility_score'] = tech_result['volatility_score']
                            df.loc[idx, 'momentum_signal'] = tech_result['momentum_signal']
                            df.loc[idx, 'predicted_volatility'] = tech_result['predicted_volatility']
                            df.loc[idx, 'risk_level'] = tech_result['risk_level']
                            
                        except Exception as e:
                            logger.warning(f"板块{sector_name}技术指标计算异常: {e}")
                            # 使用基于板块名称的确定性值
                            from .deterministic_hash import deterministic_hash
                            hash_val = deterministic_hash(str(sector_name), 1000)
                            df.loc[idx, 'rsi_score'] = 50 + (hash_val - 500) * 0.02
                            df.loc[idx, 'momentum_score'] = 50 + ((hash_val + 100) % 1000 - 500) * 0.02
                            df.loc[idx, 'volatility_score'] = 50 + ((hash_val + 200) % 1000 - 500) * 0.02
            
            # 计算技术面综合评分 - 针对短线交易优化权重
            df['technical_score'] = (
                0.35 * df['rsi_score'] +      # RSI权重35%（短线重要）
                0.45 * df['momentum_score'] +  # 动量权重45%（短线最重要）
                0.20 * df['volatility_score']  # 波动率权重20%（风险控制）
            ).round(2)
            
            # 根据动量信号进行微调
            signal_adjustments = {
                'strong_buy': 5,
                'buy': 2,
                'neutral': 0,
                'sell': -2,
                'strong_sell': -5
            }
            
            for signal, adjustment in signal_adjustments.items():
                mask = df['momentum_signal'] == signal
                df.loc[mask, 'technical_score'] += adjustment
            
            # 限制评分范围
            df['technical_score'] = df['technical_score'].clip(0, 100)
            
            logger.info(f"优化技术指标计算完成，平均RSI: {df['rsi_score'].mean():.2f}, 平均动量: {df['momentum_score'].mean():.2f}")
            
            return df
            
        except Exception as e:
            logger.error(f"优化技术指标计算异常: {str(e)}")
            # 异常时使用传统方法作为备用
            return self._calculate_technical_factors_fallback(df)
    
    def _calculate_technical_factors_fallback(self, df):
        """备用技术指标计算方法 - 传统方法"""
        try:
            sector_col = 'sector_name' if 'sector_name' in df.columns else 'concept_name'
            
            logger.warning("使用备用技术指标计算方法")
            
            # 动量评分：基于当日涨跌幅和资金流的综合分析
            df['momentum_score'] = self._calculate_current_day_momentum(df)
            
            # 波动率评分：基于当日数据的相对波动性分析
            df['volatility_score'] = self._calculate_current_day_volatility(df)
            
            # RSI评分：基于当日数据估算
            df['rsi_score'] = self._calculate_current_day_rsi(df)
            
            # 计算技术面综合评分
            df['technical_score'] = (
                0.4 * df['momentum_score'] +
                0.3 * df['volatility_score'] +
                0.3 * df['rsi_score']
            ).round(2)
            
            # 添加默认值
            df['momentum_signal'] = 'neutral'
            df['predicted_volatility'] = 0.03
            df['risk_level'] = 'medium'
            
            return df
            
        except Exception as e:
            logger.error(f"备用技术指标计算也失败: {str(e)}")
            df['technical_score'] = 50
            df['momentum_score'] = 50
            df['volatility_score'] = 50
            df['rsi_score'] = 50
            df['momentum_signal'] = 'neutral'
            df['predicted_volatility'] = 0.03
            df['risk_level'] = 'medium'
            return df
    
    def _calculate_current_day_momentum(self, df):
        """基于当日数据计算动量评分"""
        try:
            # 综合考虑涨跌幅、主力资金流入、成交量等因素
            momentum_factors = []
            
            # 因子1：涨跌幅标准化
            if 'change_percent' in df.columns:
                change_score = self._zscore(df['change_percent'])
                momentum_factors.append(change_score)
            
            # 因子2：主力资金流入强度
            if 'main_net_ratio' in df.columns:
                main_score = self._zscore(df['main_net_ratio'])
                momentum_factors.append(main_score)
            
            # 因子3：超大单资金流入
            if 'super_large_amount' in df.columns:
                super_score = self._zscore(df['super_large_amount'])
                momentum_factors.append(super_score)
            
            # 综合动量评分
            if momentum_factors:
                momentum_score = np.mean(momentum_factors, axis=0)
                # 添加基于板块名称的微小差异化
                sector_col = 'sector_name' if 'sector_name' in df.columns else 'concept_name'
                if sector_col in df.columns:
                    from .deterministic_hash import DeterministicHash
                    hash_func = DeterministicHash()
                    sector_adjustment = df[sector_col].apply(
                        lambda x: hash_func.technical_adjustment(str(x), 'momentum', 5.0)
                    )
                    momentum_score += sector_adjustment
                
                return momentum_score.clip(0, 100)
            else:
                return pd.Series(50, index=df.index)
                
        except Exception as e:
            logger.warning(f"当日动量计算异常: {e}")
            return pd.Series(50, index=df.index)
    
    def _calculate_current_day_volatility(self, df):
        """基于当日数据计算波动率评分"""
        try:
            # 使用涨跌幅的绝对值作为波动率指标
            if 'change_percent' in df.columns:
                abs_change = df['change_percent'].abs()
                # 波动率评分：适中波动率得高分，过高或过低都扣分
                volatility_score = abs_change.apply(lambda x: 
                    100 - abs(x - 3) * 5 if x <= 10 else max(0, 50 - (x - 10) * 3)
                ).clip(0, 100)
                
                # 添加基于板块特性的调整
                sector_col = 'sector_name' if 'sector_name' in df.columns else 'concept_name'
                if sector_col in df.columns:
                    from .deterministic_hash import DeterministicHash
                    hash_func = DeterministicHash()
                    sector_adjustment = df[sector_col].apply(
                        lambda x: hash_func.technical_adjustment(str(x), 'volatility', 4.0)
                    )
                    volatility_score += sector_adjustment
                
                return volatility_score.clip(0, 100)
            else:
                return pd.Series(50, index=df.index)
                
        except Exception as e:
            logger.warning(f"当日波动率计算异常: {e}")
            return pd.Series(50, index=df.index)
    
    def _calculate_current_day_rsi(self, df):
        """基于当日数据估算RSI评分"""
        try:
            if 'change_percent' in df.columns:
                # 将涨跌幅转换为RSI风格的评分
                # 正涨幅对应RSI>50，负涨幅对应RSI<50
                rsi_base = df['change_percent'].apply(lambda x: 
                    50 + min(40, max(-40, x * 3))  # 将±10%涨跌幅映射到10-90的RSI范围
                )
                
                # 结合资金流强度进行调整
                if 'main_net_ratio' in df.columns:
                    flow_adjustment = df['main_net_ratio'].apply(lambda x:
                        min(10, max(-10, x))  # ±10分的资金流调整
                    )
                    rsi_base += flow_adjustment
                
                # 添加板块差异化
                sector_col = 'sector_name' if 'sector_name' in df.columns else 'concept_name'
                if sector_col in df.columns:
                    from .deterministic_hash import DeterministicHash
                    hash_func = DeterministicHash()
                    sector_adjustment = df[sector_col].apply(
                        lambda x: hash_func.technical_adjustment(str(x), 'rsi', 3.0)
                    )
                    rsi_base += sector_adjustment
                
                return rsi_base.clip(0, 100)
            else:
                return pd.Series(50, index=df.index)
                
        except Exception as e:
            logger.warning(f"当日RSI计算异常: {e}")
            return pd.Series(50, index=df.index)

    # 基本面因子 (动态化修复版)
    def _calculate_fundamental_factors(self, df):
        """基本面因子估计 - 修复版：实现动态评分"""
        try:
            sector_col = 'sector_name' if 'sector_name' in df.columns else 'concept_name'
            
            # 行业基础评分矩阵 (基于行业特性和发展前景)
            industry_base_scores = {
                # 科技类 - 高成长性
                '人工智能': 75, 'AI': 75, '芯片': 70, '半导体': 70, '5G': 68, '云计算': 72,
                '新能源车': 78, '光伏': 65, '风电': 62, '储能': 68, '锂电池': 72,
                '软件': 65, '互联网': 62, '游戏': 58, '电商': 60,
                
                # 传统行业 - 稳定性较低
                '银行': 45, '保险': 48, '地产': 35, '房地产': 35, '钢铁': 40,
                '煤炭': 42, '石油': 38, '化工': 44, '建筑': 42,
                
                # 消费类 - 中等稳定性
                '白酒': 68, '医药': 72, '生物医药': 74, '食品': 58, '零售': 52,
                '旅游': 45, '餐饮': 48, '纺织': 40, '家电': 55,
                
                # 周期类 - 中等波动性
                '有色': 50, '建材': 46, '机械': 54, '军工': 65, '航空': 58,
                '汽车': 52, '化肥': 48, '农业': 50
            }
            
            # 政策因子加成 (±20分)
            policy_bonus = {
                '新能源': 15, '人工智能': 18, 'AI': 18, '芯片': 20, '半导体': 20,
                '军工': 12, '医药': 8, '生物医药': 10, '环保': 10, 
                '数字经济': 15, '新基建': 12, '碳中和': 8, '乡村振兴': 6
            }
            
            # 计算基本面评分函数
            def calculate_fundamental_score(sector_name):
                if pd.isna(sector_name) or sector_name == '':
                    return 50
                    
                base_score = 50  # 默认中性分
                
                # 匹配行业基础分 (优先精确匹配，然后模糊匹配)
                sector_name_str = str(sector_name)
                
                # 精确匹配
                if sector_name_str in industry_base_scores:
                    base_score = industry_base_scores[sector_name_str]
                else:
                    # 模糊匹配
                    for industry, score in industry_base_scores.items():
                        if industry in sector_name_str:
                            base_score = score
                            break
                
                # 政策加成计算
                policy_add = 0
                for policy, bonus in policy_bonus.items():
                    if policy in sector_name_str:
                        policy_add += bonus
                
                # 最终评分 (限制在0-100)
                final_score = min(100, max(0, base_score + policy_add))
                return final_score
            
            # 应用基本面评分计算
            if sector_col in df.columns:
                df['fundamental_score'] = df[sector_col].apply(calculate_fundamental_score)
            else:
                df['fundamental_score'] = 50
            
            # 添加基于板块名称的确定性微小扰动，避免完全相同的评分
            if sector_col in df.columns:
                from .deterministic_hash import DeterministicHash
                hash_func = DeterministicHash()
                df['fundamental_score'] = df.apply(
                    lambda row: row['fundamental_score'] + 
                    hash_func.sector_adjustment(str(row[sector_col]), 2.0),
                    axis=1
                )
            
            # 确保评分在合理范围内
            df['fundamental_score'] = df['fundamental_score'].clip(0, 100).round(2)
            
            # 使用缓存的政策因子进行微调 (如果存在)
            if sector_col in df.columns and 'policy_factor' in self.factor_memory:
                policy_adjustment = df[sector_col].map(
                    lambda x: self.factor_memory.get('policy_factor', {}).get(x, 0)
                )
                # 政策因子作为微调，影响±5分
                df['fundamental_score'] += policy_adjustment * 0.1
                df['fundamental_score'] = df['fundamental_score'].clip(0, 100).round(2)
            
            return df
        except Exception as e:
            logger.error(f"基本面因子计算异常: {str(e)}")
            df['fundamental_score'] = 50  # 异常时使用中性值
            return df

    # 基础分析因子 (标准化修复版)
    def _calculate_basic_analysis_factors(self, df):
        """基础分析因子 - 修复版：实现标准化评分"""
        try:
            sector_col = 'sector_name' if 'sector_name' in df.columns else 'concept_name'
            
            # 基础分析评分矩阵 (基于估值水平、盈利能力、成长性)
            basic_analysis_scores = {
                # 高估值但高成长 (科技类)
                '人工智能': 72, 'AI': 72, '芯片': 68, '半导体': 68, '5G': 65, 
                '云计算': 70, '软件': 62, '互联网': 58, '游戏': 55,
                
                # 新兴产业 (估值合理，成长性强)
                '新能源车': 75, '光伏': 62, '风电': 58, '储能': 65, '锂电池': 68,
                '生物医药': 70, '医疗器械': 68, '创新药': 72,
                
                # 传统价值股 (低估值，稳定盈利)
                '银行': 65, '保险': 62, '白酒': 78, '食品': 60, '家电': 58,
                '建筑': 45, '钢铁': 42, '煤炭': 48, '石油': 40,
                
                # 周期性行业 (估值波动大)
                '有色': 52, '化工': 48, '建材': 46, '机械': 55, '汽车': 50,
                '房地产': 35, '地产': 35, '航空': 45, '旅游': 42,
                
                # 防御性行业 (估值稳定)
                '医药': 68, '公用事业': 55, '电力': 52, '水务': 50, '燃气': 48,
                '农业': 48, '零售': 50, '纺织': 38
            }
            
            # 市场情绪调整因子 (基于当前市场环境)
            market_sentiment_adjustment = {
                # 热门概念 (+10分)
                '人工智能': 10, 'AI': 10, 'ChatGPT': 12, '大模型': 10,
                '新能源': 8, '军工': 6, '半导体': 8, '芯片': 8,
                
                # 政策支持 (+5分)
                '乡村振兴': 5, '数字经济': 6, '新基建': 5, '碳中和': 4,
                '国企改革': 3, '一带一路': 3,
                
                # 市场冷门 (-5分)
                '房地产': -8, '地产': -8, '教育': -5, '游戏': -3,
                '旅游': -4, '餐饮': -3, '纺织': -5
            }
            
            # 计算基础分析评分函数
            def calculate_basic_analysis_score(sector_name):
                if pd.isna(sector_name) or sector_name == '':
                    return 50
                    
                base_score = 50  # 默认中性分
                sector_name_str = str(sector_name)
                
                # 匹配基础分析评分 (优先精确匹配)
                if sector_name_str in basic_analysis_scores:
                    base_score = basic_analysis_scores[sector_name_str]
                else:
                    # 模糊匹配
                    for sector, score in basic_analysis_scores.items():
                        if sector in sector_name_str:
                            base_score = score
                            break
                
                # 市场情绪调整
                sentiment_adj = 0
                for concept, adjustment in market_sentiment_adjustment.items():
                    if concept in sector_name_str:
                        sentiment_adj += adjustment
                
                # 最终评分 (限制在0-100)
                final_score = min(100, max(0, base_score + sentiment_adj))
                return final_score
            
            # 应用基础分析评分计算
            if sector_col in df.columns:
                df['basic_analysis_score'] = df[sector_col].apply(calculate_basic_analysis_score)
            else:
                df['basic_analysis_score'] = 50
            
            # 基于技术指标的微调 (如果存在RSI等技术指标)
            if 'rsi_score' in df.columns:
                # RSI超买超卖调整：RSI>70减分，RSI<30加分
                rsi_adjustment = df['rsi_score'].apply(
                    lambda x: -5 if x > 70 else (5 if x < 30 else 0) if pd.notna(x) else 0
                )
                df['basic_analysis_score'] += rsi_adjustment
            
            # 基于资金流的微调 (如果存在资金流指标)
            if 'capital_score' in df.columns:
                # 资金流强度调整：强流入加分，强流出减分
                flow_adjustment = (df['capital_score'] - 50) * 0.1  # ±5分调整
                df['basic_analysis_score'] += flow_adjustment
            
            # 添加基于板块名称的确定性微小扰动
            if sector_col in df.columns:
                from .deterministic_hash import DeterministicHash
                hash_func = DeterministicHash()
                df['basic_analysis_score'] = df.apply(
                    lambda row: row['basic_analysis_score'] + 
                    hash_func.sector_adjustment(str(row[sector_col]), 1.5),
                    axis=1
                )
            
            # 确保评分在合理范围内
            df['basic_analysis_score'] = df['basic_analysis_score'].clip(0, 100).round(2)
            
            return df
        except Exception as e:
            logger.error(f"基础分析因子计算异常: {str(e)}")
            df['basic_analysis_score'] = 50
            return df

    # 风险因子 (扩展范围修复版)
    def _calculate_risk_factors(self, df):
        """风险因子评估 - 修复版：扩展惩罚范围到-50~0"""
        try:
            # 初始化风险惩罚为0
            df['risk_penalty'] = 0
            
            # 波动率风险惩罚 (0到-20分)
            if 'volatility_score' in df.columns:
                # 波动率越高，惩罚越重
                # 将0-100的波动率分数转换为0到-20的惩罚
                volatility_penalty = -(df['volatility_score'] / 100) * 20
                df['risk_penalty'] += volatility_penalty
            
            # 流动性风险惩罚 (0到-15分)
            if 'turnover' in df.columns:
                # 成交额越低，流动性风险越高
                mean_turnover = df['turnover'].mean()
                std_turnover = df['turnover'].std()
                
                # 标准化成交额，然后转换为惩罚
                normalized_turnover = (df['turnover'] - mean_turnover) / (std_turnover + 1e-8)
                # 成交额低于平均值的给予惩罚
                liquidity_penalty = np.where(
                    normalized_turnover < -1,  # 低于平均值1个标准差
                    -15,  # 最大惩罚15分
                    np.where(
                        normalized_turnover < 0,  # 低于平均值
                        normalized_turnover * 7.5,  # 线性惩罚，最多7.5分
                        0  # 高于平均值不惩罚
                    )
                )
                df['risk_penalty'] += liquidity_penalty
            
            # 极端涨跌风险惩罚 (0到-10分)
            if 'change_percent' in df.columns:
                # 极端涨跌都视为风险
                extreme_change_penalty = np.where(
                    np.abs(df['change_percent']) > 10,  # 涨跌幅超过10%
                    -10,  # 最大惩罚10分
                    np.where(
                        np.abs(df['change_percent']) > 5,  # 涨跌幅超过5%
                        -(np.abs(df['change_percent']) - 5),  # 线性惩罚
                        0  # 正常涨跌幅不惩罚
                    )
                )
                df['risk_penalty'] += extreme_change_penalty
            
            # 技术指标风险惩罚 (0到-8分)
            if 'rsi_score' in df.columns:
                # RSI极值风险：过度超买或超卖都是风险
                rsi_risk_penalty = np.where(
                    (df['rsi_score'] > 80) | (df['rsi_score'] < 20),  # RSI极值
                    -8,  # 最大惩罚8分
                    np.where(
                        (df['rsi_score'] > 70) | (df['rsi_score'] < 30),  # RSI偏极值
                        -4,  # 中等惩罚4分
                        0  # 正常RSI不惩罚
                    )
                )
                df['risk_penalty'] += rsi_risk_penalty
            
            # 历史回撤风险惩罚 (0到-12分)
            sector_col = 'sector_name' if 'sector_name' in df.columns else 'concept_name'
            if sector_col in df.columns and self.historical_data:
                df['drawdown_penalty'] = df.apply(
                    lambda row: self._calculate_drawdown_penalty(row[sector_col]),
                    axis=1
                )
                df['risk_penalty'] += df['drawdown_penalty']
            
            # 市场情绪风险惩罚 (0到-5分)
            if sector_col in df.columns:
                # 基于板块特性的风险评估
                high_risk_sectors = {
                    '房地产': -5, '地产': -5, '煤炭': -4, '钢铁': -4,
                    '有色': -3, '化工': -3, '建材': -3, '石油': -4,
                    '航空': -3, '旅游': -3, '餐饮': -2, '纺织': -2
                }
                
                sector_risk_penalty = df[sector_col].map(
                    lambda x: next((penalty for sector, penalty in high_risk_sectors.items() 
                                  if sector in str(x)), 0)
                )
                df['risk_penalty'] += sector_risk_penalty
            
            # 添加基于板块名称的确定性微小扰动
            if sector_col in df.columns:
                from .deterministic_hash import DeterministicHash
                hash_func = DeterministicHash()
                df['risk_penalty'] = df.apply(
                    lambda row: row['risk_penalty'] + 
                    hash_func.risk_adjustment(str(row[sector_col]), 1.0),
                    axis=1
                )
            
            # 限制风险惩罚范围到-50~0，确保都是负值
            df['risk_penalty'] = df['risk_penalty'].clip(-50, 0).round(2)
            
            return df
        except Exception as e:
            logger.error(f"风险因子计算异常: {str(e)}")
            df['risk_penalty'] = 0  # 异常时不施加惩罚
            return df
    
    def _calculate_drawdown_penalty(self, sector_name):
        """计算回撤惩罚分数"""
        try:
            if sector_name not in self.historical_data:
                return 0
            
            history = self.historical_data[sector_name]
            if len(history) < 5:
                return 0
            
            # 计算最大回撤
            prices = np.array(history[-20:])  # 最近20个数据点
            peak = np.maximum.accumulate(prices)
            drawdown = (prices - peak) / peak
            max_drawdown = np.min(drawdown)
            
            # 将回撤转换为惩罚分数 (0到-12分)
            if max_drawdown < -0.3:  # 回撤超过30%
                return -12
            elif max_drawdown < -0.2:  # 回撤超过20%
                return -8
            elif max_drawdown < -0.1:  # 回撤超过10%
                return -4
            else:
                return 0
        except Exception as e:
            logger.error(f"计算回撤惩罚异常: {e}")
            return 0
    
    def _calculate_dynamic_weights(self, df):
        """根据因子质量动态计算权重 - 核心优化函数"""
        try:
            factor_scores = {
                'capital': df['capital_score'],
                'technical': df['technical_score'], 
                'fundamental': df['fundamental_score'],
                'basic_analysis': df['basic_analysis_score'],
                'risk': df['risk_penalty']
            }
            
            # 计算每个因子的区分度（标准差）
            factor_discrimination = {}
            for name, scores in factor_scores.items():
                std_dev = scores.std()
                # 区分度评分：标准差越大，区分度越高
                factor_discrimination[name] = std_dev
            
            # 计算每个因子的有效性（非中性值比例）
            factor_effectiveness = {}
            for name, scores in factor_scores.items():
                # 计算偏离中性值的程度
                if name == 'risk':  # 风险因子中性值为-25
                    deviation_from_neutral = abs(scores - (-25)).mean()
                else:  # 其他因子中性值为50
                    deviation_from_neutral = abs(scores - 50).mean()
                factor_effectiveness[name] = deviation_from_neutral
            
            # 综合质量评分（区分度 + 有效性）
            factor_quality = {}
            for name in factor_scores.keys():
                # 标准化区分度和有效性到相同量级
                norm_discrimination = factor_discrimination[name] / max(factor_discrimination.values()) if max(factor_discrimination.values()) > 0 else 0
                norm_effectiveness = factor_effectiveness[name] / max(factor_effectiveness.values()) if max(factor_effectiveness.values()) > 0 else 0
                
                quality = (
                    0.6 * norm_discrimination +  # 区分度权重60%
                    0.4 * norm_effectiveness     # 有效性权重40%
                )
                factor_quality[name] = quality
            
            # 基础权重（保证最小权重）
            base_weights = {
                'capital': 0.15,      # 资金流最小15%
                'technical': 0.10,    # 技术面最小10%
                'fundamental': 0.15,  # 基本面最小15%
                'basic_analysis': 0.15, # 基础分析最小15%
                'risk': 0.10         # 风险因子最小10%
            }
            
            # 可分配权重（总权重1.0 - 基础权重之和）
            remaining_weight = 1.0 - sum(base_weights.values())
            
            # 根据质量评分分配剩余权重
            total_quality = sum(factor_quality.values())
            if total_quality > 0:
                dynamic_weights = {}
                for name in factor_scores.keys():
                    quality_ratio = factor_quality[name] / total_quality
                    additional_weight = remaining_weight * quality_ratio
                    dynamic_weights[name] = base_weights[name] + additional_weight
            else:
                # 如果质量评分计算失败，使用固定权重
                dynamic_weights = {
                    'capital': 0.25,
                    'technical': 0.20,
                    'fundamental': 0.20,
                    'basic_analysis': 0.20,
                    'risk': 0.15
                }
            
            # 确保权重和为1.0
            total_weight = sum(dynamic_weights.values())
            if total_weight > 0:
                dynamic_weights = {k: v/total_weight for k, v in dynamic_weights.items()}
            
            # 记录因子质量信息
            logger.info(f"因子质量评分: {factor_quality}")
            logger.info(f"因子区分度: {factor_discrimination}")
            logger.info(f"因子有效性: {factor_effectiveness}")
            
            return dynamic_weights
            
        except Exception as e:
            logger.warning(f"动态权重计算异常: {e}，使用固定权重")
            # 异常时使用固定权重
            return {
                'capital': 0.25,
                'technical': 0.20,
                'fundamental': 0.20,
                'basic_analysis': 0.20,
                'risk': 0.15
            }
    
    def _combine_weights(self, dynamic_weights, market_weights):
        """结合因子质量权重和市场状态权重"""
        try:
            # 因子质量权重占70%，市场状态权重占30%
            combined_weights = {}
            for key in dynamic_weights.keys():
                if key in market_weights:
                    combined_weights[key] = (
                        0.7 * dynamic_weights[key] + 
                        0.3 * market_weights[key]
                    )
                else:
                    combined_weights[key] = dynamic_weights[key]
            
            # 确保权重和为1.0
            total_weight = sum(combined_weights.values())
            if total_weight > 0:
                combined_weights = {k: v/total_weight for k, v in combined_weights.items()}
            
            return combined_weights
            
        except Exception as e:
            logger.warning(f"权重结合异常: {e}，使用动态权重")
            return dynamic_weights

    # 基础分析模块已加载

            if os.path.exists(last_refresh_file):
                try:
                    with open(last_refresh_file, 'r') as f:
                        last_refresh = f.read().strip()
                    if last_refresh != today_str:
                        force_refresh = True
                except:
                    force_refresh = True
            else:
                force_refresh = True

            if force_refresh:
                logger.info("今日首次运行，强制刷新板块分析缓存")
                try:
                    with open(last_refresh_file, 'w') as f:
                        f.write(today_str)
                except Exception as e:
                    logger.warning(f"更新刷新标记失败: {e}")

            if not force_refresh and os.path.exists(cache_file):
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)

                    # 检查缓存是否过期（使用动态缓存时间）
                    timestamp = datetime.fromisoformat(cache_data.get('timestamp', '2000-01-01T00:00:00'))
                    if (now - timestamp).total_seconds() < cache_max_age:
                        cached_sectors = cache_data.get('sectors', {})
                        logger.info(f"从缓存加载了{len(cached_sectors)}个板块分析结果，缓存有效期: {cache_max_age//3600}小时")
                    else:
                        logger.info(f"缓存已过期 ({(now - timestamp).total_seconds()//60:.1f}分钟前)，将重新分析")
                except Exception as e:
                    logger.warning(f"加载板块分析缓存失败: {e}")

            # 过滤出需要分析的板块（排除已缓存的）
            sectors_to_analyze = []
            for sector in top_sectors:
                if sector not in cached_sectors:
                    sectors_to_analyze.append(sector)
                else:
                    # 直接使用缓存结果
                    sector_results[sector] = cached_sectors[sector]
                    logger.info(f"使用缓存的板块分析结果: {sector}")

            # 如果所有板块都有缓存，直接返回
            if not sectors_to_analyze:
                logger.info("所有板块都使用缓存结果，无需重新分析")
            else:
                # 分批处理需要分析的板块
                for i in range(0, len(sectors_to_analyze), batch_size):
                    batch_sectors = sectors_to_analyze[i:i+batch_size]
                    logger.info(f"处理第{i//batch_size + 1}批板块，包含{len(batch_sectors)}个板块")

                    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                        # 提交任务
                        future_to_sector = {executor.submit(_analyze_sector_info, sector): sector for sector in batch_sectors}

                        # 收集结果
                        for future in concurrent.futures.as_completed(future_to_sector):
                            try:
                                sector = future_to_sector[future]
                                result = future.result()
                                if result:
                                    sector_name = result['sector_name']
                                    sector_results[sector_name] = result
                                    # 同时更新缓存
                                    cached_sectors[sector_name] = result
                                    logger.info(f"完成板块分析: {sector_name}")
                            except Exception as e:
                                logger.error(f"获取板块分析结果时出错: {e}")

                # 保存更新后的缓存
                try:
                    with open(cache_file, 'w', encoding='utf-8') as f:
                        json.dump({
                            'timestamp': datetime.now().isoformat(),
                            'sectors': cached_sectors
                        }, f, ensure_ascii=False, indent=2)
                    logger.info(f"已保存{len(cached_sectors)}个板块分析结果到缓存")
                except Exception as e:
                    logger.warning(f"保存板块分析缓存失败: {e}")

            # 将结果应用到数据框
            for sector, result in sector_results.items():
                # 找出属于该板块的股票
                sector_mask = df[sector_column] == sector

                # 基础分析模式

            # 记录总耗时和各因子的平均值
            total_time = time.time() - total_start_time

            market_weights_info = "基础分析模式"

            logger.info(f"板块分析完成 - 处理了{len(sector_results)}个板块 | 总耗时: {total_time:.2f}秒 | {market_weights_info}")

            # 记录缓存使用情况
            cached_count = len(sector_results) - len(sectors_to_analyze)
            if cached_count > 0:
                cache_ratio = cached_count / len(sector_results) * 100
                if cache_ratio > 80:
                    logger.warning(f"缓存使用率过高: {cache_ratio:.1f}%，可能影响实时性。考虑减少缓存依赖或缩短缓存有效期。")
                else:
                    logger.info(f"缓存使用情况: {cached_count}个板块使用缓存 ({cache_ratio:.1f}%)，{len(sectors_to_analyze)}个板块实时分析")

            return df

        except Exception as e:
            # 捕获并记录详细的异常信息，包括堆栈跟踪
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"基础分析失败: {e}")
            logger.error(f"异常详情: {error_details}")

            # 记录当前处理状态和中间结果
            logger.warning(f"基础分析失败时的状态: 已处理 {len(sector_results)} 个板块，市场状态: {market_state}")

            # 发生错误时返回原数据框
            return df

    def _apply_trend_reinforcement(self, df):
        """趋势强化与稳定性调整"""
        try:
            sector_col = 'sector_name' if 'sector_name' in df.columns else 'concept_name'

            # 默认无趋势增强
            df['trend_boost'] = 1.0

            # 需要有板块名和历史数据才能计算趋势
            if sector_col in df.columns and self.historical_data:
                # 计算每个板块的趋势
                for idx, row in df.iterrows():
                    sector_name = row[sector_col]
                    if sector_name in self.historical_data:
                        history = self.historical_data[sector_name]

                        # 如果有足够历史数据
                        if len(history) >= 5:
                            # 简单线性拟合
                            y = history[-5:]
                            x = np.arange(len(y))

                            try:
                                slope, _, _, _, _ = stats.linregress(x, y)

                                # 根据斜率调整趋势因子
                                if slope > 0.1:  # 明显上升
                                    df.loc[idx, 'trend_boost'] = 1.15
                                elif slope < -0.1:  # 明显下降
                                    df.loc[idx, 'trend_boost'] = 0.9
                                elif slope > 0.05:  # 小幅上升
                                    df.loc[idx, 'trend_boost'] = 1.08
                                elif slope < -0.05:  # 小幅下降
                                    df.loc[idx, 'trend_boost'] = 0.95
                            except:
                                pass

            # 应用趋势增强
            df['strength_score'] = (df['strength_score'] * df['trend_boost']).round(2)

        except Exception as e:
            logger.error(f"趋势强化异常: {str(e)}")

        return df

    # 历史动量计算
    def _calculate_momentum(self, sector_name):
        """计算板块动量指标"""
        try:
            if sector_name in self.historical_data:
                history = self.historical_data[sector_name]

                if len(history) >= 5:
                    # 计算3日和5日动量
                    momentum_3d = history[-1] / history[-3] - 1 if history[-3] != 0 else 0
                    momentum_5d = history[-1] / history[-5] - 1 if history[-5] != 0 else 0

                    # 综合动量评分 (0-1)
                    score = 0.7 * (0.5 + momentum_3d) + 0.3 * (0.5 + momentum_5d)
                    return min(max(score, 0), 1)

            return 0.5  # 默认中性
        except:
            return 0.5

    # 波动率计算
    def _calculate_volatility(self, sector_name):
        """计算板块波动率 (保留原方法用于风险计算)"""
        try:
            if sector_name in self.historical_data:
                history = self.historical_data[sector_name]

                if len(history) >= 5:
                    # 计算标准差作为波动率
                    volatility = np.std(history[-5:]) / np.mean(history[-5:]) if np.mean(history[-5:]) != 0 else 0

                    # 标准化到0-1区间
                    return min(volatility / 0.1, 1)  # 10%波动率作为上限

            return 0.5  # 默认中等波动
        except:
            return 0.5

    def _calculate_volatility_stability_score(self, sector_name):
        """计算波动率稳定性评分 (0-100分) - 修复版"""
        try:
            if sector_name in self.historical_data:
                history = self.historical_data[sector_name]
                if len(history) >= 5:
                    # 计算变异系数作为波动率
                    mean_val = np.mean(history[-5:])
                    if mean_val != 0:
                        volatility = np.std(history[-5:]) / mean_val
                        # 波动率越低，稳定性越高，技术分数越高
                        # 使用反比关系，并标准化到0-100
                        stability_score = max(0, 100 * (1 - min(volatility / 0.15, 1)))
                        return stability_score
                elif len(history) >= 3:
                    # 数据不足时使用3日数据
                    mean_val = np.mean(history[-3:])
                    if mean_val != 0:
                        volatility = np.std(history[-3:]) / mean_val
                        stability_score = max(0, 100 * (1 - min(volatility / 0.2, 1)))
                        return stability_score
            return 50  # 默认中等稳定性
        except Exception as e:
            logger.warning(f"波动率稳定性计算异常 {sector_name}: {e}")
            return 50

    def _calculate_individual_rsi(self, sector_name, period=14):
        """
        为单个板块计算RSI指标 - 增强修复版
        
        Parameters:
        -----------
        sector_name : str
            板块名称
        period : int
            RSI计算周期，默认14
            
        Returns:
        --------
        float
            RSI值，范围0-1 (已标准化)
        """
        try:
            # 检查历史数据
            if sector_name not in self.historical_data:
                # 基于板块名称生成确定性的中性值变化
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(sector_name, 1000)
                return 0.5 + (hash_val - 500) * 0.0001  # 在0.45-0.55之间
            
            history = self.historical_data[sector_name]
            if len(history) < 3:  # 至少需要3个数据点
                # 基于板块名称生成确定性的中性值变化
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(sector_name, 1000)
                return 0.5 + (hash_val - 500) * 0.0001
            
            # 使用固定的最小周期，避免不同板块使用不同周期导致相同结果
            effective_period = min(period, len(history) - 1)
            if effective_period < 3:
                effective_period = len(history) - 1
            
            # 计算价格变化序列
            price_changes = np.diff(history[-effective_period-1:])
            
            # 分离上涨和下跌
            gains = np.where(price_changes > 0, price_changes, 0)
            losses = np.where(price_changes < 0, -price_changes, 0)
            
            # 计算平均收益和损失 - 使用更精确的计算
            avg_gain = np.mean(gains) if np.sum(gains) > 0 else 0
            avg_loss = np.mean(losses) if np.sum(losses) > 0 else 0
            
            # 处理无变化情况
            if avg_gain == 0 and avg_loss == 0:
                # 基于板块名称和历史数据生成确定性的微小差异
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_{len(history)}", 1000)
                return 0.5 + (hash_val - 500) * 0.0001  # 在0.45-0.55之间
            
            # 处理边界情况
            if avg_loss == 0:
                # 全部上涨，但根据涨幅大小区分
                gain_magnitude = np.sum(gains)
                base_rsi = 1.0
                # 根据板块名称添加微小差异
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_gain", 100)
                return min(1.0, base_rsi - hash_val * 0.001)
            
            if avg_gain == 0:
                # 全部下跌，但根据跌幅大小区分
                loss_magnitude = np.sum(losses)
                base_rsi = 0.0
                # 根据板块名称添加微小差异
                from .deterministic_hash import deterministic_hash
                hash_val = deterministic_hash(f"{sector_name}_loss", 100)
                return max(0.0, base_rsi + hash_val * 0.001)
            
            # 计算RSI
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            # 添加基于板块名称的微小扰动，确保不同板块有不同结果
            from .deterministic_hash import deterministic_hash
            hash_val = deterministic_hash(f"{sector_name}_rsi_adj", 100)
            rsi_adjustment = (hash_val - 50) * 0.01  # ±0.5的调整
            rsi = max(0, min(100, rsi + rsi_adjustment))
            
            # 标准化到0-1范围
            return max(0, min(1, rsi / 100))
            
        except Exception as e:
            logger.warning(f"RSI计算异常 {sector_name}: {e}")
            # 异常时也基于板块名称返回不同值
            from .deterministic_hash import deterministic_hash
            hash_val = deterministic_hash(f"{sector_name}_error", 1000)
            return 0.5 + (hash_val - 500) * 0.0001

    # 最大回撤计算
    def _calculate_max_drawdown(self, sector_name):
        """计算板块最大回撤"""
        try:
            if sector_name in self.historical_data:
                history = self.historical_data[sector_name]

                if len(history) >= 10:
                    # 计算最大回撤
                    cummax = np.maximum.accumulate(history[-10:])
                    drawdown = 1 - np.array(history[-10:]) / cummax
                    max_dd = np.max(drawdown)

                    # 标准化到0-1区间
                    return min(max_dd / 0.2, 1)  # 20%回撤作为上限

            return 0.3  # 默认轻微回撤
        except:
            return 0.3

    # 资金流连续性计算
    def _calculate_flow_consistency(self, sector_name):
        """计算资金流向一致性"""
        try:
            if 'fund_flow' in self.historical_data and sector_name in self.historical_data['fund_flow']:
                flows = self.historical_data['fund_flow'][sector_name]

                if len(flows) >= 3:
                    # 计算最近3天资金流向一致性
                    same_direction = sum(1 for i in range(len(flows)-1) if np.sign(flows[i]) == np.sign(flows[-1]))
                    consistency = same_direction / (len(flows)-1)

                    # 标准化评分 (0.3-1)
                    return 0.3 + 0.7 * consistency

            return 0.5  # 默认中性
        except:
            return 0.5

    # 更新历史数据
    def update_historical_data(self, df):
        """更新板块历史数据"""
        try:
            # 确定板块名称列和得分列
            sector_column = 'sector_name' if 'sector_name' in df.columns else 'concept_name'

            if sector_column not in df.columns or 'strength_score' not in df.columns:
                return

            # 更新强度得分历史
            for _, row in df.iterrows():
                sector_name = row[sector_column]
                score = row['strength_score']

                if sector_name not in self.historical_data:
                    self.historical_data[sector_name] = []

                # 保持最近20天的数据
                history = self.historical_data[sector_name]
                history.append(score)
                if len(history) > 20:
                    history = history[-20:]
                self.historical_data[sector_name] = history

            # 更新资金流历史 (如果有)
            if 'main_net_ratio' in df.columns:
                if 'fund_flow' not in self.historical_data:
                    self.historical_data['fund_flow'] = {}

                for _, row in df.iterrows():
                    sector_name = row[sector_column]
                    flow = row['main_net_ratio']

                    if sector_name not in self.historical_data['fund_flow']:
                        self.historical_data['fund_flow'][sector_name] = []

                    # 保持最近5天的数据
                    flows = self.historical_data['fund_flow'][sector_name]
                    flows.append(flow)
                    if len(flows) > 5:
                        flows = flows[-5:]
                    self.historical_data['fund_flow'][sector_name] = flows

            logger.info(f"历史数据更新完成, 已记录 {len(self.historical_data)} 个板块")

        except Exception as e:
            logger.error(f"更新历史数据异常: {str(e)}")

    # 基础分析模式


class StockSelector:
    """选股执行器"""

    def __init__(self, use_basic_analysis=True):
        self.sector_analyzer = None
        self.concept_analyzer = None
        self.strength_calculator = None
        self.use_basic_analysis = use_basic_analysis
        self._initialized = False
        self.backtest_results = {}

    def _ensure_initialized(self):
        """确保模块已初始化"""
        if not self._initialized:
            self._initialize_components()

    def _initialize_components(self):
        """延迟初始化组件"""
        if self._initialized:
            return

        logger.info("初始化选股模块组件...")
        from fundamental_analysis.sector_rank import SectorRankAnalyzer
        from fundamental_analysis.concept_rank import ConceptRankAnalyzer

        self.sector_analyzer = SectorRankAnalyzer()
        self.concept_analyzer = ConceptRankAnalyzer()

        # 使用基础分析模式
        self.use_basic_analysis = True
        self.strength_calculator = SectorStrengthCalculator(use_basic_analysis=True)
        logger.info("板块强度计算器初始化成功（基础分析模式）")

        self._initialized = True
        logger.info("选股模块组件初始化完成")

    def get_top_sectors(self, sector_type: str = 'industry', top_n: int = 10) -> pd.DataFrame:
        """
        获取顶级板块
        :param sector_type: 板块类型 industry/concept
        :param top_n: 前N名
        """
        self._ensure_initialized()

        try:
            if sector_type == 'industry':
                raw_df = self.sector_analyzer.get_sector_rank()
            else:
                raw_df = self.concept_analyzer.get_concept_rank()

            # 确保强度计算器已正确初始化
            if self.strength_calculator is None:
                logger.warning("强度计算器未初始化，重新初始化中...")
                self.strength_calculator = SectorStrengthCalculator(use_basic_analysis=True)

            scored_df = self.strength_calculator.calculate_strength(raw_df)
            return scored_df.head(top_n)

        except Exception as e:
            logger.error(f"获取板块数据失败: {str(e)}")
            # 出现错误时返回空DataFrame而不是抛出异常
            if sector_type == 'industry':
                return pd.DataFrame(columns=['rank', 'sector_name', 'change_percent', 'strength_score'])
            else:
                return pd.DataFrame(columns=['rank', 'concept_name', 'change_percent', 'strength_score'])

    def get_constituents(self, sector_name: str, sector_type: str) -> pd.DataFrame:
        """
        获取板块成分股
        :param sector_name: 板块名称
        :param sector_type: 板块类型 industry/concept
        """
        self._ensure_initialized()

        try:
            if sector_type == 'industry':
                return ak.stock_board_industry_cons_em(symbol=sector_name)
            else:
                return ak.stock_board_concept_cons_em(symbol=sector_name)
        except Exception as e:
            logger.warning(f"获取{sector_name}成分股失败: {str(e)}")
            return pd.DataFrame()

    def parallel_fetch_constituents(self, top_sectors: pd.DataFrame, sector_type: str) -> dict:
        """修复版并行获取成分股"""
        self._ensure_initialized()

        results = {}
        # 动态确定名称列
        name_column = 'sector_name' if sector_type == 'industry' else 'concept_name'

        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            future_to_sector = {
                executor.submit(self.get_constituents, row[name_column], sector_type): row[name_column]
                for _, row in top_sectors.iterrows()
            }

            for future in concurrent.futures.as_completed(future_to_sector):
                sector_name = future_to_sector[future]
                try:
                    data = future.result()
                    if not data.empty:
                        results[sector_name] = data
                        logger.info(f"成功获取 {sector_name} 成分股 ({len(data)} 只)")
                except Exception as e:
                    logger.error(f"处理 {sector_name} 时发生错误: {str(e)}")

        return results

    def validate_scoring_model(self, days=5, top_n=10):
        """验证板块评分模型有效性

        Args:
            days (int): 回测天数
            top_n (int): 每天选取的前N名板块

        Returns:
            dict: 验证结果统计
        """
        self._ensure_initialized()

        logger.info(f"开始验证评分模型有效性，回测 {days} 天...")

        try:
            # 初始化结果存储
            self.backtest_results = {
                'industry': {
                    'next_day_hit_rate': [],
                    'avg_next_day_return': [],
                    'top_sectors': {}
                },
                'concept': {
                    'next_day_hit_rate': [],
                    'avg_next_day_return': [],
                    'top_sectors': {}
                }
            }

            # 获取当前日期作为起始日
            current_date = datetime.now().date()

            # 初始化缓存
            selection_cache = {'industry': {}, 'concept': {}}

            # 模拟连续多天运行选股，并验证结果
            for d in range(days):
                # 减去d天，模拟历史日期
                simulation_date = current_date - pd.Timedelta(days=d)
                logger.info(f"模拟回测日期: {simulation_date}")

                for sector_type in ['industry', 'concept']:
                    # 获取当日评分最高的前N个板块
                    top_sectors = self.get_top_sectors(sector_type, top_n)

                    # 如果没有数据则跳过
                    if top_sectors.empty:
                        continue

                    # 提取板块名称列
                    name_column = 'sector_name' if sector_type == 'industry' else 'concept_name'

                    # 将排名信息存入缓存
                    today_selections = {}
                    for _, row in top_sectors.iterrows():
                        sector_name = row[name_column]
                        score = row['strength_score']
                        change = row['change_percent']
                        today_selections[sector_name] = {'score': score, 'change': change}

                    selection_cache[sector_type][simulation_date] = today_selections

                    # 验证前一天选出的板块今日表现
                    if d > 0:  # 需要有前一天的数据
                        prev_date = current_date - pd.Timedelta(days=d-1)
                        if prev_date in selection_cache[sector_type]:
                            prev_selections = selection_cache[sector_type][prev_date]

                            # 计算命中率和平均回报
                            hit_count = 0
                            total_return = 0
                            for sector_name, prev_data in prev_selections.items():
                                # 检查该板块今日是否继续上涨
                                if sector_name in today_selections:
                                    today_change = today_selections[sector_name]['change']
                                    if today_change > 0:  # 上涨算命中
                                        hit_count += 1
                                    total_return += today_change

                            # 计算命中率和平均回报
                            hit_rate = hit_count / len(prev_selections) if prev_selections else 0
                            avg_return = total_return / len(prev_selections) if prev_selections else 0

                            # 记录结果
                            self.backtest_results[sector_type]['next_day_hit_rate'].append(hit_rate)
                            self.backtest_results[sector_type]['avg_next_day_return'].append(avg_return)

                            logger.info(f"{sector_type} 板块第 {d} 天验证结果: 命中率 {hit_rate:.2%}, 平均收益 {avg_return:.2%}")

            # 计算总体统计结果
            for sector_type in ['industry', 'concept']:
                hit_rates = self.backtest_results[sector_type]['next_day_hit_rate']
                returns = self.backtest_results[sector_type]['avg_next_day_return']

                if hit_rates:
                    self.backtest_results[sector_type]['avg_hit_rate'] = sum(hit_rates) / len(hit_rates)
                    self.backtest_results[sector_type]['avg_return'] = sum(returns) / len(returns)

                    logger.info(f"{sector_type} 板块总体验证结果:")
                    logger.info(f"  平均命中率: {self.backtest_results[sector_type]['avg_hit_rate']:.2%}")
                    logger.info(f"  平均次日收益: {self.backtest_results[sector_type]['avg_return']:.2%}")

            return self.backtest_results

        except Exception as e:
            logger.error(f"验证评分模型时出错: {str(e)}")
            return {'error': str(e)}

    # 基础分析相关方法

    def run_pipeline(self):
        """完整选股流程"""
        logger.info("启动选股流程...")

        # 步骤1：获取行业板块排名
        industry_top = self.get_top_sectors('industry', 10)
        concept_top = self.get_top_sectors('concept', 10)

        # 步骤2：并行获取成分股
        logger.info("开始获取行业成分股...")
        industry_stocks = self.parallel_fetch_constituents(industry_top, 'industry')

        logger.info("开始获取概念成分股...")
        concept_stocks = self.parallel_fetch_constituents(concept_top, 'concept')

        # 整合结果
        final_result = {
            'industry': industry_stocks,
            'concept': concept_stocks
        }

        logger.info("选股流程完成")

        return final_result


if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="股票选择工具")
    parser.add_argument('--validate', action='store_true', default=False,
                        help='是否验证评分模型')
    parser.add_argument('--validate_days', type=int, default=5,
                        help='验证回溯天数')
    args = parser.parse_args()

    # 初始化选股器
    selector = StockSelector()

    # 如果选择验证模式
    if args.validate:
        logger.info("开始验证评分模型...")
        validation_results = selector.validate_scoring_model(days=args.validate_days)

        # 输出验证结果摘要
        print("\n=== 板块评分模型验证结果 ===")
        for sector_type in ['industry', 'concept']:
            if 'avg_hit_rate' in validation_results[sector_type]:
                print(f"\n{sector_type.title()} 板块:")
                print(f"  平均命中率: {validation_results[sector_type]['avg_hit_rate']:.2%}")
                print(f"  平均次日收益: {validation_results[sector_type]['avg_return']:.2%}")

                # 输出详细的日期结果
                if validation_results[sector_type]['next_day_hit_rate']:
                    print("  每日详细结果:")
                    for i, (hit_rate, ret) in enumerate(zip(
                        validation_results[sector_type]['next_day_hit_rate'],
                        validation_results[sector_type]['avg_next_day_return']
                    )):
                        print(f"    Day {i+1}: 命中率 {hit_rate:.2%}, 收益率 {ret:.2%}")

        print("\n验证完成，建议根据结果调整模型参数")
    else:
        # 运行选股流程
        result = selector.run_pipeline()

        # 示例输出
        print("\n行业板块Top3成分股示例:")
        for sector, df in list(result['industry'].items())[:3]:
            print(f"\n{sector} 成分股:")
            print(df[['代码', '名称', '最新价', '涨跌幅']].head(3))

        print("\n概念板块Top3成分股示例:")
        for sector, df in list(result['concept'].items())[:3]:
            print(f"\n{sector} 成分股:")
            print(df[['代码', '名称', '最新价', '涨跌幅']].head(3))

        print("\n提示: 可使用 --validate 选项验证评分模型准确性")
