# progress_widgets.py
import sys
from PyQt6.QtWidgets import (QWidget, QProgressBar, QLabel, QVBoxLayout, QHBoxLayout,
                             QPushButton, QDialog, QApplication, QFrame)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QColor, QPalette


class ProgressBar(QProgressBar):
    """
    增强型进度条，支持自定义样式和动画效果
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setTextVisible(True)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setMinimum(0)
        self.setMaximum(100)
        self.setValue(0)
        self.setFormat("%p%")
        
        # 设置样式
        self.setStyleSheet("""
            QProgressBar {
                border: 1px solid #CCCCCC;
                border-radius: 5px;
                background-color: #F5F5F5;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 5px;
            }
        """)


class LoadingProgressDialog(QDialog):
    """
    加载进度对话框，用于显示耗时操作的进度
    """
    def __init__(self, parent=None, title="正在加载", message="请稍候...", cancelable=True):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setMinimumWidth(400)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)
        self.setModal(True)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 消息标签
        self.message_label = QLabel(message)
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.message_label)
        
        # 详细信息标签
        self.detail_label = QLabel("")
        self.detail_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.detail_label)
        
        # 进度条
        self.progress_bar = ProgressBar()
        layout.addWidget(self.progress_bar)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch(1)
        
        # 取消按钮
        if cancelable:
            self.cancel_button = QPushButton("取消")
            self.cancel_button.clicked.connect(self.reject)
            button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def set_progress(self, value, detail_text=None):
        """
        更新进度条值和详细信息
        """
        self.progress_bar.setValue(value)
        if detail_text:
            self.detail_label.setText(detail_text)
    
    def set_message(self, message):
        """
        更新主消息
        """
        self.message_label.setText(message)


class StatusIndicator(QFrame):
    """
    状态指示器，用于在状态栏中显示当前操作状态
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.Shape.NoFrame)
        self.setFixedSize(16, 16)
        self._status = "idle"  # idle, loading, success, error
        self._colors = {
            "idle": QColor(200, 200, 200),
            "loading": QColor(66, 133, 244),
            "success": QColor(76, 175, 80),
            "error": QColor(244, 67, 54)
        }
        self._pulse_timer = QTimer(self)
        self._pulse_timer.timeout.connect(self._pulse_animation)
        self._pulse_opacity = 1.0
        self._pulse_direction = -0.1
    
    def set_status(self, status):
        """
        设置状态指示器的状态
        :param status: 'idle', 'loading', 'success', 'error'
        """
        if status not in self._colors:
            return
            
        self._status = status
        
        # 如果是加载状态，启动脉冲动画
        if status == "loading" and not self._pulse_timer.isActive():
            self._pulse_timer.start(50)
        elif status != "loading" and self._pulse_timer.isActive():
            self._pulse_timer.stop()
            self._pulse_opacity = 1.0
        
        self.update()
    
    def _pulse_animation(self):
        """
        脉冲动画效果
        """
        self._pulse_opacity += self._pulse_direction
        if self._pulse_opacity <= 0.4:
            self._pulse_opacity = 0.4
            self._pulse_direction = 0.1
        elif self._pulse_opacity >= 1.0:
            self._pulse_opacity = 1.0
            self._pulse_direction = -0.1
        
        self.update()
    
    def paintEvent(self, event):
        """
        绘制状态指示器
        """
        import math
        from PyQt6.QtGui import QPainter, QBrush
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 获取当前状态的颜色
        color = self._colors[self._status]
        
        # 如果是加载状态，应用脉冲效果
        if self._status == "loading":
            color.setAlphaF(self._pulse_opacity)
        
        # 绘制圆形
        painter.setBrush(QBrush(color))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(2, 2, 12, 12)
        
        # 如果是加载状态，绘制旋转的弧
        if self._status == "loading":
            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(QBrush(QColor(255, 255, 255)))
            from PyQt6.QtCore import QTime
            current_time = QTime.currentTime().msecsSinceStartOfDay() / 1000.0
            start_angle = int((current_time * 360) % 360) * 16
            span_angle = 120 * 16
            painter.drawPie(3, 3, 10, 10, start_angle, span_angle)


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # 测试进度对话框
    dialog = LoadingProgressDialog(title="测试进度对话框", message="正在处理数据...")
    
    # 模拟进度更新
    # 使用列表存储进度值，避免使用nonlocal
    progress_value = [0]
    
    def update_progress():
        progress_value[0] += 5
        if progress_value[0] <= 100:
            dialog.set_progress(progress_value[0], f"处理第 {progress_value[0]} 项...")
            QTimer.singleShot(200, update_progress)
        else:
            dialog.accept()
    
    QTimer.singleShot(200, update_progress)
    dialog.exec()
    
    sys.exit(app.exec())