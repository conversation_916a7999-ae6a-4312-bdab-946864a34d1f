# -*- coding: utf-8 -*-
"""
确定性哈希函数模块
用于替代Python内置的hash()函数，确保股票评分的绝对一致性

作者: LilyBullRider Team
创建时间: 2024
版本: 1.0.0
"""

import hashlib
from typing import Union


class DeterministicHash:
    """
    确定性哈希函数类
    
    提供完全确定性的哈希计算，确保相同输入始终产生相同输出，
    解决Python内置hash()函数在不同进程间结果不一致的问题。
    
    特点:
    - 跨进程一致性：相同输入在任何时候、任何进程中都产生相同结果
    - 高性能：基于MD5算法，计算速度快
    - 安全性：使用加盐机制，避免哈希碰撞
    - 可配置：支持自定义盐值和输出范围
    """
    
    def __init__(self, salt: str = "LilyBullRider_2024"):
        """
        初始化确定性哈希函数
        
        Parameters:
        -----------
        salt : str
            盐值，用于增强哈希安全性，默认为项目标识
        """
        self.salt = salt
    
    def hash_string(self, text: Union[str, int, float], modulo: int = 100) -> int:
        """
        计算字符串的确定性哈希值
        
        Parameters:
        -----------
        text : Union[str, int, float]
            要哈希的文本或数值
        modulo : int
            取模数，控制输出范围 [0, modulo)
            
        Returns:
        --------
        int
            确定性哈希值，范围 [0, modulo)
            
        Examples:
        ---------
        >>> hasher = DeterministicHash()
        >>> hasher.hash_string("人工智能", 100)
        42  # 每次运行都是相同结果
        """
        # 将输入转换为字符串并添加盐值
        salted_text = f"{self.salt}_{str(text)}"
        
        # 使用MD5计算哈希值
        hash_object = hashlib.md5(salted_text.encode('utf-8'))
        hash_hex = hash_object.hexdigest()
        
        # 将十六进制转换为整数并取模
        hash_int = int(hash_hex[:8], 16)  # 取前8位十六进制
        return hash_int % modulo
    
    def hash_float(self, text: Union[str, int, float], 
                   min_val: float = -5.0, max_val: float = 5.0) -> float:
        """
        计算确定性浮点数哈希值
        
        Parameters:
        -----------
        text : Union[str, int, float]
            要哈希的文本或数值
        min_val : float
            输出最小值
        max_val : float
            输出最大值
            
        Returns:
        --------
        float
            确定性浮点数哈希值，范围 [min_val, max_val]
            
        Examples:
        ---------
        >>> hasher = DeterministicHash()
        >>> hasher.hash_float("人工智能", -2.0, 2.0)
        0.42  # 每次运行都是相同结果
        """
        # 获取0-1000范围的整数哈希值
        int_hash = self.hash_string(text, 1000)
        
        # 转换为0-1范围的浮点数
        normalized = int_hash / 1000.0
        
        # 映射到目标范围
        return min_val + normalized * (max_val - min_val)
    
    def sector_adjustment(self, sector_name: str, 
                         adjustment_range: float = 2.0) -> float:
        """
        为板块生成确定性调整值
        
        Parameters:
        -----------
        sector_name : str
            板块名称
        adjustment_range : float
            调整范围，生成 [-adjustment_range, +adjustment_range] 的调整值
            
        Returns:
        --------
        float
            确定性调整值
            
        Examples:
        ---------
        >>> hasher = DeterministicHash()
        >>> hasher.sector_adjustment("人工智能", 2.0)
        -0.84  # 每次运行都是相同结果
        """
        return self.hash_float(f"sector_{sector_name}", 
                              -adjustment_range, adjustment_range)
    
    def technical_adjustment(self, sector_name: str, 
                           indicator_type: str,
                           adjustment_range: float = 5.0) -> float:
        """
        为技术指标生成确定性调整值
        
        Parameters:
        -----------
        sector_name : str
            板块名称
        indicator_type : str
            指标类型（如 'rsi', 'momentum', 'volatility'）
        adjustment_range : float
            调整范围
            
        Returns:
        --------
        float
            确定性技术指标调整值
        """
        return self.hash_float(f"tech_{sector_name}_{indicator_type}", 
                              -adjustment_range, adjustment_range)
    
    def risk_adjustment(self, sector_name: str, 
                       adjustment_range: float = 1.0) -> float:
        """
        为风险因子生成确定性调整值
        
        Parameters:
        -----------
        sector_name : str
            板块名称
        adjustment_range : float
            调整范围
            
        Returns:
        --------
        float
            确定性风险调整值
        """
        return self.hash_float(f"risk_{sector_name}", 
                              -adjustment_range, adjustment_range)


# 创建全局确定性哈希实例
_global_hasher = DeterministicHash()


def deterministic_hash(text: Union[str, int, float], modulo: int = 100) -> int:
    """
    全局确定性哈希函数
    
    这是一个便捷函数，用于替代Python内置的hash()函数
    
    Parameters:
    -----------
    text : Union[str, int, float]
        要哈希的文本或数值
    modulo : int
        取模数，控制输出范围
        
    Returns:
    --------
    int
        确定性哈希值
        
    Examples:
    ---------
    >>> deterministic_hash("人工智能", 100)
    42  # 每次运行都是相同结果
    """
    return _global_hasher.hash_string(text, modulo)


def deterministic_float_hash(text: Union[str, int, float], 
                            min_val: float = -5.0, 
                            max_val: float = 5.0) -> float:
    """
    全局确定性浮点数哈希函数
    
    Parameters:
    -----------
    text : Union[str, int, float]
        要哈希的文本或数值
    min_val : float
        输出最小值
    max_val : float
        输出最大值
        
    Returns:
    --------
    float
        确定性浮点数哈希值
    """
    return _global_hasher.hash_float(text, min_val, max_val)


if __name__ == "__main__":
    # 测试代码
    print("=== 确定性哈希函数测试 ===")
    
    hasher = DeterministicHash()
    
    # 测试字符串哈希
    test_sectors = ["人工智能", "新能源车", "医药生物", "银行", "房地产"]
    
    print("\n板块基本面调整值测试:")
    for sector in test_sectors:
        adjustment = hasher.sector_adjustment(sector, 2.0)
        print(f"{sector}: {adjustment:.2f}")
    
    print("\n技术指标调整值测试:")
    for sector in test_sectors[:3]:
        rsi_adj = hasher.technical_adjustment(sector, "rsi", 3.0)
        momentum_adj = hasher.technical_adjustment(sector, "momentum", 5.0)
        print(f"{sector} - RSI调整: {rsi_adj:.2f}, 动量调整: {momentum_adj:.2f}")
    
    print("\n风险调整值测试:")
    for sector in test_sectors[:3]:
        risk_adj = hasher.risk_adjustment(sector, 1.0)
        print(f"{sector}: {risk_adj:.2f}")
    
    print("\n一致性测试（多次运行应产生相同结果）:")
    test_text = "人工智能"
    for i in range(5):
        result = deterministic_hash(test_text, 100)
        print(f"第{i+1}次: {result}")
    
    print("\n测试完成！所有结果应该完全一致。")