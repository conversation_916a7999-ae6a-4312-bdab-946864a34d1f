#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
缓存管理器模块

提供数据缓存管理功能，支持内存缓存和文件缓存
"""

import os
import json
import pickle
import time
import pandas as pd
import threading
from typing import Dict, Any, Optional, Union
from utils.logging_config import get_logger

# 配置日志
logger = get_logger(__name__)

class CacheManager:
    """
    缓存管理器
    
    用于管理内存缓存和文件缓存，提供数据缓存功能
    """
    
    # 类变量，用于在应用程序全局共享缓存
    _memory_cache = {}
    _memory_cache_timestamps = {}
    _memory_cache_lock = threading.RLock()
    
    def __init__(self, cache_dir="data/cache", max_memory_cache_size=100, use_pickle=True):
        """
        初始化缓存管理器
        
        Args:
            cache_dir: 缓存目录
            max_memory_cache_size: 最大内存缓存项数
            use_pickle: 是否使用pickle格式存储文件缓存
        """
        self.cache_dir = cache_dir
        self.max_memory_cache_size = max_memory_cache_size
        self.use_pickle = use_pickle
        
        # 确保缓存目录存在
        self._ensure_cache_dir()
        
    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir, exist_ok=True)
            logger.info(f"创建缓存目录: {self.cache_dir}")
    
    def get_memory_cache(self, key: str, max_age_seconds: Optional[int] = None) -> Optional[Any]:
        """
        从内存缓存获取数据
        
        Args:
            key: 缓存键
            max_age_seconds: 最大缓存年龄（秒）
            
        Returns:
            缓存数据，如果不存在或过期则返回None
        """
        with CacheManager._memory_cache_lock:
            if key not in CacheManager._memory_cache:
                return None
            
            if max_age_seconds is not None:
                timestamp = CacheManager._memory_cache_timestamps.get(key, 0)
                if time.time() - timestamp > max_age_seconds:
                    # 缓存过期，删除并返回None
                    del CacheManager._memory_cache[key]
                    del CacheManager._memory_cache_timestamps[key]
                    return None
            
            return CacheManager._memory_cache.get(key)
    
    def set_memory_cache(self, key: str, data: Any) -> None:
        """
        设置内存缓存
        
        Args:
            key: 缓存键
            data: 缓存数据
        """
        with CacheManager._memory_cache_lock:
            # 如果缓存已满，删除最旧的项
            if len(CacheManager._memory_cache) >= self.max_memory_cache_size:
                # 找到最旧的缓存项
                oldest_key = None
                oldest_time = float('inf')
                
                for k, t in CacheManager._memory_cache_timestamps.items():
                    if t < oldest_time:
                        oldest_time = t
                        oldest_key = k
                
                # 删除最旧的缓存项
                if oldest_key:
                    del CacheManager._memory_cache[oldest_key]
                    del CacheManager._memory_cache_timestamps[oldest_key]
            
            # 设置缓存和时间戳
            CacheManager._memory_cache[key] = data
            CacheManager._memory_cache_timestamps[key] = time.time()
    
    def clear_memory_cache(self, prefix: Optional[str] = None) -> None:
        """
        清除内存缓存
        
        Args:
            prefix: 缓存键前缀，如果指定则只清除指定前缀的缓存
        """
        with CacheManager._memory_cache_lock:
            if prefix:
                keys_to_delete = []
                for key in CacheManager._memory_cache:
                    if key.startswith(prefix):
                        keys_to_delete.append(key)
                
                for key in keys_to_delete:
                    del CacheManager._memory_cache[key]
                    del CacheManager._memory_cache_timestamps[key]
            else:
                CacheManager._memory_cache.clear()
                CacheManager._memory_cache_timestamps.clear()
    
    def get_cache_file_path(self, cache_name: str) -> str:
        """
        获取缓存文件路径
        
        Args:
            cache_name: 缓存名称
            
        Returns:
            缓存文件路径
        """
        # 替换无效文件名字符
        cache_name = cache_name.replace('/', '_').replace('\\', '_')
        
        # 确定文件扩展名
        extension = '.pkl' if self.use_pickle else '.json'
        
        # 返回完整路径
        return os.path.join(self.cache_dir, f"{cache_name}{extension}")
    
    def save_to_cache(self, data: Any, cache_name: str) -> bool:
        """
        保存数据到缓存
        
        Args:
            data: 要缓存的数据
            cache_name: 缓存名称
            
        Returns:
            是否成功保存
        """
        try:
            # 同时保存到内存缓存
            self.set_memory_cache(cache_name, data)
            
            # 保存到文件缓存
            file_path = self.get_cache_file_path(cache_name)
            
            if self.use_pickle:
                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)
            else:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False)
            
            logger.debug(f"数据已缓存到: {file_path}")
            return True
        except Exception as e:
            logger.error(f"保存缓存失败 {cache_name}: {str(e)}")
            return False
    
    def load_from_cache(self, cache_name: str, max_age_minutes: Optional[int] = None) -> Optional[Any]:
        """
        从缓存加载数据
        
        Args:
            cache_name: 缓存名称
            max_age_minutes: 最大缓存年龄（分钟）
            
        Returns:
            缓存数据，如果不存在或过期则返回None
        """
        # 首先尝试从内存缓存加载
        if max_age_minutes is not None:
            max_age_seconds = max_age_minutes * 60
        else:
            max_age_seconds = None
            
        memory_data = self.get_memory_cache(cache_name, max_age_seconds)
        if memory_data is not None:
            logger.debug(f"从内存缓存加载: {cache_name}")
            return memory_data
        
        # 如果内存缓存不存在，尝试从文件缓存加载
        try:
            file_path = self.get_cache_file_path(cache_name)
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return None
            
            # 检查缓存是否过期
            if max_age_minutes is not None:
                file_age_minutes = (time.time() - os.path.getmtime(file_path)) / 60
                if file_age_minutes > max_age_minutes:
                    logger.debug(f"缓存已过期: {cache_name}")
                    return None
            
            # 加载数据
            if self.use_pickle:
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
            else:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            
            # 加载成功后，更新内存缓存
            self.set_memory_cache(cache_name, data)
            
            logger.debug(f"从文件缓存加载: {file_path}")
            return data
        except Exception as e:
            logger.error(f"加载缓存失败 {cache_name}: {str(e)}")
            return None
    
    def is_cache_valid(self, cache_name: str, max_age_minutes: Optional[int] = None) -> bool:
        """
        检查缓存是否有效
        
        Args:
            cache_name: 缓存名称
            max_age_minutes: 最大缓存年龄（分钟）
            
        Returns:
            缓存是否有效
        """
        # 检查内存缓存
        if max_age_minutes is not None:
            max_age_seconds = max_age_minutes * 60
        else:
            max_age_seconds = None
            
        # 如果内存缓存有效，直接返回True
        if self.get_memory_cache(cache_name, max_age_seconds) is not None:
            return True
        
        # 检查文件缓存
        file_path = self.get_cache_file_path(cache_name)
        
        if not os.path.exists(file_path):
            return False
        
        if max_age_minutes is not None:
            file_age_minutes = (time.time() - os.path.getmtime(file_path)) / 60
            return file_age_minutes <= max_age_minutes
        
        return True
    
    def clear_cache(self, prefix: Optional[str] = None) -> None:
        """
        清除缓存
        
        Args:
            prefix: 缓存名称前缀，如果指定则只清除指定前缀的缓存
        """
        # 清除内存缓存
        self.clear_memory_cache(prefix)
        
        # 清除文件缓存
        try:
            for filename in os.listdir(self.cache_dir):
                file_path = os.path.join(self.cache_dir, filename)
                if os.path.isfile(file_path):
                    if prefix is None or filename.startswith(prefix):
                        os.remove(file_path)
                        logger.debug(f"删除缓存文件: {file_path}")
        except Exception as e:
            logger.error(f"清除缓存失败: {str(e)}")
    
    def save_dataframe_to_cache(self, df: pd.DataFrame, cache_name: str) -> bool:
        """
        保存DataFrame到缓存
        
        Args:
            df: DataFrame数据
            cache_name: 缓存名称
            
        Returns:
            是否成功保存
        """
        return self.save_to_cache(df, cache_name)
    
    def load_dataframe_from_cache(self, cache_name: str, max_age_minutes: Optional[int] = None) -> Optional[pd.DataFrame]:
        """
        从缓存加载DataFrame
        
        Args:
            cache_name: 缓存名称
            max_age_minutes: 最大缓存年龄（分钟）
            
        Returns:
            DataFrame数据，如果不存在或过期则返回None
        """
        data = self.load_from_cache(cache_name, max_age_minutes)
        if data is not None and isinstance(data, pd.DataFrame):
            return data
        return None
    
    def get_cache_size(self) -> Dict[str, Union[int, float]]:
        """
        获取缓存大小信息
        
        Returns:
            缓存大小信息字典，包含文件数、总大小等
        """
        try:
            file_count = 0
            total_size = 0
            
            for filename in os.listdir(self.cache_dir):
                file_path = os.path.join(self.cache_dir, filename)
                if os.path.isfile(file_path):
                    file_count += 1
                    total_size += os.path.getsize(file_path)
            
            memory_cache_count = len(CacheManager._memory_cache)
            
            return {
                'file_count': file_count,
                'total_size_bytes': total_size,
                'total_size_mb': total_size / (1024 * 1024),
                'memory_cache_count': memory_cache_count
            }
        except Exception as e:
            logger.error(f"获取缓存大小失败: {str(e)}")
            return {
                'file_count': 0,
                'total_size_bytes': 0,
                'total_size_mb': 0,
                'memory_cache_count': 0
            }


if __name__ == "__main__":
    # 示例用法
    from utils.logging_config import get_logger
    logger = get_logger(__name__)
    cache_mgr = CacheManager()
    
    # 创建测试数据
    test_data = pd.DataFrame({
        "id": [1, 2, 3],
        "name": ["测试1", "测试2", "测试3"],
        "value": [100, 200, 300]
    })
    
    # 保存到缓存
    cache_mgr.save_dataframe_to_cache(test_data, "test_cache")
    
    # 加载缓存
    df = cache_mgr.load_dataframe_from_cache("test_cache")
    
    print("缓存数据:")
    print(df)
    
    # 检查缓存有效性
    print(f"\n缓存是否有效: {cache_mgr.is_cache_valid('test_cache')}")
    
    # 获取缓存信息
    cache_info = cache_mgr.get_cache_size()
    print(f"\n缓存信息: {cache_info}")