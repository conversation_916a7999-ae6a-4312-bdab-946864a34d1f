# capital_flow/market_fund_flow.py
import akshare as ak
import pandas as pd
from utils.logging_config import get_logger


class MarketFundFlow:
    """大盘资金流向数据处理类"""

    def __init__(self):
        self.logger = get_logger(__name__)
        # 列名映射，用于标准化数据列名
        self.column_mapping = {
            "日期": "date",
            "上证-收盘价": "sh_close",
            "上证-涨跌幅": "sh_change",
            "深证-收盘价": "sz_close",
            "深证-涨跌幅": "sz_change",
            "主力净流入-净额": "main_net_amount",
            "主力净流入-净占比": "main_net_ratio",
            "超大单净流入-净额": "super_large_amount",
            "超大单净流入-净占比": "super_large_ratio",
            "大单净流入-净额": "large_amount",
            "大单净流入-净占比": "large_ratio",
            "中单净流入-净额": "medium_amount",
            "中单净流入-净占比": "medium_ratio",
            "小单净流入-净额": "small_amount",
            "小单净流入-净占比": "small_ratio"
        }

    def get_market_fund_flow(self, normalize=True):
        """
        获取大盘资金流向数据
        
        Args:
            normalize (bool): 是否标准化数据，默认为True
            
        Returns:
            pd.DataFrame: 大盘资金流向数据
        """
        try:
            # 使用akshare获取大盘资金流向数据
            df = ak.stock_market_fund_flow()
            
            if df.empty:
                self.logger.warning("获取大盘资金流向数据为空")
                return pd.DataFrame()
                
            # 数据标准化处理
            if normalize:
                df = self._normalize_data(df)
                
            return df
            
        except Exception as e:
            self.logger.error(f"获取大盘资金流向数据失败: {str(e)}")
            return pd.DataFrame()
    
    def _normalize_data(self, df):
        """
        标准化数据处理
        
        Args:
            df (pd.DataFrame): 原始数据
            
        Returns:
            pd.DataFrame: 标准化后的数据
        """
        # 复制数据，避免修改原始数据
        result = df.copy()
        
        # 重命名列名
        if self.column_mapping:
            result.rename(columns=self.column_mapping, inplace=True)
        
        # 处理日期列
        if 'date' in result.columns:
            result['date'] = pd.to_datetime(result['date'])
        
        # 处理百分比列（涨跌幅和净占比）
        percent_cols = [col for col in result.columns if 'change' in col or 'ratio' in col]
        for col in percent_cols:
            result[col] = pd.to_numeric(result[col], errors='coerce') / 100
        
        # 处理金额列（净额）
        amount_cols = [col for col in result.columns if 'amount' in col]
        for col in amount_cols:
            # 将净额转换为亿元单位
            result[col] = pd.to_numeric(result[col], errors='coerce') / 100000000
            result[col] = result[col].round(2)  # 保留两位小数
        
        # 处理收盘价列
        price_cols = [col for col in result.columns if 'close' in col]
        for col in price_cols:
            result[col] = pd.to_numeric(result[col], errors='coerce').round(2)
        
        return result
    
    def get_recent_days(self, days=5):
        """
        获取最近几天的大盘资金流向数据
        
        Args:
            days (int): 天数，默认为5天
            
        Returns:
            pd.DataFrame: 最近几天的大盘资金流向数据
        """
        df = self.get_market_fund_flow()
        if df.empty or days <= 0:
            return df
        
        # 按日期排序并获取最近几天的数据
        df = df.sort_values('date', ascending=False).head(days)
        return df
    
    def get_summary(self):
        """
        获取大盘资金流向摘要信息
        
        Returns:
            dict: 摘要信息，包含最新一天的主要数据
        """
        df = self.get_recent_days(1)
        if df.empty:
            return {}
        
        # 获取最新一天的数据
        latest = df.iloc[0]
        
        # 构建摘要信息
        summary = {
            "日期": latest.get('date').strftime('%Y-%m-%d'),
            "上证指数": {
                "收盘价": latest.get('sh_close'),
                "涨跌幅": latest.get('sh_change')
            },
            "深证指数": {
                "收盘价": latest.get('sz_close'),
                "涨跌幅": latest.get('sz_change')
            },
            "资金流向": {
                "主力净流入": {
                    "净额(亿)": latest.get('main_net_amount'),
                    "净占比": latest.get('main_net_ratio')
                },
                "超大单净流入": {
                    "净额(亿)": latest.get('super_large_amount'),
                    "净占比": latest.get('super_large_ratio')
                },
                "大单净流入": {
                    "净额(亿)": latest.get('large_amount'),
                    "净占比": latest.get('large_ratio')
                },
                "中单净流入": {
                    "净额(亿)": latest.get('medium_amount'),
                    "净占比": latest.get('medium_ratio')
                },
                "小单净流入": {
                    "净额(亿)": latest.get('small_amount'),
                    "净占比": latest.get('small_ratio')
                }
            }
        }
        
        return summary


# 示例用法
if __name__ == "__main__":
    # 创建大盘资金流向对象
    logger = get_logger(__name__)
    market_flow = MarketFundFlow()
    
    # 获取大盘资金流向数据
    df = market_flow.get_market_fund_flow()
    if not df.empty:
        print("\n大盘资金流向数据示例:")
        print(df.head())
    
    # 获取最近5天的数据
    recent_df = market_flow.get_recent_days(5)
    if not recent_df.empty:
        print("\n最近5天大盘资金流向:")
        print(recent_df[['date', 'sh_close', 'sh_change', 'main_net_amount', 'main_net_ratio']])
    
    # 获取摘要信息
    summary = market_flow.get_summary()
    if summary:
        print("\n大盘资金流向摘要:")
        print(f"日期: {summary['日期']}")
        print(f"上证指数: {summary['上证指数']['收盘价']} ({summary['上证指数']['涨跌幅']:.2%})")
        print(f"深证指数: {summary['深证指数']['收盘价']} ({summary['深证指数']['涨跌幅']:.2%})")
        print(f"主力净流入: {summary['资金流向']['主力净流入']['净额(亿)']:.2f}亿 ({summary['资金流向']['主力净流入']['净占比']:.2%})")
        print(f"超大单净流入: {summary['资金流向']['超大单净流入']['净额(亿)']:.2f}亿 ({summary['资金流向']['超大单净流入']['净占比']:.2%})")
        print(f"大单净流入: {summary['资金流向']['大单净流入']['净额(亿)']:.2f}亿 ({summary['资金流向']['大单净流入']['净占比']:.2%})")
        print(f"中单净流入: {summary['资金流向']['中单净流入']['净额(亿)']:.2f}亿 ({summary['资金流向']['中单净流入']['净占比']:.2%})")
        print(f"小单净流入: {summary['资金流向']['小单净流入']['净额(亿)']:.2f}亿 ({summary['资金流向']['小单净流入']['净占比']:.2%})")