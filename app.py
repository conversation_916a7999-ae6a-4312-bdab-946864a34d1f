#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
运行股票分析系统 - 增强型启动界面
"""

import sys
import os
import threading
import time
from datetime import datetime

try:
    from PyQt6.QtWidgets import QApplication, QSplashScreen, QProgressBar, QMessageBox
    from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject
    from PyQt6.QtGui import QPixmap, QFont, QColor
except ImportError:
    print("PyQt6未安装或导入失败。请确保已安装PyQt6库。")
    sys.exit(1)

# 导入统一日志配置模块
from utils.logging_config import get_logger

# 移除了text_feature_engine相关导入

# 获取配置好的logger实例
logger = get_logger(__name__)
logger.info("应用开始运行，日志系统已配置。")

# 确保logs目录存在
if not os.path.exists("logs"):
    os.makedirs("logs")

# 导入增强型启动画面
from pyqt_gui.app import EnhancedSplashScreen, STARTUP_STAGES

# 资源加载信号器
class ResourceSignals(QObject):
    resource_loaded = pyqtSignal(str, bool)  # 参数: 资源名称, 是否成功

def main():
    """启动应用程序"""
    try:
        # 创建QApplication实例
        app = QApplication(sys.argv)
        app.setStyle("Fusion")

        # 创建资源信号器
        resource_signals = ResourceSignals()

        logger.info("应用启动中...")

        # 创建启动画面
        splash_pix = QPixmap(400, 300)
        splash_pix.fill(Qt.GlobalColor.white)
        splash = EnhancedSplashScreen(splash_pix)
        splash.show()
        app.processEvents()

        logger.info("应用组件加载完成")

        # 模拟启动过程中的各个阶段
        current_stage = 0

        # 启动主界面
        def start_main_window():
            try:
                # 检查启动画面是否仍然存在
                if splash and not splash.isHidden():
                    splash.set_progress(100, "正在启动主界面...")
                QTimer.singleShot(500, load_main_window)
            except RuntimeError:
                # 如果启动画面已被删除，直接加载主窗口
                load_main_window()

        def simulate_startup_progress():
            nonlocal current_stage
            try:
                if current_stage < len(STARTUP_STAGES):
                    progress = int((current_stage / len(STARTUP_STAGES)) * 100)
                    # 检查启动画面是否仍然存在
                    if splash and not splash.isHidden():
                        splash.set_progress(progress, STARTUP_STAGES[current_stage])
                    current_stage += 1
                    QTimer.singleShot(300, simulate_startup_progress)
                else:
                    # 所有阶段完成后，启动主界面
                    start_main_window()
            except RuntimeError:
                # 如果启动画面已被删除，直接启动主界面
                start_main_window()

        # 导入并显示主窗口
        def load_main_window():
            try:
                # 延迟导入主窗口类，减少启动时间
                from pyqt_gui.main_window import StockAnalysisApp

                # 初始化样式
                from pyqt_gui.style_manager import StyleManager
                StyleManager.initialize()
                StyleManager.apply_fusion_style(app)

                # 创建主窗口但不立即显示
                main_window = StockAnalysisApp()

                # 关闭启动画面并显示主窗口
                splash.finish(main_window)
                main_window.show()

                # 主界面启动完成

                # 记录屏幕信息
                screen = app.primaryScreen()
                logger.info(f"屏幕分辨率: {screen.size().width()}x{screen.size().height()}")
                logger.info(f"逻辑DPI: {screen.logicalDotsPerInch()}")
            except Exception as e:
                logger.error(f"加载主窗口时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                # 显示错误对话框
                try:
                    from PyQt6.QtWidgets import QMessageBox
                    error_box = QMessageBox()
                    error_box.setIcon(QMessageBox.Icon.Critical)
                    error_box.setWindowTitle("错误")
                    error_box.setText("加载主窗口时出错")
                    error_box.setDetailedText(f"{str(e)}\n{traceback.format_exc()}")
                    error_box.exec()
                except Exception as msg_error:
                    logger.error(f"无法显示错误对话框: {str(msg_error)}")
                # 关闭启动画面
                try:
                    splash.close()
                except:
                    pass

        # 开始模拟启动进度
        QTimer.singleShot(100, simulate_startup_progress)

        # 运行应用程序事件循环
        return app.exec()
    except Exception as e:
        logger.error(f"应用程序启动时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        # 如果可能，显示错误对话框
        try:
            from PyQt6.QtWidgets import QMessageBox
            app = QApplication(sys.argv) if QApplication.instance() is None else QApplication.instance()
            error_box = QMessageBox()
            error_box.setIcon(QMessageBox.Icon.Critical)
            error_box.setWindowTitle("错误")
            error_box.setText("应用程序启动时出错")
            error_box.setDetailedText(f"{str(e)}\n{traceback.format_exc()}")
            error_box.exec()
        except:
            pass
        return 1

if __name__ == "__main__":
    sys.exit(main())