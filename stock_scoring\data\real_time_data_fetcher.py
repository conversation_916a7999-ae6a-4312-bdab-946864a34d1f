import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import logging
from typing import Optional, Dict, Any, List

# 导入数据验证和清洗模块
try:
    from utils.data_validator import DataValidator, StockDataValidatorFactory
    from utils.data_cleaner import DataCleaner, StockDataCleaningRules
    from utils.validation_config import ValidationConfigManager
    from utils.logging_config import get_logger
except ImportError:
    # 如果导入失败，使用标准logging
    def get_logger(name):
        return logging.getLogger(name)
    DataValidator = None
    DataCleaner = None
    StockDataValidatorFactory = None
    StockDataCleaningRules = None
    ValidationConfigManager = None


class RealTimeDataFetcher:
    """
    实时数据获取器，用于获取股票和指数的实时行情数据
    支持缓存机制以减少API调用频率
    集成数据验证和清洗功能
    """
    
    # 类级别的缓存
    _cache = {}
    _cache_timeout = 60  # 缓存超时时间（秒）
    
    # 数据验证和清洗组件
    _validator = None
    _cleaner = None
    _config_manager = None
    _validation_enabled = True
    _cleaning_enabled = True
    
    # 数据质量统计
    _statistics = {
        'total_requests': 0,
        'validation_failures': 0,
        'cleaning_applied': 0,
        'data_quality_scores': []
    }
    
    @classmethod
    def initialize_validation(cls, enable_validation: bool = True, enable_cleaning: bool = True) -> None:
        """
        初始化数据验证和清洗功能
        
        Args:
            enable_validation: 是否启用数据验证
            enable_cleaning: 是否启用数据清洗
        """
        try:
            cls._validation_enabled = enable_validation
            cls._cleaning_enabled = enable_cleaning
            
            if StockDataValidatorFactory and enable_validation:
                cls._validator = StockDataValidatorFactory.create_realtime_validator()
                
            if StockDataCleaningRules and enable_cleaning:
                cls._cleaner = StockDataCleaningRules.create_realtime_cleaner()
                
            if ValidationConfigManager:
                cls._config_manager = ValidationConfigManager()
                
            logger = get_logger(__name__)
            logger.info(f"实时数据验证功能初始化完成 - 验证: {enable_validation}, 清洗: {enable_cleaning}")
            
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"实时数据验证功能初始化失败: {str(e)}")
    
    @classmethod
    def _validate_and_clean_realtime_data(cls, df: pd.DataFrame, data_type: str) -> tuple:
        """
        验证和清洗实时数据
        
        Args:
            df: 要处理的DataFrame
            data_type: 数据类型标识
            
        Returns:
            tuple: (处理后的DataFrame, 验证信息字典)
        """
        if df is None or df.empty:
            return df, {'original_rows': 0, 'final_rows': 0, 'removed_rows': 0, 
                       'cleaning_applied': False, 'quality_score': 0.0}
        
        # 确保验证功能已初始化
        if cls._validator is None and cls._validation_enabled:
            cls.initialize_validation()
        
        original_rows = len(df)
        cls._statistics['total_requests'] += 1
        
        try:
            # 数据验证
            if cls._validation_enabled and cls._validator:
                # 标准化字段名
                standardized_df = cls._standardize_realtime_fields(df.copy())
                
                # 转换为记录列表进行验证
                records = standardized_df.to_dict('records')
                valid_records = []
                
                for record in records:
                    if cls._validator.validate(record):
                        valid_records.append(record)
                    else:
                        cls._statistics['validation_failures'] += 1
                
                # 转换回DataFrame
                if valid_records:
                    df = pd.DataFrame(valid_records)
                    # 恢复原始字段名
                    df = cls._restore_original_fields(df, standardized_df.columns, df.columns)
                else:
                    df = pd.DataFrame()
            
            # 数据清洗
            cleaning_applied = False
            if cls._cleaning_enabled and cls._cleaner and not df.empty:
                cleaned_df = cls._cleaner.clean(df)
                if not cleaned_df.equals(df):
                    df = cleaned_df
                    cleaning_applied = True
                    cls._statistics['cleaning_applied'] += 1
            
            final_rows = len(df) if df is not None else 0
            removed_rows = original_rows - final_rows
            
            # 计算数据质量评分
            quality_score = (final_rows / original_rows) if original_rows > 0 else 0.0
            cls._statistics['data_quality_scores'].append(quality_score)
            
            validation_info = {
                'original_rows': original_rows,
                'final_rows': final_rows,
                'removed_rows': removed_rows,
                'cleaning_applied': cleaning_applied,
                'quality_score': quality_score
            }
            
            return df, validation_info
            
        except Exception as e:
            logger = get_logger(__name__)
            logger.error(f"实时数据验证清洗失败 ({data_type}): {str(e)}")
            return df, {'original_rows': original_rows, 'final_rows': original_rows, 
                       'removed_rows': 0, 'cleaning_applied': False, 'quality_score': 1.0}
    
    @staticmethod
    def _standardize_realtime_fields(df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化实时数据字段名
        
        Args:
            df: 原始DataFrame
            
        Returns:
            标准化后的DataFrame
        """
        field_mapping = {
            '代码': 'code',
            '名称': 'name', 
            '最新价': 'price',
            '涨跌额': 'change_amount',
            '涨跌幅': 'change_percent',
            '成交量': 'volume',
            '成交额': 'turnover',
            '今开': 'open',
            '最高': 'high',
            '最低': 'low',
            '昨收': 'prev_close',
            'latest_price': 'price',
            'close': 'price'
        }
        
        standardized_df = df.copy()
        for chinese_name, english_name in field_mapping.items():
            if chinese_name in standardized_df.columns:
                standardized_df = standardized_df.rename(columns={chinese_name: english_name})
        
        return standardized_df
    
    @staticmethod
    def _restore_original_fields(df: pd.DataFrame, original_columns, current_columns) -> pd.DataFrame:
        """
        恢复原始字段名
        
        Args:
            df: 处理后的DataFrame
            original_columns: 原始列名
            current_columns: 当前列名
            
        Returns:
            恢复字段名后的DataFrame
        """
        # 简化处理：如果列数匹配，直接使用原始列名
        if len(df.columns) == len(original_columns):
            df.columns = original_columns
        return df
    
    @staticmethod
    def _get_cached_data(cache_key: str) -> Optional[pd.DataFrame]:
        """
        获取缓存数据
        
        Args:
            cache_key: 缓存键
            
        Returns:
            缓存的数据或None
        """
        if cache_key in RealTimeDataFetcher._cache:
            cached_item = RealTimeDataFetcher._cache[cache_key]
            if time.time() - cached_item['timestamp'] < RealTimeDataFetcher._cache_timeout:
                return cached_item['data']
            else:
                # 缓存过期，删除
                del RealTimeDataFetcher._cache[cache_key]
        return None
    
    @staticmethod
    def _set_cached_data(cache_key: str, data: pd.DataFrame) -> None:
        """
        设置缓存数据
        
        Args:
            cache_key: 缓存键
            data: 要缓存的数据
        """
        RealTimeDataFetcher._cache[cache_key] = {
            'data': data.copy(),
            'timestamp': time.time()
        }
    
    @staticmethod
    def get_index_realtime_data(symbol: str = "沪深重要指数") -> Optional[pd.DataFrame]:
        """
        获取指数实时行情数据
        
        Args:
            symbol: 指数类型，可选值："沪深重要指数", "上证系列指数", "深证系列指数", "指数成份", "中证系列指数"
            
        Returns:
            包含指数实时行情的DataFrame，失败时返回None
        """
        cache_key = f"index_realtime_{symbol}"
        
        # 检查缓存
        cached_data = RealTimeDataFetcher._get_cached_data(cache_key)
        if cached_data is not None:
            logging.info(f"从缓存获取指数实时数据: {symbol}")
            return cached_data
        
        try:
            logger = get_logger(__name__)
            logger.info(f"获取指数实时数据: {symbol}")
            data = ak.stock_zh_index_spot_em(symbol=symbol)
            
            if data is not None and not data.empty:
                # 标准化列名
                if '最新价' in data.columns:
                    data = data.rename(columns={
                        '代码': 'code',
                        '名称': 'name',
                        '最新价': 'latest_price',
                        '涨跌额': 'change_amount',
                        '涨跌幅': 'change_percent',
                        '成交量': 'volume',
                        '成交额': 'turnover',
                        '今开': 'open',
                        '最高': 'high',
                        '最低': 'low',
                        '昨收': 'prev_close'
                    })
                
                # 添加时间戳
                data['timestamp'] = datetime.now()
                
                # 数据验证和清洗
                data, validation_info = RealTimeDataFetcher._validate_and_clean_realtime_data(data, 'index_realtime')
                
                # 记录数据质量信息
                if validation_info['removed_rows'] > 0:
                    logger.info(f"指数实时数据验证 - 移除{validation_info['removed_rows']}行无效数据，数据质量评分: {validation_info['quality_score']}")
                
                if data is not None and not data.empty:
                    # 缓存数据
                    RealTimeDataFetcher._set_cached_data(cache_key, data)
                    logger.info(f"成功获取{len(data)}条指数实时数据")
                    return data
                else:
                    logger.warning(f"指数实时数据验证后为空: {symbol}")
                    return None
            else:
                logger.warning(f"获取指数实时数据为空: {symbol}")
                return None
                
        except Exception as e:
            logging.error(f"获取指数实时数据失败: {symbol}, 错误: {str(e)}")
            return None
    
    @staticmethod
    def get_stock_realtime_data(symbol: str, period: str = "1") -> Optional[pd.DataFrame]:
        """
        获取个股实时数据（通过分钟级数据获取最新价格）
        
        Args:
            symbol: 股票代码，如 '000001'
            period: 分钟周期，可选值：'1', '5', '15', '30', '60'
            
        Returns:
            包含股票最新数据的DataFrame，失败时返回None
        """
        cache_key = f"stock_realtime_{symbol}_{period}"
        
        # 检查缓存
        cached_data = RealTimeDataFetcher._get_cached_data(cache_key)
        if cached_data is not None:
            logging.info(f"从缓存获取股票实时数据: {symbol}")
            return cached_data
        
        try:
            logger = get_logger(__name__)
            logger.info(f"获取股票实时数据: {symbol}")
            # 使用分钟级数据获取最新价格
            data = ak.stock_zh_a_minute(symbol=symbol, period=period, adjust="")
            
            if data is not None and not data.empty:
                # 获取最新的一条记录
                latest_data = data.tail(1).copy()
                
                # 标准化列名
                if 'close' in latest_data.columns:
                    latest_data = latest_data.rename(columns={
                        'close': 'latest_price',
                        'open': 'open',
                        'high': 'high',
                        'low': 'low',
                        'volume': 'volume'
                    })
                
                # 添加股票代码和时间戳
                latest_data['code'] = symbol
                latest_data['timestamp'] = datetime.now()
                
                # 数据验证和清洗
                latest_data, validation_info = RealTimeDataFetcher._validate_and_clean_realtime_data(latest_data, 'stock_realtime')
                
                # 记录数据质量信息
                if validation_info['removed_rows'] > 0:
                    logger.info(f"股票实时数据验证 - 移除{validation_info['removed_rows']}行无效数据，数据质量评分: {validation_info['quality_score']}")
                
                if latest_data is not None and not latest_data.empty:
                    # 缓存数据
                    RealTimeDataFetcher._set_cached_data(cache_key, latest_data)
                    logger.info(f"成功获取股票实时数据: {symbol}")
                    return latest_data
                else:
                    logger.warning(f"股票实时数据验证后为空: {symbol}")
                    return None
            else:
                logger.warning(f"获取股票实时数据为空: {symbol}")
                return None
                
        except Exception as e:
            logging.error(f"获取股票实时数据失败: {symbol}, 错误: {str(e)}")
            return None
    
    @staticmethod
    def calculate_realtime_volatility(symbol: str, period: str = "1", window: int = 20) -> Optional[float]:
        """
        计算实时波动率
        
        Args:
            symbol: 股票代码
            period: 分钟周期
            window: 计算窗口大小
            
        Returns:
            波动率值，失败时返回None
        """
        try:
            logging.info(f"计算实时波动率: {symbol}")
            # 获取分钟级数据
            data = ak.stock_zh_a_minute(symbol=symbol, period=period, adjust="")
            
            if data is not None and not data.empty and len(data) >= window:
                # 计算收益率
                data['returns'] = data['close'].pct_change()
                
                # 计算滚动波动率
                volatility = data['returns'].tail(window).std() * np.sqrt(252 * 240 / int(period))  # 年化波动率
                
                logging.info(f"成功计算实时波动率: {symbol}, 波动率: {volatility:.4f}")
                return float(volatility)
            else:
                logging.warning(f"数据不足以计算波动率: {symbol}")
                return None
                
        except Exception as e:
            logging.error(f"计算实时波动率失败: {symbol}, 错误: {str(e)}")
            return None
    
    @staticmethod
    def get_market_status() -> Dict[str, Any]:
        """
        获取市场状态信息
        
        Returns:
            包含市场状态信息的字典
        """
        try:
            # 获取主要指数数据来判断市场状态
            index_data = RealTimeDataFetcher.get_index_realtime_data("沪深重要指数")
            
            if index_data is not None and not index_data.empty:
                # 查找上证指数和深证成指
                sh_index = index_data[index_data['code'].str.contains('000001', na=False)]
                sz_index = index_data[index_data['code'].str.contains('399001', na=False)]
                
                market_status = {
                    'timestamp': datetime.now(),
                    'is_trading': True,  # 简化处理，实际应该根据交易时间判断
                    'sh_index': {
                        'price': float(sh_index['latest_price'].iloc[0]) if not sh_index.empty else None,
                        'change_percent': float(sh_index['change_percent'].iloc[0]) if not sh_index.empty else None
                    },
                    'sz_index': {
                        'price': float(sz_index['latest_price'].iloc[0]) if not sz_index.empty else None,
                        'change_percent': float(sz_index['change_percent'].iloc[0]) if not sz_index.empty else None
                    }
                }
                
                logging.info("成功获取市场状态")
                return market_status
            else:
                logging.warning("无法获取市场状态数据")
                return {'timestamp': datetime.now(), 'is_trading': False}
                
        except Exception as e:
            logging.error(f"获取市场状态失败: {str(e)}")
            return {'timestamp': datetime.now(), 'is_trading': False, 'error': str(e)}
    
    @staticmethod
    def clear_cache() -> None:
        """
        清空缓存
        """
        RealTimeDataFetcher._cache.clear()
        logging.info("已清空实时数据缓存")
    
    @staticmethod
    def get_cache_info() -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            缓存信息字典
        """
        current_time = time.time()
        cache_info = {
            'total_items': len(RealTimeDataFetcher._cache),
            'valid_items': 0,
            'expired_items': 0,
            'cache_timeout': RealTimeDataFetcher._cache_timeout
        }
        
        for key, item in RealTimeDataFetcher._cache.items():
            if current_time - item['timestamp'] < RealTimeDataFetcher._cache_timeout:
                cache_info['valid_items'] += 1
            else:
                cache_info['expired_items'] += 1
        
        return cache_info
    
    @classmethod
    def get_realtime_statistics(cls) -> Dict[str, Any]:
        """
        获取实时数据处理统计信息
        
        Returns:
            统计信息字典
        """
        avg_quality_score = 0.0
        if cls._statistics['data_quality_scores']:
            avg_quality_score = sum(cls._statistics['data_quality_scores']) / len(cls._statistics['data_quality_scores'])
        
        return {
            'total_requests': cls._statistics['total_requests'],
            'validation_failures': cls._statistics['validation_failures'],
            'cleaning_applied': cls._statistics['cleaning_applied'],
            'average_quality_score': avg_quality_score,
            'validation_enabled': cls._validation_enabled,
            'cleaning_enabled': cls._cleaning_enabled
        }
    
    @classmethod
    def reset_realtime_statistics(cls) -> None:
        """
        重置实时数据处理统计信息
        """
        cls._statistics = {
            'total_requests': 0,
            'validation_failures': 0,
            'cleaning_applied': 0,
            'data_quality_scores': []
        }
        
        logger = get_logger(__name__)
        logger.info("实时数据处理统计信息已重置")
    
    @classmethod
    def configure_realtime_validation(cls, enable_validation: bool = None, enable_cleaning: bool = None,
                                    custom_validator=None, custom_cleaner=None) -> None:
        """
        配置实时数据验证和清洗功能
        
        Args:
            enable_validation: 是否启用数据验证
            enable_cleaning: 是否启用数据清洗
            custom_validator: 自定义验证器
            custom_cleaner: 自定义清洗器
        """
        if enable_validation is not None:
            cls._validation_enabled = enable_validation
        
        if enable_cleaning is not None:
            cls._cleaning_enabled = enable_cleaning
        
        if custom_validator is not None:
            cls._validator = custom_validator
        
        if custom_cleaner is not None:
            cls._cleaner = custom_cleaner
        
        logger = get_logger(__name__)
        logger.info(f"实时数据验证配置已更新 - 验证: {cls._validation_enabled}, 清洗: {cls._cleaning_enabled}")