import akshare as ak
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Union, Optional
from datetime import datetime, timedelta
from utils.logging_config import get_logger

class StockAnalyzer:
    def __init__(self):
        self.logger = get_logger(__name__)
        self.data = None
        self.stock_code = None
        self.history_data = None
        self.intraday_data = None
        self.cache = {}
        self._akshare_loaded = False
        self._local_reader_loaded = False

    def _ensure_akshare_loaded(self):
        """确保akshare模块已加载"""
        if not self._akshare_loaded:
            global ak
            import akshare as ak
            self._akshare_loaded = True
    
    def _ensure_local_reader_loaded(self):
        """确保本地数据读取模块已加载"""
        if not self._local_reader_loaded:
            global get_local_history_data
            from technical_analysis.fast_csv_reader import get_local_history_data
            self._local_reader_loaded = True

    def get_history_data(self, stock_code: str, days: int = 30) -> pd.DataFrame:
        """获取个股历史数据，优先从本地缓存读取，如果不存在则从API获取
        
        Args:
            stock_code: 股票代码
            days: 需要获取的天数
            
        Returns:
            DataFrame: 历史行情数据
        """
        try:
            import os
            
            # 延迟加载需要的模块
            self._ensure_local_reader_loaded()
            
            # 首先尝试从本地CSV文件读取数据
            data_dir = "data/historical"
            file_path = os.path.join(data_dir, f"{stock_code}_historical_data.csv")
            
            if os.path.exists(file_path):
                # 使用快速CSV读取器读取本地数据
                df = get_local_history_data(stock_code, data_dir, days)
                if not df.empty:
                    print(f"从本地文件读取{stock_code}历史数据成功，共{len(df)}行")
                    self.history_data = df
                else:
                    # 如果本地读取失败，从API获取
                    self._ensure_akshare_loaded()
                    print(f"本地文件读取失败，从API获取{stock_code}历史数据")
                    end_date = datetime.now().strftime('%Y%m%d')
                    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
                    df = ak.stock_zh_a_hist(symbol=stock_code, period="daily", 
                                          start_date=start_date, end_date=end_date, adjust="qfq")
                    df = df.tail(days)
                    self.history_data = df
            else:
                # 如果本地文件不存在，从API获取
                self._ensure_akshare_loaded()
                print(f"本地文件不存在，从API获取{stock_code}历史数据")
                end_date = datetime.now().strftime('%Y%m%d')
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
                df = ak.stock_zh_a_hist(symbol=stock_code, period="daily", 
                                      start_date=start_date, end_date=end_date, adjust="qfq")
                df = df.tail(days)
                self.history_data = df
            
            # 批量计算常用技术指标
            self.calculate_all_indicators(df)
            
            return df
        except Exception as e:
            print(f"获取历史数据失败：{str(e)}")
            return pd.DataFrame()
    
    def calculate_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """批量计算所有技术指标"""
        if df.empty:
            return df
            
        # 判断数据列名是否是中文
        price_column = '收盘' if '收盘' in df.columns else 'close'
        high_column = '最高' if '最高' in df.columns else 'high'
        low_column = '最低' if '最低' in df.columns else 'low'
        volume_column = '成交量' if '成交量' in df.columns else 'volume'
        date_column = '日期' if '日期' in df.columns else 'date'
        
        close = df[price_column].values
        high = df[high_column].values
        low = df[low_column].values
        volume = df[volume_column].values if volume_column in df.columns else None
        
        # 1. 分时均线（T+0价格动量引擎）
        df['EMA5'] = pd.Series(close).ewm(span=5, adjust=False).mean().values
        df['EMA10'] = pd.Series(close).ewm(span=10, adjust=False).mean().values
        
        # 2. 动态RSI（超买超卖加速器）- 使用RSI(6)而非传统的RSI(14)
        df = self.calculate_rsi(df, price_column, 6)
        
        # 3. 布林带收缩突破（波动率挤压策略）
        df = self.calculate_bollinger_bands(df, price_column, 10, 2)
        
        # 4. 成交量加权MACD（量价背离探测器）
        if volume_column in df.columns:
            df = self.calculate_volume_weighted_macd(df, price_column, volume_column)
        
        # 5. 分时成交量突变（CUSUM异常检测）
        if volume_column in df.columns:
            df = self.calculate_volume_cusum(df, volume_column)
        
        # 6. 价格加速度（二阶导数指标）
        df = self.calculate_price_acceleration(df, price_column)
        
        # 7. 日内波动率通道（ATR动态止损）
        df = self.calculate_atr(df, high_column, low_column, price_column, 14)
        
        # 8. 计算传统MACD (12, 26, 9)
        df = self.calculate_macd(df, price_column)
        
        # 9. 资金流强度（量价背离修正版）
        if volume_column in df.columns:
            df = self.calculate_money_flow_index(df, price_column, volume_column)
        
        return df
            
    def get_intraday_data(self, stock_code: str, period: str = '5') -> pd.DataFrame:
        """获取分钟级别K线数据
        period: 分钟周期，可选 1, 5, 15, 30, 60
        """
        try:
            # 检查缓存
            cache_key = f"{stock_code}_{period}"
            if cache_key in self.cache:
                return self.cache[cache_key]
            
            # 延迟加载akshare模块
            self._ensure_akshare_loaded()
                
            df = ak.stock_zh_a_hist_min_em(symbol=stock_code, period=period)
            df = df.tail(100)  # 获取最近100条记录
            self.intraday_data = df
            
            # 计算所有指标
            self.calculate_all_indicators(df)
            
            # 保存到缓存
            self.cache[cache_key] = df
            
            return df
        except Exception as e:
            print(f"获取分钟级别数据失败：{str(e)}")
            return pd.DataFrame()
    
    def calculate_rsi(self, df: pd.DataFrame, price_column: str, period: int = 6) -> pd.DataFrame:
        """计算RSI指标"""
        delta = df[price_column].diff()
        
        gain = delta.copy()
        loss = delta.copy()
        
        gain[gain < 0] = 0
        loss[loss > 0] = 0
        loss = abs(loss)
        
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        df[f'RSI{period}'] = rsi
        return df
    
    def calculate_bollinger_bands(self, df: pd.DataFrame, price_column: str, 
                                 window: int = 10, num_std: float = 2) -> pd.DataFrame:
        """计算布林带指标"""
        df['BOLL_MA'] = df[price_column].rolling(window=window).mean()
        df['BOLL_STD'] = df[price_column].rolling(window=window).std()
        df['BOLL_UP'] = df['BOLL_MA'] + (df['BOLL_STD'] * num_std)
        df['BOLL_DOWN'] = df['BOLL_MA'] - (df['BOLL_STD'] * num_std)
        
        # 计算带宽
        df['BOLL_BANDWIDTH'] = (df['BOLL_UP'] - df['BOLL_DOWN']) / df['BOLL_MA'] * 100
        
        # 计算布林带百分比
        df['BOLL_PCT'] = (df[price_column] - df['BOLL_DOWN']) / (df['BOLL_UP'] - df['BOLL_DOWN'])
        
        return df
    
    def calculate_volume_weighted_macd(self, df: pd.DataFrame, price_column: str, 
                                      volume_column: str) -> pd.DataFrame:
        """计算成交量加权MACD"""
        # 使用更短的参数(6,12,3)而非传统的(12,26,9)
        volume_price = df[price_column] * df[volume_column]
        
        # 计算EMA
        ema6 = volume_price.ewm(span=6, adjust=False).mean()
        ema12 = volume_price.ewm(span=12, adjust=False).mean()
        
        # 计算DIF (MACD Line)
        df['VOL_MACD_DIF'] = ema6 - ema12
        
        # 计算DEA (Signal Line)
        df['VOL_MACD_DEA'] = df['VOL_MACD_DIF'].ewm(span=3, adjust=False).mean()
        
        # 计算MACD柱状图
        df['VOL_MACD_HIST'] = df['VOL_MACD_DIF'] - df['VOL_MACD_DEA']
        
        return df
    
    def calculate_volume_cusum(self, df: pd.DataFrame, volume_column: str) -> pd.DataFrame:
        """计算成交量CUSUM异常检测指标"""
        # 计算均值和标准差
        volume = df[volume_column]
        mean_volume = volume.rolling(window=20).mean()
        std_volume = volume.rolling(window=20).std()
        
        # 标准化成交量
        normalized_volume = (volume - mean_volume) / std_volume
        
        # 计算CUSUM
        df['CUSUM_HIGH'] = 0.0
        df['CUSUM_LOW'] = 0.0
        
        # 设置敏感度参数k
        k = 0.5
        
        for i in range(1, len(df)):
            # 如果前一行有值且不是NaN
            if i > 0 and not pd.isna(df['CUSUM_HIGH'].iloc[i-1]) and not pd.isna(normalized_volume.iloc[i]):
                # 计算累积上偏和下偏，使用iloc获取前一行的值，使用loc进行赋值
                df.loc[df.index[i], 'CUSUM_HIGH'] = max(0, df['CUSUM_HIGH'].iloc[i-1] + normalized_volume.iloc[i] - k)
                df.loc[df.index[i], 'CUSUM_LOW'] = max(0, df['CUSUM_LOW'].iloc[i-1] - normalized_volume.iloc[i] - k)
        
        # 设置异常阈值
        threshold = 3
        df['VOLUME_ANOMALY'] = ((df['CUSUM_HIGH'] > threshold) | (df['CUSUM_LOW'] > threshold)).astype(int)
        
        return df
    
    def calculate_price_acceleration(self, df: pd.DataFrame, price_column: str) -> pd.DataFrame:
        """计算价格加速度"""
        # 计算一阶差分（价格变化率）
        df['PRICE_VELOCITY'] = df[price_column].diff()
        
        # 计算二阶差分（价格加速度）
        df['PRICE_ACCELERATION'] = df['PRICE_VELOCITY'].diff()
        
        return df
    
    def calculate_atr(self, df: pd.DataFrame, high_column: str, low_column: str, 
                     price_column: str, period: int = 14) -> pd.DataFrame:
        """计算ATR（真实波动幅度）"""
        # 计算TR（真实范围）
        tr1 = df[high_column] - df[low_column]
        tr2 = abs(df[high_column] - df[price_column].shift())
        tr3 = abs(df[low_column] - df[price_column].shift())
        
        # 取三者的最大值
        df['TR'] = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # 计算ATR
        df[f'ATR{period}'] = df['TR'].rolling(window=period).mean()
        
        # 计算动态止损位
        df['STOP_LOSS'] = df[price_column] - 1.5 * df[f'ATR{period}']
        
        return df

    def calculate_macd(self, df: pd.DataFrame, price_column: str = None) -> pd.DataFrame:
        """计算MACD指标"""
        if price_column is None:
            price_column = '收盘' if '收盘' in df.columns else 'close'
        
        # 确保至少有26个数据点
        if len(df) < 26:
            return df
            
        # 计算EMA        
        ema12 = df[price_column].ewm(span=12, adjust=False).mean()
        ema26 = df[price_column].ewm(span=26, adjust=False).mean()
        
        # 计算DIF
        df['DIF'] = ema12 - ema26
        
        # 计算DEA
        df['DEA'] = df['DIF'].ewm(span=9, adjust=False).mean()
        
        # 计算MACD柱状图
        df['MACD'] = (df['DIF'] - df['DEA']) * 2
        
        return df
    
    def calculate_money_flow_index(self, df: pd.DataFrame, price_column: str, 
                                  volume_column: str) -> pd.DataFrame:
        """计算资金流强度指标"""
        # 确定价格变动方向
        price_diff = df[price_column].diff()
        
        # 创建带符号的成交量
        df['SIGNED_VOLUME'] = df[volume_column] * np.sign(price_diff)
        
        # 计算累积资金流
        df['MONEY_FLOW_INDEX'] = df['SIGNED_VOLUME'].cumsum()
        
        return df
    
    def calculate_gap(self, df: pd.DataFrame, price_column: str = None, 
                     open_column: str = None) -> pd.DataFrame:
        """计算缺口指标"""
        if price_column is None:
            price_column = '收盘' if '收盘' in df.columns else 'close'
        if open_column is None:
            open_column = '开盘' if '开盘' in df.columns else 'open'
        
        # 计算前一日收盘价
        prev_close = df[price_column].shift(1)
        
        # 计算缺口百分比
        df['GAP_PCT'] = (df[open_column] / prev_close - 1) * 100
        
        # 标记显著的缺口
        df['SIGNIFICANT_GAP'] = 0
        df.loc[df['GAP_PCT'] > 2, 'SIGNIFICANT_GAP'] = 1  # 显著高开
        df.loc[df['GAP_PCT'] < -2, 'SIGNIFICANT_GAP'] = -1  # 显著低开
        
        return df
            
    def analyze_ma_trend(self, df: pd.DataFrame) -> Dict:
        """分析均线趋势"""
        if df.empty or len(df) < 10:
            return {}
            
        analysis = {}
        
        # 确定价格列名
        price_column = '收盘' if '收盘' in df.columns else 'close'
        
        current_price = df[price_column].iloc[-1]
        
        # 检查均线列是否存在，不存在则计算
        if 'EMA5' not in df.columns:
            df['EMA5'] = df[price_column].ewm(span=5, adjust=False).mean()
        if 'EMA10' not in df.columns:
            df['EMA10'] = df[price_column].ewm(span=10, adjust=False).mean()
        
        ema5 = df['EMA5'].iloc[-1]
        ema10 = df['EMA10'].iloc[-1]
        
        # 判断均线趋势
        ema5_trend = df['EMA5'].iloc[-1] > df['EMA5'].iloc[-2]
        ema10_trend = df['EMA10'].iloc[-1] > df['EMA10'].iloc[-2]
        
        # 判断价格与均线关系
        if current_price > ema5:
            analysis['ema5_signal'] = '股价站上5周期均线，呈现强势'
        else:
            analysis['ema5_signal'] = '股价运行在5周期均线下方，需要观察'
            
        # 判断金叉死叉
        if ema5 > ema10 and df['EMA5'].iloc[-2] <= df['EMA10'].iloc[-2]:
            analysis['ma_cross'] = '5周期均线金叉10周期均线，可能形成上涨趋势'
        elif ema5 < ema10 and df['EMA5'].iloc[-2] >= df['EMA10'].iloc[-2]:
            analysis['ma_cross'] = '5周期均线死叉10周期均线，可能形成下跌趋势'
            
        # 判断趋势
        if ema5_trend and ema10_trend:
            analysis['trend'] = '双均线向上，趋势向好'
        elif not ema5_trend and not ema10_trend:
            analysis['trend'] = '双均线向下，趋势转弱'
        else:
            analysis['trend'] = '均线趋势分歧，建议观望'
            
        return analysis
        
    def analyze_macd(self, df: pd.DataFrame) -> Dict:
        """分析MACD指标"""
        if df.empty or 'DIF' not in df.columns or 'DEA' not in df.columns:
            return {}
        
        analysis = {}
        
        # 获取最新的MACD值
        dif = df['DIF'].iloc[-1]
        dea = df['DEA'].iloc[-1]
        macd = df['MACD'].iloc[-1]
        
        # 判断金叉死叉
        if dif > dea and df['DIF'].iloc[-2] <= df['DEA'].iloc[-2]:
            analysis['cross'] = 'MACD金叉形成，买入信号'
        elif dif < dea and df['DIF'].iloc[-2] >= df['DEA'].iloc[-2]:
            analysis['cross'] = 'MACD死叉形成，卖出信号'
        
        # 判断柱状图变化趋势
        if macd > 0 and df['MACD'].iloc[-2] < df['MACD'].iloc[-1]:
            analysis['histogram'] = 'MACD柱状图扩大，多头动能增强'
        elif macd < 0 and df['MACD'].iloc[-2] > df['MACD'].iloc[-1]:
            analysis['histogram'] = 'MACD柱状图缩小，多头动能减弱'
        elif macd > 0 and df['MACD'].iloc[-2] > df['MACD'].iloc[-1]:
            analysis['histogram'] = 'MACD柱状图缩小，空头动能减弱'
        elif macd < 0 and df['MACD'].iloc[-2] < df['MACD'].iloc[-1]:
            analysis['histogram'] = 'MACD柱状图缩小，空头动能增强'
        
        # 背离分析
        price_column = '收盘' if '收盘' in df.columns else 'close'
        if len(df) > 10:  # 需要足够的数据来检测背离
            # 查找最近的价格高点和MACD高点
            price_peaks = self._find_peaks(df[price_column].values, 5)
            macd_peaks = self._find_peaks(df['DIF'].values, 5)
            
            if len(price_peaks) >= 2 and len(macd_peaks) >= 2:
                # 检查顶背离
                if (price_peaks[-1] > price_peaks[-2] and 
                    macd_peaks[-1] < macd_peaks[-2] and
                    abs(price_peaks[-1] - macd_peaks[-1]) <= 3):  # 允许一定的时间差
                    analysis['divergence'] = 'MACD顶背离，可能见顶回落'
                
                # 检查底背离
                price_troughs = self._find_troughs(df[price_column].values, 5)
                macd_troughs = self._find_troughs(df['DIF'].values, 5)
                
                if (len(price_troughs) >= 2 and len(macd_troughs) >= 2 and
                    price_troughs[-1] < price_troughs[-2] and 
                    macd_troughs[-1] > macd_troughs[-2] and
                    abs(price_troughs[-1] - macd_troughs[-1]) <= 3):
                    analysis['divergence'] = 'MACD底背离，可能触底反弹'
        
        return analysis
    
    def _find_peaks(self, data, min_distance=5):
        """查找数据中的峰值位置"""
        peaks = []
        for i in range(min_distance, len(data) - min_distance):
            if all(data[i] > data[i-j] for j in range(1, min_distance+1)) and \
               all(data[i] > data[i+j] for j in range(1, min_distance+1)):
                peaks.append(i)
        return peaks
    
    def _find_troughs(self, data, min_distance=5):
        """查找数据中的谷值位置"""
        troughs = []
        for i in range(min_distance, len(data) - min_distance):
            if all(data[i] < data[i-j] for j in range(1, min_distance+1)) and \
               all(data[i] < data[i+j] for j in range(1, min_distance+1)):
                troughs.append(i)
        return troughs
        
    def analyze_multi_period(self, stock_code: str) -> Dict:
        """多周期共振分析"""
        analysis = {}
        
        # 获取5分钟和30分钟的数据
        df_5min = self.get_intraday_data(stock_code, '5')
        df_30min = self.get_intraday_data(stock_code, '30')
        
        if not df_5min.empty and not df_30min.empty:
            # 分析5分钟和30分钟的趋势
            trend_5min = self.analyze_ma_trend(df_5min)
            trend_30min = self.analyze_ma_trend(df_30min)
            
            # 判断是否形成多周期共振
            if '5周期均线金叉10周期均线' in trend_5min.get('ma_cross', '') and \
               '双均线向上' in trend_30min.get('trend', ''):
                analysis['resonance'] = '5分钟金叉配合30分钟上升趋势，形成做多信号'
            elif '5周期均线死叉10周期均线' in trend_5min.get('ma_cross', '') and \
                 '双均线向下' in trend_30min.get('trend', ''):
                analysis['resonance'] = '5分钟死叉配合30分钟下降趋势，形成做空信号'
            else:
                analysis['resonance'] = '未形成有效的多周期共振信号'
                
        return analysis

    def calculate_support_resistance(self, window: int = 20) -> Tuple[float, float]:
        """计算支撑位和阻力位"""
        if self.history_data is None or self.history_data.empty:
            return 0.0, 0.0
        
        df = self.history_data
        high_prices = df['最高'].rolling(window=window).max()
        low_prices = df['最低'].rolling(window=window).min()
        
        resistance = high_prices.iloc[-1]
        support = low_prices.iloc[-1]
        return support, resistance

    def get_realtime_data(self, stock_code: str) -> Dict:
        """获取单只股票的实时行情数据"""
        try:
            # 获取实时行情数据
            df = ak.stock_zh_a_spot_em()
            # 查找指定股票代码的数据
            stock_data = df[df['代码'] == stock_code].iloc[0]
            
            return {
                'code': stock_code,
                'name': stock_data['名称'],
                'price': stock_data['最新价'],
                'change_percent': stock_data['涨跌幅'],
                'volume': stock_data['成交量'],
                'turnover_rate': stock_data['换手率'],
                'volume_ratio': stock_data['量比']
            }
        except Exception as e:
            print(f"获取股票{stock_code}数据失败：{str(e)}")
            return {'error': f'获取数据失败: {str(e)}', 'code': stock_code}

    def analyze_volume(self, data: Dict) -> Dict:
        """分析成交量相关指标"""
        analysis = {}
        
        if self.history_data is None:
            self.get_history_data(data['code'])
        
        if self.history_data is not None:
            df = self.history_data  # 使用self.history_data替代df
            # 计算支撑位和阻力位
            support, resistance = self.calculate_support_resistance()
            current_price = data['price']
            
            # 计算前N日平均成交量
            avg_volume = df['成交量'].mean()
            last_volume = df['成交量'].iloc[-1]
            
            # 放量突破分析
            if current_price > resistance and data['volume'] > avg_volume * 1.5:
                analysis['breakthrough'] = '放量突破阻力位，趋势看涨'
            elif current_price < support and data['volume'] > avg_volume * 1.5:
                analysis['breakthrough'] = '放量跌破支撑位，趋势看跌'
            
            # 缩量回调分析
            if current_price < df['收盘'].iloc[-1] and data['volume'] < avg_volume * 0.8:
                analysis['volume_decrease'] = '缩量回调，可能为洗盘'
            
            # 量比分析
            if data['volume_ratio'] > 1.5:
                analysis['volume_ratio_signal'] = '交投活跃，可能启动行情'
            else:
                analysis['volume_ratio_signal'] = '交投一般'

            # 换手率分析
            if data['turnover_rate'] > 10:
                if current_price > resistance:
                    analysis['turnover_signal'] = '换手率较高且突破阻力位，信号更强'
                else:
                    analysis['turnover_signal'] = '换手率较高，筹码交换充分'
            else:
                analysis['turnover_signal'] = '换手率正常'

            # 布林带分析（使用20日布林带，2倍标准差）
            window = 20
            num_std = 2
            if len(df) >= window:
                df['BOLL_MA'] = df['收盘'].rolling(window=window).mean()
                df['BOLL_STD'] = df['收盘'].rolling(window=window).std()
                df['BOLL_UP'] = df['BOLL_MA'] + (df['BOLL_STD'] * num_std)
                df['BOLL_DOWN'] = df['BOLL_MA'] - (df['BOLL_STD'] * num_std)
                
                # 判断布林带突破和反弹信号
                if not df['BOLL_DOWN'].empty and not df['BOLL_UP'].empty:
                    if current_price <= df['BOLL_DOWN'].values[-1]:
                        if data['volume'] < avg_volume * 0.8:
                            analysis['boll_signal'] = '股价触及布林带下轨且缩量，可能出现反弹'
                    elif current_price >= df['BOLL_UP'].values[-1]:
                        if data['volume'] > avg_volume * 1.5:
                            analysis['boll_signal'] = '股价突破布林带上轨且放量，趋势看涨'
                    
                    # 判断布林带收口
                    boll_width = (df['BOLL_UP'] - df['BOLL_DOWN']) / df['BOLL_MA']
                    if len(boll_width) >= 5 and not boll_width.empty:
                        if boll_width.values[-1] < boll_width.values[-5] * 0.8:
                            analysis['boll_convergence'] = '布林带收口，可能即将变盘'
            
            return analysis

    def calculate_amplitude(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算日内振幅"""
        if not df.empty and '最高' in df.columns and '最低' in df.columns and '收盘' in df.columns:
            df['振幅'] = (df['最高'] - df['最低']) / df['收盘'].shift(1) * 100
        return df

    def analyze_amplitude(self, df: pd.DataFrame) -> Dict:
        """分析日内振幅"""
        analysis = {}
        
        if not df.empty and '振幅' in df.columns:
            # 获取最新振幅
            current_amplitude = df['振幅'].values[-1]
            
            # 判断振幅信号
            if current_amplitude > 5:
                analysis['amplitude_signal'] = f'日内振幅{current_amplitude:.2f}%，波动较大，需结合其他指标确认'
        
        return analysis

    def analyze_key_levels(self, df: pd.DataFrame) -> Dict:
        """分析关键点位"""
        analysis = {}
        
        # 获取最新价格
        current_price = df['收盘'].values[-1]
        
        # 分析前期高低点
        previous_high = df['最高'].values[-2]
        previous_low = df['最低'].values[-2]
        
        if abs(current_price - previous_high) / previous_high < 0.02:
            analysis['previous_high'] = f'接近前期高点{previous_high:.2f}，注意突破确认'
        elif abs(current_price - previous_low) / previous_low < 0.02:
            analysis['previous_low'] = f'接近前期低点{previous_low:.2f}，关注支撑有效性'
        
        # 分析整数关口
        price_integer = int(current_price)
        price_decimal = current_price - price_integer
        
        if price_decimal < 0.1 or price_decimal > 0.9:
            analysis['integer_level'] = f'接近整数关口{price_integer + (1 if price_decimal > 0.9 else 0)}元，注意心理压力位'
            
        return analysis

    def print_analysis(self, stock_code: str):
        """打印分析结果"""
        data = self.get_realtime_data(stock_code)
        if data:
            print(f"\n股票代码：{data['code']} ({data['name']})")
            print(f"最新价：{data['price']}")
            print(f"涨跌幅：{data['change_percent']}%")
            print(f"成交量：{data['volume']}")
            print(f"换手率：{data['turnover_rate']}%")
            print(f"量比：{data['volume_ratio']}")
            
            # 成交量分析
            volume_analysis = self.analyze_volume(data)
            print("\n成交量分析：")
            if 'breakthrough' in volume_analysis:
                print(f"突破分析：{volume_analysis['breakthrough']}")
            if 'volume_decrease' in volume_analysis:
                print(f"量能分析：{volume_analysis['volume_decrease']}")
            print(f"量比分析：{volume_analysis['volume_ratio_signal']}")
            print(f"换手率分析：{volume_analysis['turnover_signal']}")
            
            # 日线均线分析
            self.get_history_data(stock_code)
            if self.history_data is not None:
                daily_trend = self.analyze_ma_trend(self.history_data)
                print("\n日线均线分析：")
                if 'ema5_signal' in daily_trend:
                    print(f"均线位置：{daily_trend['ema5_signal']}")
                if 'ma_cross' in daily_trend:
                    print(f"均线交叉：{daily_trend['ma_cross']}")
                if 'trend' in daily_trend:
                    print(f"趋势分析：{daily_trend['trend']}")
            
            # 分钟级别分析
            multi_period = self.analyze_multi_period(stock_code)
            if multi_period:
                print("\n多周期分析：")
                if 'resonance' in multi_period:
                    print(f"共振信号：{multi_period['resonance']}")
                    
            # 动量指标分析
            momentum = self.analyze_momentum(stock_code)
            if momentum:
                print("\nMACD指标分析：")
                if 'cross' in momentum['macd']:
                    print(f"MACD信号：{momentum['macd']['cross']}")
                if 'histogram' in momentum['macd']:
                    print(f"柱状量能：{momentum['macd']['histogram']}")
                if 'divergence' in momentum['macd']:
                    print(f"背离信号：{momentum['macd']['divergence']}")
                    
                print("\nKDJ指标分析：")
                if 'overbought' in momentum['kdj']:
                    print(f"超买信号：{momentum['kdj']['overbought']}")
                elif 'oversold' in momentum['kdj']:
                    print(f"超卖信号：{momentum['kdj']['oversold']}")
                if 'cross' in momentum['kdj']:
                    print(f"KDJ信号：{momentum['kdj']['cross']}")

    def analyze_ultrashort_signals(self, df: pd.DataFrame) -> Dict:
        """分析适合超短线交易的信号组合"""
        if df.empty or len(df) < 20:
            return {'error': '数据不足，无法进行超短线分析'}
        
        signals = {}
        
        # 确定列名
        price_column = '收盘' if '收盘' in df.columns else 'close'
        volume_column = '成交量' if '成交量' in df.columns else 'volume'
        
        # 检查是否已计算所需指标，如果没有则计算
        if 'EMA5' not in df.columns or 'EMA10' not in df.columns:
            df = self.calculate_all_indicators(df)
        
        # 1. 分时均线交叉信号分析
        if df['EMA5'].iloc[-1] > df['EMA10'].iloc[-1] and df['EMA5'].iloc[-2] <= df['EMA10'].iloc[-2]:
            signals['ema_cross'] = {
                'signal': '买入',
                'strength': 'strong',
                'description': '5周期均线金叉10周期均线，形成短线买入信号'
            }
        elif df['EMA5'].iloc[-1] < df['EMA10'].iloc[-1] and df['EMA5'].iloc[-2] >= df['EMA10'].iloc[-2]:
            signals['ema_cross'] = {
                'signal': '卖出',
                'strength': 'strong',
                'description': '5周期均线死叉10周期均线，形成短线卖出信号'
            }
        
        # 2. RSI超买超卖信号分析
        if 'RSI6' in df.columns:
            rsi6 = df['RSI6'].iloc[-1]
            if rsi6 < 20:
                signals['rsi'] = {
                    'signal': '买入',
                    'strength': 'medium',
                    'description': f'RSI(6)={rsi6:.2f}进入超卖区域，可能出现反弹'
                }
            elif rsi6 > 80:
                signals['rsi'] = {
                    'signal': '卖出',
                    'strength': 'medium',
                    'description': f'RSI(6)={rsi6:.2f}进入超买区域，可能出现回调'
                }
        
        # 3. 布林带突破信号
        if all(x in df.columns for x in ['BOLL_UP', 'BOLL_DOWN', 'BOLL_BANDWIDTH']):
            current_price = df[price_column].iloc[-1]
            if current_price > df['BOLL_UP'].iloc[-1]:
                # 检查成交量是否放大
                volume_increase = False
                if volume_column in df.columns:
                    avg_volume = df[volume_column].rolling(window=5).mean().iloc[-2]
                    if df[volume_column].iloc[-1] > avg_volume * 1.5:
                        volume_increase = True
                
                signals['bollinger'] = {
                    'signal': '买入',
                    'strength': 'strong' if volume_increase else 'medium',
                    'description': '价格突破布林带上轨' + ('且成交量放大' if volume_increase else '')
                }
            elif current_price < df['BOLL_DOWN'].iloc[-1]:
                signals['bollinger'] = {
                    'signal': '卖出',
                    'strength': 'strong',
                    'description': '价格跌破布林带下轨，可能继续下跌'
                }
            
            # 布林带宽度收缩信号
            bandwidth = df['BOLL_BANDWIDTH'].iloc[-1]
            bandwidth_percentile = pd.Series(df['BOLL_BANDWIDTH'].dropna()).rank(pct=True).iloc[-1]
            if bandwidth_percentile < 0.1:  # 带宽处于历史10%分位数以下
                signals['bandwidth_squeeze'] = {
                    'signal': '观察',
                    'strength': 'medium',
                    'description': f'布林带宽度收缩至历史{bandwidth_percentile*100:.1f}%分位，可能即将突破'
                }
        
        # 4. 成交量加权MACD信号
        if all(x in df.columns for x in ['VOL_MACD_DIF', 'VOL_MACD_DEA']):
            vol_dif = df['VOL_MACD_DIF'].iloc[-1]
            vol_dea = df['VOL_MACD_DEA'].iloc[-1]
            
            if vol_dif > vol_dea and df['VOL_MACD_DIF'].iloc[-2] <= df['VOL_MACD_DEA'].iloc[-2]:
                signals['vol_macd'] = {
                    'signal': '买入',
                    'strength': 'strong',
                    'description': '成交量加权MACD金叉，短线买入信号'
                }
            elif vol_dif < vol_dea and df['VOL_MACD_DIF'].iloc[-2] >= df['VOL_MACD_DEA'].iloc[-2]:
                signals['vol_macd'] = {
                    'signal': '卖出',
                    'strength': 'strong',
                    'description': '成交量加权MACD死叉，短线卖出信号'
                }
        
        # 5. 成交量异常信号
        if 'VOLUME_ANOMALY' in df.columns and volume_column in df.columns:
            if df['VOLUME_ANOMALY'].iloc[-1] == 1:
                # 判断价格是否同时向上突破
                price_breakout = df[price_column].iloc[-1] > df[price_column].rolling(window=5).max().iloc[-2]
                
                if price_breakout:
                    signals['volume_anomaly'] = {
                        'signal': '买入',
                        'strength': 'strong',
                        'description': '成交量异常放大且价格突破，主力可能介入'
                    }
                else:
                    signals['volume_anomaly'] = {
                        'signal': '观察',
                        'strength': 'medium',
                        'description': '成交量异常放大但价格未突破，可能是洗盘'
                    }
        
        # 6. 价格加速度信号
        if 'PRICE_ACCELERATION' in df.columns:
            if df['PRICE_ACCELERATION'].iloc[-1] > 0 and df['PRICE_ACCELERATION'].iloc[-2] > 0:
                signals['price_acceleration'] = {
                    'signal': '买入',
                    'strength': 'medium',
                    'description': '价格加速度连续为正，上涨动能增强'
                }
            elif df['PRICE_ACCELERATION'].iloc[-1] < 0 and df['PRICE_ACCELERATION'].iloc[-2] > 0:
                signals['price_acceleration'] = {
                    'signal': '卖出',
                    'strength': 'medium',
                    'description': '价格加速度由正转负，上涨动能减弱'
                }
        
        # 7. ATR动态止损信号
        if 'STOP_LOSS' in df.columns:
            current_price = df[price_column].iloc[-1]
            stop_loss = df['STOP_LOSS'].iloc[-1]
            
            if current_price < stop_loss and df[price_column].iloc[-2] >= df['STOP_LOSS'].iloc[-2]:
                signals['atr_stop'] = {
                    'signal': '卖出',
                    'strength': 'strong',
                    'description': f'价格跌破ATR动态止损位{stop_loss:.2f}，建议止损'
                }
        
        # 8. 缺口回补信号
        if 'GAP_PCT' in df.columns and 'SIGNIFICANT_GAP' in df.columns:
            if df['SIGNIFICANT_GAP'].iloc[-1] == 1:  # 高开
                # 检查是否回补一半
                open_price = df['开盘' if '开盘' in df.columns else 'open'].iloc[-1]
                prev_close = df[price_column].iloc[-2]
                current_price = df[price_column].iloc[-1]
                
                gap_size = open_price - prev_close
                gap_fill_pct = (open_price - current_price) / gap_size if gap_size != 0 else 0
                
                if gap_fill_pct >= 0.5:
                    signals['gap_fill'] = {
                        'signal': '买入',
                        'strength': 'medium',
                        'description': f'高开缺口回补一半({gap_fill_pct*100:.1f}%)，可能企稳回升'
                    }
            elif df['SIGNIFICANT_GAP'].iloc[-1] == -1:  # 低开
                # 检查是否回补至缺口上沿
                open_price = df['开盘' if '开盘' in df.columns else 'open'].iloc[-1]
                prev_close = df[price_column].iloc[-2]
                current_price = df[price_column].iloc[-1]
                
                if current_price >= prev_close:
                    signals['gap_fill'] = {
                        'signal': '买入',
                        'strength': 'strong',
                        'description': '低开缺口完全回补，可能继续上涨'
                    }
        
        # 9. 资金流强度信号
        if 'MONEY_FLOW_INDEX' in df.columns:
            mfi_change = df['MONEY_FLOW_INDEX'].diff().iloc[-1]
            price_change = df[price_column].diff().iloc[-1]
            
            # 检测背离
            if mfi_change > 0 and price_change < 0:
                signals['money_flow'] = {
                    'signal': '买入',
                    'strength': 'medium',
                    'description': '资金流增加但价格下跌，可能出现反弹'
                }
            elif mfi_change < 0 and price_change > 0:
                signals['money_flow'] = {
                    'signal': '卖出',
                    'strength': 'medium',
                    'description': '资金流减少但价格上涨，可能出现回调'
                }
        
        # 10. 尾盘竞价博弈信号 (需要分钟级别数据)
        # 这部分需要单独处理，这里只是示例
        
        return signals
    
    def get_ultrashort_strategy(self, stock_code: str, period: str = '5') -> Dict:
        """获取超短线交易策略建议"""
        try:
            # 获取分钟级别数据
            df = self.get_intraday_data(stock_code, period)
            if df.empty:
                return {'error': f'获取{stock_code}分钟级别数据失败'}
            
            # 分析超短线信号
            signals = self.analyze_ultrashort_signals(df)
            
            # 对信号进行评分和总结
            score = 0
            buy_signals = 0
            sell_signals = 0
            
            for signal_type, signal_info in signals.items():
                if 'signal' not in signal_info:
                    continue
                    
                strength_value = {
                    'strong': 2,
                    'medium': 1,
                    'weak': 0.5
                }
                
                value = strength_value.get(signal_info['strength'], 0)
                
                if signal_info['signal'] == '买入':
                    score += value
                    buy_signals += 1
                elif signal_info['signal'] == '卖出':
                    score -= value
                    sell_signals += 1
            
            # 确定最终策略
            strategy = {}
            
            # 基本信息
            strategy['code'] = stock_code
            strategy['signals'] = signals
            strategy['score'] = score
            
            # 综合建议
            if score >= 3:
                strategy['recommendation'] = '强烈推荐买入'
                strategy['confidence'] = 'high'
            elif score >= 1.5:
                strategy['recommendation'] = '建议买入'
                strategy['confidence'] = 'medium'
            elif score <= -3:
                strategy['recommendation'] = '强烈建议卖出'
                strategy['confidence'] = 'high'
            elif score <= -1.5:
                strategy['recommendation'] = '建议卖出'
                strategy['confidence'] = 'medium'
            else:
                strategy['recommendation'] = '观望'
                strategy['confidence'] = 'low'
            
            # 生成止损位
            if 'STOP_LOSS' in df.columns:
                strategy['stop_loss'] = df['STOP_LOSS'].iloc[-1]
            else:
                # 使用ATR计算止损位
                price_column = '收盘' if '收盘' in df.columns else 'close'
                high_column = '最高' if '最高' in df.columns else 'high'
                low_column = '最低' if '最低' in df.columns else 'low'
                
                df = self.calculate_atr(df, high_column, low_column, price_column)
                strategy['stop_loss'] = df['STOP_LOSS'].iloc[-1]
            
            # 技术分析描述
            strategy['analysis'] = self._generate_analysis_text(signals, score)
            
            return strategy
            
        except Exception as e:
            import traceback
            error_msg = f"获取超短线策略失败: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            return {'error': error_msg}
    
    def _generate_analysis_text(self, signals: Dict, score: float) -> str:
        """生成技术分析文本描述"""
        analysis_text = []
        
        # 添加综合评分
        if score >= 3:
            analysis_text.append(f"【综合评分: {score:.1f}】技术指标高度看多，多项指标共振")
        elif score >= 1.5:
            analysis_text.append(f"【综合评分: {score:.1f}】技术指标偏多，短线可能上涨")
        elif score <= -3:
            analysis_text.append(f"【综合评分: {score:.1f}】技术指标高度看空，建议规避")
        elif score <= -1.5:
            analysis_text.append(f"【综合评分: {score:.1f}】技术指标偏空，短线可能下跌")
        else:
            analysis_text.append(f"【综合评分: {score:.1f}】技术指标中性，建议观望")
        
        # 添加各项信号分析
        analysis_text.append("\n【技术指标解读】")
        
        for signal_type, signal_info in signals.items():
            if 'description' in signal_info:
                analysis_text.append(f"• {signal_info['description']}")
        
        # 添加操作建议
        analysis_text.append("\n【操作建议】")
        if score >= 1.5:
            analysis_text.append("• 短线可考虑买入，设置止损位保护")
            if 'bollinger' in signals and signals['bollinger']['signal'] == '买入':
                analysis_text.append("• 布林带突破信号明确，上升空间大")
            if 'volume_anomaly' in signals and signals['volume_anomaly']['signal'] == '买入':
                analysis_text.append("• 成交量异常放大，主力可能介入")
        elif score <= -1.5:
            analysis_text.append("• 短线避险为主，持股注意减仓")
            if 'atr_stop' in signals and signals['atr_stop']['signal'] == '卖出':
                analysis_text.append("• 已跌破ATR止损线，建议无条件离场")
        else:
            analysis_text.append("• 技术信号模糊，建议等待更明确信号")
            if 'bandwidth_squeeze' in signals:
                analysis_text.append("• 布林带收缩，等待突破方向确认后跟进")
            
        return "\n".join(analysis_text)
        
    def get_ultrashort_recommendation(self, stock_code: str) -> Dict:
        """获取超短线交易综合建议"""
        # 获取多个时间周期的数据
        periods = ['5', '15', '30']
        strategies = {}
        
        for period in periods:
            strategies[period] = self.get_ultrashort_strategy(stock_code, period)
        
        # 检查是否所有时间周期都有错误
        errors = [p for p, s in strategies.items() if 'error' in s]
        if len(errors) == len(periods):
            return {'error': f'所有时间周期数据获取失败'}
        
        # 多周期共振分析
        recommendation = {
            'code': stock_code,
            'strategies': strategies,
            'multi_period_analysis': {}
        }
        
        # 计算多周期共振分数
        scores = {p: s.get('score', 0) for p, s in strategies.items() if 'score' in s}
        avg_score = sum(scores.values()) / len(scores) if scores else 0
        
        # 将平均分数添加到返回字典的根级别，以便能够在外部访问
        recommendation['average_score'] = avg_score
        
        # 检查趋势是否一致
        trends = {p: 'bullish' if s.get('score', 0) > 0 else 'bearish' if s.get('score', 0) < 0 else 'neutral' 
                 for p, s in strategies.items() if 'score' in s}
        
        same_trend = len(set(trends.values())) == 1 and 'neutral' not in trends.values()
        
        # 生成多周期共振分析
        if same_trend:
            trend = list(trends.values())[0]
            if trend == 'bullish':
                recommendation['multi_period_analysis']['resonance'] = '多个时间周期共振看多，买入信号更强'
                resonance_strength = 'high'
            else:
                recommendation['multi_period_analysis']['resonance'] = '多个时间周期共振看空，卖出信号更强'
                resonance_strength = 'high'
        else:
            recommendation['multi_period_analysis']['resonance'] = '时间周期信号不一致，建议观望或轻仓操作'
            resonance_strength = 'low'
        
        recommendation['multi_period_analysis']['average_score'] = avg_score
        recommendation['multi_period_analysis']['resonance_strength'] = resonance_strength
        
        # 最终建议
        if avg_score >= 2 and resonance_strength == 'high':
            recommendation['final_recommendation'] = '强烈推荐买入，多周期共振确认'
        elif avg_score >= 1:
            recommendation['final_recommendation'] = '建议买入，注意设置止损'
        elif avg_score <= -2 and resonance_strength == 'high':
            recommendation['final_recommendation'] = '强烈建议卖出，下跌风险高'
        elif avg_score <= -1:
            recommendation['final_recommendation'] = '建议卖出或观望'
        else:
            recommendation['final_recommendation'] = '信号模糊，建议观望'
            
        return recommendation

    def analyze_open_gap(self, stock_code: str) -> Dict:
        """分析开盘缺口情况"""
        try:
            # 获取日线数据
            df = self.get_history_data(stock_code)
            if df.empty:
                return {'error': f'获取{stock_code}日线数据失败'}
            
            # 确保有缺口计算
            if 'GAP_PCT' not in df.columns:
                df = self.calculate_gap(df)
            
            # 获取今日缺口情况
            today_gap = df['GAP_PCT'].iloc[-1]
            
            result = {
                'code': stock_code,
                'today_gap': today_gap,
                'strategy': {}
            }
            
            # 制定缺口策略
            if today_gap > 3:
                result['strategy'] = {
                    'type': 'high_gap',
                    'description': f'高开{today_gap:.2f}%，缺口较大',
                    'recommendation': '等待回补至缺口1/2处做空'
                }
            elif today_gap > 1:
                result['strategy'] = {
                    'type': 'medium_high_gap',
                    'description': f'高开{today_gap:.2f}%，缺口适中',
                    'recommendation': '关注回补情况，回补1/2后谨慎做多'
                }
            elif today_gap < -2:
                result['strategy'] = {
                    'type': 'low_gap',
                    'description': f'低开{today_gap:.2f}%，缺口较大',
                    'recommendation': '关注回补至缺口上沿情况，若能回补则做多'
                }
            elif today_gap < -1:
                result['strategy'] = {
                    'type': 'medium_low_gap',
                    'description': f'低开{today_gap:.2f}%，缺口适中',
                    'recommendation': '密切关注是否能回补，回补则做多'
                }
            else:
                result['strategy'] = {
                    'type': 'no_significant_gap',
                    'description': f'开盘缺口{today_gap:.2f}%不显著',
                    'recommendation': '无缺口策略，参考其他指标'
                }
            
            # 缺口回补统计
            if len(df) > 30:
                # 统计过去30天缺口回补概率
                significant_gaps = df['GAP_PCT'].iloc[-30:].abs() > 1
                num_gaps = significant_gaps.sum()
                
                if num_gaps > 0:
                    # 检查每个显著缺口是否在当天或第二天回补
                    filled_gaps = 0
                    for i in range(-30, 0):
                        if abs(df['GAP_PCT'].iloc[i]) > 1:
                            # 获取开盘价、前收盘价和当日收盘价
                            open_price = df['开盘' if '开盘' in df.columns else 'open'].iloc[i]
                            prev_close = df['收盘' if '收盘' in df.columns else 'close'].iloc[i-1]
                            close_price = df['收盘' if '收盘' in df.columns else 'close'].iloc[i]
                            next_close = df['收盘' if '收盘' in df.columns else 'close'].iloc[i+1] if i < -1 else None
                            
                            # 判断是否回补 (高开的情况下收盘价低于开盘价，低开的情况下收盘价高于开盘价)
                            if (open_price > prev_close and close_price <= prev_close) or \
                               (open_price < prev_close and close_price >= prev_close):
                                filled_gaps += 1
                            # 检查次日是否回补
                            elif next_close is not None:
                                if (open_price > prev_close and next_close <= prev_close) or \
                                   (open_price < prev_close and next_close >= prev_close):
                                    filled_gaps += 1
                    
                    # 计算回补概率
                    fill_probability = filled_gaps / num_gaps
                    result['gap_statistics'] = {
                        'num_significant_gaps': int(num_gaps),
                        'num_filled_gaps': filled_gaps,
                        'fill_probability': fill_probability,
                        'description': f'最近30天内{int(num_gaps)}个显著缺口中有{filled_gaps}个在两天内回补，概率{fill_probability*100:.1f}%'
                    }
            
            return result
            
        except Exception as e:
            import traceback
            error_msg = f"分析开盘缺口失败: {str(e)}\n{traceback.format_exc()}"
            print(error_msg)
            return {'error': error_msg}

    def analyze_momentum(self, stock_code: str, period: str = '5') -> Dict:
        """分析动量指标（MACD和RSI）"""
        analysis = {}
        
        # 获取K线数据
        df = self.get_intraday_data(stock_code, period)
        if df.empty:
            return analysis
            
        # 分析MACD信号
        macd_analysis = self.analyze_macd(df)
        analysis['macd'] = macd_analysis
        
        # 分析RSI信号
        rsi_analysis = {}
        if 'RSI6' in df.columns:
            rsi6 = df['RSI6'].iloc[-1]
            
            if rsi6 > 80:
                rsi_analysis['overbought'] = f'RSI(6)={rsi6:.2f}进入超买区域，可能回调'
            elif rsi6 < 20:
                rsi_analysis['oversold'] = f'RSI(6)={rsi6:.2f}进入超卖区域，可能反弹'
                
            # 判断RSI拐点
            if len(df) > 2:
                rsi_trend = df['RSI6'].diff().iloc[-1]
                if rsi6 < 30 and rsi_trend > 0:
                    rsi_analysis['cross'] = 'RSI超卖区域上穿，可能形成反弹'
                elif rsi6 > 70 and rsi_trend < 0:
                    rsi_analysis['cross'] = 'RSI超买区域下穿，可能形成回调'
        
        analysis['kdj'] = rsi_analysis  # 为了兼容旧代码，使用kdj作为key
        
        return analysis

def main():
    """测试函数"""
    analyzer = StockAnalyzer()
    
    # 测试股票代码
    stock_code = "000001"  # 平安银行
    
    # 获取超短线推荐
    recommendation = analyzer.get_ultrashort_recommendation(stock_code)
    print(f"超短线推荐: {recommendation['final_recommendation']}")
    
    # 打印每个周期的分析
    for period, strategy in recommendation['strategies'].items():
        if 'analysis' in strategy:
            print(f"\n{period}分钟周期分析:")
            print(strategy['analysis'])
    
    # 打印缺口分析
    gap_analysis = analyzer.analyze_open_gap(stock_code)
    if 'error' not in gap_analysis:
        print(f"\n开盘缺口分析:")
        print(f"今日缺口: {gap_analysis['today_gap']:.2f}%")
        print(f"策略建议: {gap_analysis['strategy']['recommendation']}")
        if 'gap_statistics' in gap_analysis:
            print(gap_analysis['gap_statistics']['description'])

if __name__ == "__main__":
    main()