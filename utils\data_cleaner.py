#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清洗工具模块
提供数据异常处理、缺失值填充、异常值检测和数据标准化功能

作者: LilyBullRider Team
创建时间: 2024
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
from datetime import datetime, date, timedelta
from enum import Enum
from dataclasses import dataclass
import logging
from scipy import stats
from sklearn.preprocessing import StandardScaler, MinMaxScaler

# 配置日志
logger = logging.getLogger(__name__)

class CleaningStrategy(Enum):
    """数据清洗策略枚举"""
    DROP = "drop"                    # 删除异常数据
    FILL_MEAN = "fill_mean"          # 用均值填充
    FILL_MEDIAN = "fill_median"      # 用中位数填充
    FILL_MODE = "fill_mode"          # 用众数填充
    FILL_FORWARD = "fill_forward"    # 前向填充
    FILL_BACKWARD = "fill_backward"  # 后向填充
    FILL_INTERPOLATE = "interpolate" # 插值填充
    FILL_ZERO = "fill_zero"          # 用0填充
    FILL_CUSTOM = "fill_custom"      # 自定义值填充
    CAP_OUTLIERS = "cap_outliers"    # 异常值截断
    TRANSFORM_LOG = "transform_log"  # 对数变换
    TRANSFORM_SQRT = "transform_sqrt" # 平方根变换

class OutlierDetectionMethod(Enum):
    """异常值检测方法枚举"""
    IQR = "iqr"                      # 四分位距方法
    Z_SCORE = "z_score"              # Z分数方法
    MODIFIED_Z_SCORE = "modified_z"  # 修正Z分数方法
    ISOLATION_FOREST = "isolation"   # 孤立森林
    BUSINESS_RULES = "business"      # 业务规则

@dataclass
class CleaningRule:
    """数据清洗规则"""
    field_name: str
    strategy: CleaningStrategy
    parameters: Dict[str, Any] = None
    condition: Optional[Callable] = None
    priority: int = 0

@dataclass
class CleaningReport:
    """数据清洗报告"""
    total_records: int
    cleaned_records: int
    dropped_records: int
    filled_values: int
    transformed_values: int
    outliers_detected: int
    outliers_handled: int
    execution_time: float
    field_reports: Dict[str, Dict[str, Any]]
    
    @property
    def cleaning_rate(self) -> float:
        """计算清洗率"""
        if self.total_records == 0:
            return 0.0
        return (self.cleaned_records + self.filled_values + self.transformed_values) / self.total_records

class OutlierDetector:
    """异常值检测器"""
    
    @staticmethod
    def detect_iqr_outliers(data: Union[List, np.ndarray], multiplier: float = 1.5) -> Tuple[np.ndarray, Dict]:
        """使用IQR方法检测异常值"""
        data_array = np.array(data)
        q1 = np.percentile(data_array, 25)
        q3 = np.percentile(data_array, 75)
        iqr = q3 - q1
        
        lower_bound = q1 - multiplier * iqr
        upper_bound = q3 + multiplier * iqr
        
        outliers = (data_array < lower_bound) | (data_array > upper_bound)
        
        stats_info = {
            'q1': q1,
            'q3': q3,
            'iqr': iqr,
            'lower_bound': lower_bound,
            'upper_bound': upper_bound,
            'outlier_count': np.sum(outliers)
        }
        
        return outliers, stats_info
    
    @staticmethod
    def detect_zscore_outliers(data: Union[List, np.ndarray], threshold: float = 3.0) -> Tuple[np.ndarray, Dict]:
        """使用Z分数方法检测异常值"""
        data_array = np.array(data)
        mean = np.mean(data_array)
        std = np.std(data_array)
        
        if std == 0:
            return np.zeros(len(data_array), dtype=bool), {'mean': mean, 'std': std, 'outlier_count': 0}
        
        z_scores = np.abs((data_array - mean) / std)
        outliers = z_scores > threshold
        
        stats_info = {
            'mean': mean,
            'std': std,
            'threshold': threshold,
            'max_z_score': np.max(z_scores),
            'outlier_count': np.sum(outliers)
        }
        
        return outliers, stats_info
    
    @staticmethod
    def detect_modified_zscore_outliers(data: Union[List, np.ndarray], threshold: float = 3.5) -> Tuple[np.ndarray, Dict]:
        """使用修正Z分数方法检测异常值（基于中位数）"""
        data_array = np.array(data)
        median = np.median(data_array)
        mad = np.median(np.abs(data_array - median))
        
        if mad == 0:
            return np.zeros(len(data_array), dtype=bool), {'median': median, 'mad': mad, 'outlier_count': 0}
        
        modified_z_scores = 0.6745 * (data_array - median) / mad
        outliers = np.abs(modified_z_scores) > threshold
        
        stats_info = {
            'median': median,
            'mad': mad,
            'threshold': threshold,
            'max_modified_z': np.max(np.abs(modified_z_scores)),
            'outlier_count': np.sum(outliers)
        }
        
        return outliers, stats_info
    
    @staticmethod
    def detect_business_rule_outliers(data: Dict[str, Any], rules: Dict[str, Callable]) -> Tuple[List[bool], Dict]:
        """使用业务规则检测异常值"""
        outliers = []
        rule_results = {}
        
        for field_name, rule_func in rules.items():
            if field_name in data:
                try:
                    is_outlier = rule_func(data[field_name], data)
                    outliers.append(is_outlier)
                    rule_results[field_name] = is_outlier
                except Exception as e:
                    logger.warning(f"业务规则检测失败 {field_name}: {str(e)}")
                    outliers.append(False)
                    rule_results[field_name] = False
            else:
                outliers.append(False)
                rule_results[field_name] = False
        
        stats_info = {
            'rule_results': rule_results,
            'outlier_count': sum(outliers)
        }
        
        return outliers, stats_info

class DataCleaner:
    """数据清洗器"""
    
    def __init__(self):
        self.cleaning_rules: List[CleaningRule] = []
        self.outlier_detector = OutlierDetector()
        self.stats = {
            'total_cleanings': 0,
            'total_records_processed': 0,
            'total_values_cleaned': 0,
            'total_execution_time': 0.0
        }
    
    def add_cleaning_rule(self, rule: CleaningRule):
        """添加清洗规则"""
        self.cleaning_rules.append(rule)
        # 按优先级排序
        self.cleaning_rules.sort(key=lambda x: x.priority, reverse=True)
    
    def remove_cleaning_rule(self, field_name: str, strategy: CleaningStrategy):
        """移除清洗规则"""
        self.cleaning_rules = [
            rule for rule in self.cleaning_rules 
            if not (rule.field_name == field_name and rule.strategy == strategy)
        ]
    
    def clean_missing_values(self, data: Union[Dict, List[Dict]], 
                           field_name: str, strategy: CleaningStrategy, 
                           custom_value: Any = None) -> Tuple[Union[Dict, List[Dict]], Dict]:
        """处理缺失值"""
        if isinstance(data, dict):
            return self._clean_missing_single_record(data, field_name, strategy, custom_value)
        else:
            return self._clean_missing_batch_records(data, field_name, strategy, custom_value)
    
    def _clean_missing_single_record(self, record: Dict, field_name: str, 
                                   strategy: CleaningStrategy, custom_value: Any = None) -> Tuple[Dict, Dict]:
        """处理单条记录的缺失值"""
        cleaned_record = record.copy()
        report = {'filled': 0, 'dropped': 0, 'method': strategy.value}
        
        if field_name not in record or record[field_name] is None or \
           (isinstance(record[field_name], str) and record[field_name].strip() == ''):
            
            if strategy == CleaningStrategy.DROP:
                return None, {'filled': 0, 'dropped': 1, 'method': strategy.value}
            elif strategy == CleaningStrategy.FILL_ZERO:
                cleaned_record[field_name] = 0
                report['filled'] = 1
            elif strategy == CleaningStrategy.FILL_CUSTOM:
                cleaned_record[field_name] = custom_value
                report['filled'] = 1
            # 其他策略需要在批量处理中实现
        
        return cleaned_record, report
    
    def _clean_missing_batch_records(self, records: List[Dict], field_name: str, 
                                   strategy: CleaningStrategy, custom_value: Any = None) -> Tuple[List[Dict], Dict]:
        """批量处理缺失值"""
        if not records:
            return records, {'filled': 0, 'dropped': 0, 'method': strategy.value}
        
        cleaned_records = []
        report = {'filled': 0, 'dropped': 0, 'method': strategy.value}
        
        # 提取有效值用于统计
        valid_values = []
        for record in records:
            if field_name in record and record[field_name] is not None:
                try:
                    if isinstance(record[field_name], (int, float)):
                        valid_values.append(record[field_name])
                    elif isinstance(record[field_name], str) and record[field_name].strip():
                        # 尝试转换为数值
                        try:
                            valid_values.append(float(record[field_name]))
                        except ValueError:
                            pass
                except:
                    pass
        
        # 计算填充值
        fill_value = None
        if strategy == CleaningStrategy.FILL_MEAN and valid_values:
            fill_value = np.mean(valid_values)
        elif strategy == CleaningStrategy.FILL_MEDIAN and valid_values:
            fill_value = np.median(valid_values)
        elif strategy == CleaningStrategy.FILL_MODE and valid_values:
            fill_value = stats.mode(valid_values)[0][0] if len(valid_values) > 0 else None
        elif strategy == CleaningStrategy.FILL_ZERO:
            fill_value = 0
        elif strategy == CleaningStrategy.FILL_CUSTOM:
            fill_value = custom_value
        
        # 处理每条记录
        for i, record in enumerate(records):
            cleaned_record = record.copy()
            
            is_missing = (field_name not in record or 
                         record[field_name] is None or 
                         (isinstance(record[field_name], str) and record[field_name].strip() == ''))
            
            if is_missing:
                if strategy == CleaningStrategy.DROP:
                    report['dropped'] += 1
                    continue
                elif strategy == CleaningStrategy.FILL_FORWARD:
                    # 前向填充
                    if i > 0 and field_name in cleaned_records[-1]:
                        cleaned_record[field_name] = cleaned_records[-1][field_name]
                        report['filled'] += 1
                elif strategy == CleaningStrategy.FILL_BACKWARD:
                    # 后向填充（需要两次遍历）
                    for j in range(i + 1, len(records)):
                        if (field_name in records[j] and 
                            records[j][field_name] is not None and 
                            records[j][field_name] != ''):
                            cleaned_record[field_name] = records[j][field_name]
                            report['filled'] += 1
                            break
                elif strategy == CleaningStrategy.FILL_INTERPOLATE:
                    # 线性插值（简化版）
                    prev_value = None
                    next_value = None
                    
                    # 找前一个有效值
                    for j in range(i - 1, -1, -1):
                        if (field_name in records[j] and 
                            records[j][field_name] is not None):
                            try:
                                prev_value = float(records[j][field_name])
                                break
                            except:
                                pass
                    
                    # 找后一个有效值
                    for j in range(i + 1, len(records)):
                        if (field_name in records[j] and 
                            records[j][field_name] is not None):
                            try:
                                next_value = float(records[j][field_name])
                                break
                            except:
                                pass
                    
                    if prev_value is not None and next_value is not None:
                        cleaned_record[field_name] = (prev_value + next_value) / 2
                        report['filled'] += 1
                    elif prev_value is not None:
                        cleaned_record[field_name] = prev_value
                        report['filled'] += 1
                    elif next_value is not None:
                        cleaned_record[field_name] = next_value
                        report['filled'] += 1
                elif fill_value is not None:
                    cleaned_record[field_name] = fill_value
                    report['filled'] += 1
            
            cleaned_records.append(cleaned_record)
        
        return cleaned_records, report
    
    def detect_and_handle_outliers(self, data: List[Dict], field_name: str, 
                                 method: OutlierDetectionMethod = OutlierDetectionMethod.IQR,
                                 handling_strategy: CleaningStrategy = CleaningStrategy.CAP_OUTLIERS,
                                 **kwargs) -> Tuple[List[Dict], Dict]:
        """检测和处理异常值"""
        if not data:
            return data, {'outliers_detected': 0, 'outliers_handled': 0}
        
        # 提取数值数据
        values = []
        valid_indices = []
        
        for i, record in enumerate(data):
            if field_name in record and record[field_name] is not None:
                try:
                    value = float(record[field_name])
                    values.append(value)
                    valid_indices.append(i)
                except (ValueError, TypeError):
                    pass
        
        if len(values) < 3:  # 数据太少，无法检测异常值
            return data, {'outliers_detected': 0, 'outliers_handled': 0}
        
        # 检测异常值
        outliers = np.zeros(len(values), dtype=bool)
        detection_stats = {}
        
        if method == OutlierDetectionMethod.IQR:
            multiplier = kwargs.get('multiplier', 1.5)
            outliers, detection_stats = self.outlier_detector.detect_iqr_outliers(values, multiplier)
        elif method == OutlierDetectionMethod.Z_SCORE:
            threshold = kwargs.get('threshold', 3.0)
            outliers, detection_stats = self.outlier_detector.detect_zscore_outliers(values, threshold)
        elif method == OutlierDetectionMethod.MODIFIED_Z_SCORE:
            threshold = kwargs.get('threshold', 3.5)
            outliers, detection_stats = self.outlier_detector.detect_modified_zscore_outliers(values, threshold)
        
        # 处理异常值
        cleaned_data = data.copy()
        outliers_handled = 0
        
        if handling_strategy == CleaningStrategy.CAP_OUTLIERS:
            # 截断异常值
            if 'lower_bound' in detection_stats and 'upper_bound' in detection_stats:
                lower_bound = detection_stats['lower_bound']
                upper_bound = detection_stats['upper_bound']
                
                for i, is_outlier in enumerate(outliers):
                    if is_outlier:
                        record_index = valid_indices[i]
                        original_value = values[i]
                        
                        if original_value < lower_bound:
                            cleaned_data[record_index][field_name] = lower_bound
                            outliers_handled += 1
                        elif original_value > upper_bound:
                            cleaned_data[record_index][field_name] = upper_bound
                            outliers_handled += 1
        
        elif handling_strategy == CleaningStrategy.DROP:
            # 删除异常值记录
            indices_to_remove = [valid_indices[i] for i, is_outlier in enumerate(outliers) if is_outlier]
            cleaned_data = [record for i, record in enumerate(data) if i not in indices_to_remove]
            outliers_handled = len(indices_to_remove)
        
        report = {
            'outliers_detected': np.sum(outliers),
            'outliers_handled': outliers_handled,
            'detection_method': method.value,
            'handling_strategy': handling_strategy.value,
            'detection_stats': detection_stats
        }
        
        return cleaned_data, report
    
    def apply_data_transformation(self, data: List[Dict], field_name: str, 
                                transformation: CleaningStrategy) -> Tuple[List[Dict], Dict]:
        """应用数据变换"""
        if not data:
            return data, {'transformed': 0}
        
        cleaned_data = data.copy()
        transformed_count = 0
        
        for record in cleaned_data:
            if field_name in record and record[field_name] is not None:
                try:
                    value = float(record[field_name])
                    
                    if transformation == CleaningStrategy.TRANSFORM_LOG:
                        if value > 0:
                            record[field_name] = np.log(value)
                            transformed_count += 1
                    elif transformation == CleaningStrategy.TRANSFORM_SQRT:
                        if value >= 0:
                            record[field_name] = np.sqrt(value)
                            transformed_count += 1
                
                except (ValueError, TypeError):
                    pass
        
        report = {
            'transformed': transformed_count,
            'transformation': transformation.value
        }
        
        return cleaned_data, report
    
    def clean_data(self, data: Union[Dict, List[Dict]], 
                  custom_rules: Optional[List[CleaningRule]] = None) -> Tuple[Union[Dict, List[Dict]], CleaningReport]:
        """执行完整的数据清洗流程"""
        start_time = datetime.now()
        
        if isinstance(data, dict):
            data = [data]
            single_record = True
        else:
            single_record = False
        
        if not data:
            return data, CleaningReport(
                total_records=0, cleaned_records=0, dropped_records=0,
                filled_values=0, transformed_values=0, outliers_detected=0,
                outliers_handled=0, execution_time=0.0, field_reports={}
            )
        
        # 使用自定义规则或默认规则
        rules_to_apply = custom_rules if custom_rules else self.cleaning_rules
        
        cleaned_data = data.copy()
        field_reports = {}
        total_filled = 0
        total_transformed = 0
        total_outliers_detected = 0
        total_outliers_handled = 0
        original_count = len(cleaned_data)
        
        # 应用清洗规则
        for rule in rules_to_apply:
            field_name = rule.field_name
            strategy = rule.strategy
            params = rule.parameters or {}
            
            if rule.condition and not rule.condition(cleaned_data):
                continue
            
            try:
                if strategy in [CleaningStrategy.FILL_MEAN, CleaningStrategy.FILL_MEDIAN, 
                              CleaningStrategy.FILL_MODE, CleaningStrategy.FILL_FORWARD,
                              CleaningStrategy.FILL_BACKWARD, CleaningStrategy.FILL_INTERPOLATE,
                              CleaningStrategy.FILL_ZERO, CleaningStrategy.FILL_CUSTOM,
                              CleaningStrategy.DROP]:
                    
                    custom_value = params.get('custom_value')
                    cleaned_data, report = self.clean_missing_values(
                        cleaned_data, field_name, strategy, custom_value
                    )
                    total_filled += report.get('filled', 0)
                    field_reports[f"{field_name}_missing"] = report
                
                elif strategy in [CleaningStrategy.CAP_OUTLIERS]:
                    method = params.get('detection_method', OutlierDetectionMethod.IQR)
                    cleaned_data, report = self.detect_and_handle_outliers(
                        cleaned_data, field_name, method, strategy, **params
                    )
                    total_outliers_detected += report.get('outliers_detected', 0)
                    total_outliers_handled += report.get('outliers_handled', 0)
                    field_reports[f"{field_name}_outliers"] = report
                
                elif strategy in [CleaningStrategy.TRANSFORM_LOG, CleaningStrategy.TRANSFORM_SQRT]:
                    cleaned_data, report = self.apply_data_transformation(
                        cleaned_data, field_name, strategy
                    )
                    total_transformed += report.get('transformed', 0)
                    field_reports[f"{field_name}_transform"] = report
            
            except Exception as e:
                logger.error(f"清洗规则执行失败 {field_name}-{strategy.value}: {str(e)}")
                field_reports[f"{field_name}_error"] = {'error': str(e)}
        
        # 计算最终统计
        final_count = len(cleaned_data) if cleaned_data else 0
        dropped_count = original_count - final_count
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 更新统计信息
        self.stats['total_cleanings'] += 1
        self.stats['total_records_processed'] += original_count
        self.stats['total_values_cleaned'] += total_filled + total_transformed + total_outliers_handled
        self.stats['total_execution_time'] += execution_time
        
        report = CleaningReport(
            total_records=original_count,
            cleaned_records=final_count,
            dropped_records=dropped_count,
            filled_values=total_filled,
            transformed_values=total_transformed,
            outliers_detected=total_outliers_detected,
            outliers_handled=total_outliers_handled,
            execution_time=execution_time,
            field_reports=field_reports
        )
        
        # 如果原始输入是单条记录，返回单条记录
        if single_record:
            return cleaned_data[0] if cleaned_data else None, report
        
        return cleaned_data, report
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取清洗统计信息"""
        return self.stats.copy()
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'total_cleanings': 0,
            'total_records_processed': 0,
            'total_values_cleaned': 0,
            'total_execution_time': 0.0
        }

# 预定义的清洗规则
class StockDataCleaningRules:
    """股票数据清洗规则集合"""
    
    @staticmethod
    def get_basic_stock_cleaning_rules() -> List[CleaningRule]:
        """获取基础股票数据清洗规则"""
        return [
            # 价格字段缺失值处理
            CleaningRule('open', CleaningStrategy.FILL_FORWARD, priority=10),
            CleaningRule('high', CleaningStrategy.FILL_FORWARD, priority=10),
            CleaningRule('low', CleaningStrategy.FILL_FORWARD, priority=10),
            CleaningRule('close', CleaningStrategy.FILL_FORWARD, priority=10),
            
            # 成交量异常值处理
            CleaningRule('volume', CleaningStrategy.CAP_OUTLIERS, 
                        {'detection_method': OutlierDetectionMethod.IQR, 'multiplier': 2.0}, priority=8),
            
            # 成交金额异常值处理
            CleaningRule('amount', CleaningStrategy.CAP_OUTLIERS,
                        {'detection_method': OutlierDetectionMethod.IQR, 'multiplier': 2.0}, priority=8),
            
            # 价格异常值处理
            CleaningRule('close', CleaningStrategy.CAP_OUTLIERS,
                        {'detection_method': OutlierDetectionMethod.MODIFIED_Z_SCORE, 'threshold': 4.0}, priority=7),
        ]
    
    @staticmethod
    def get_financial_data_cleaning_rules() -> List[CleaningRule]:
        """获取财务数据清洗规则"""
        return [
            # 财务比率缺失值处理
            CleaningRule('pe_ratio', CleaningStrategy.FILL_MEDIAN, priority=9),
            CleaningRule('pb_ratio', CleaningStrategy.FILL_MEDIAN, priority=9),
            CleaningRule('roe', CleaningStrategy.FILL_MEDIAN, priority=9),
            
            # 财务比率异常值处理
            CleaningRule('pe_ratio', CleaningStrategy.CAP_OUTLIERS,
                        {'detection_method': OutlierDetectionMethod.IQR, 'multiplier': 3.0}, priority=7),
            CleaningRule('pb_ratio', CleaningStrategy.CAP_OUTLIERS,
                        {'detection_method': OutlierDetectionMethod.IQR, 'multiplier': 3.0}, priority=7),
            
            # 市值数据变换
            CleaningRule('market_cap', CleaningStrategy.TRANSFORM_LOG, priority=5),
        ]

# 预定义的数据清洗器实例
BASIC_STOCK_CLEANER = DataCleaner()
for rule in StockDataCleaningRules.get_basic_stock_cleaning_rules():
    BASIC_STOCK_CLEANER.add_cleaning_rule(rule)

FINANCIAL_DATA_CLEANER = DataCleaner()
for rule in StockDataCleaningRules.get_financial_data_cleaning_rules():
    FINANCIAL_DATA_CLEANER.add_cleaning_rule(rule)

# 组合清洗器
COMPREHENSIVE_CLEANER = DataCleaner()
for rule in (StockDataCleaningRules.get_basic_stock_cleaning_rules() + 
            StockDataCleaningRules.get_financial_data_cleaning_rules()):
    COMPREHENSIVE_CLEANER.add_cleaning_rule(rule)