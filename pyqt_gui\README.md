# PyQt图形界面模块

本模块提供了基于PyQt6的图形用户界面，是AI股票分析系统的交互前端。

## 模块结构

```
pyqt_gui/
├── main_window.py        # 主窗口实现
├── app.py                # 应用程序入口
├── style_manager.py      # 样式管理器
├── chart_widget.py       # 图表组件
├── progress_widgets.py   # 进度条组件
├── thread_manager.py     # 线程管理器
├── styles.py             # 样式定义
├── icons/                # 图标资源
├── resources/            # 其他资源
└── __init__.py
```

## 主要组件

### 1. 主窗口 (main_window.py)

主应用程序窗口，整合了所有UI组件和功能模块。

**主要特点**：
- 现代化的UI设计，符合桌面应用程序规范
- 多标签页布局，方便同时查看多种分析结果
- 响应式设计，适应不同屏幕大小
- 自动调整DPI，支持高分辨率显示器
- 丰富的快捷键和菜单系统

### 2. 样式管理器 (style_manager.py)

负责应用程序的视觉风格和主题。

**主要功能**：
- 多主题支持（暗色主题/亮色主题）
- 自定义字体和颜色方案
- 自适应风格调整
- 图标和资源管理

### 3. 图表组件 (chart_widget.py)

股票数据可视化图表组件。

**主要特点**：
- K线图、分时图、技术指标图表
- 交互式缩放和平移
- 多种图表叠加显示
- 自定义技术指标绘制
- 高性能渲染，支持大量数据点

### 4. 线程管理器 (thread_manager.py)

管理后台线程和异步任务。

**主要功能**：
- 多线程任务处理
- 防止UI卡顿
- 任务队列管理
- 线程池优化

## 主要功能

### 1. 股票分析界面

- 股票搜索和选择
- 多时间周期K线图显示
- 技术指标叠加显示
- 分析结果展示面板

### 2. 股票评分面板

- 综合评分显示
- 多维度评分细节
- 历史评分趋势图
- 评分报告导出

### 3. 选股工具

- 按评分筛选股票
- 按技术指标筛选
- 行业和概念选股
- 自定义选股条件

### 4. 市场概览

- 大盘指数显示
- 行业板块热力图
- 市场宏观指标
- 资金流向分析

### 5. 数据管理

- 历史数据下载和更新
- 数据导入导出
- 数据清理和备份

## 使用示例

### 启动应用程序

```python
from pyqt_gui import start_app

# 启动应用程序
start_app()
```

### 自定义主题

```python
from pyqt_gui import StyleManager

# 初始化样式管理器
style_manager = StyleManager()

# 设置暗色主题
style_manager.set_dark_theme()

# 自定义主题
custom_theme = {
    'primary_color': '#3498db',
    'secondary_color': '#2ecc71',
    'background_color': '#2c3e50',
    'text_color': '#ecf0f1',
    'accent_color': '#e74c3c'
}
style_manager.apply_custom_theme(custom_theme)
```

### 创建自定义图表

```python
from pyqt_gui import ChartWidget
from PyQt6.QtWidgets import QApplication, QMainWindow
import sys

class SimpleChartApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("股票图表示例")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建图表组件
        self.chart = ChartWidget()
        self.setCentralWidget(self.chart)
        
        # 加载股票数据
        self.chart.load_stock_data("600519")
        
        # 添加技术指标
        self.chart.add_indicator("MA", periods=[5, 10, 20])
        self.chart.add_indicator("MACD")
        
        # 显示K线图
        self.chart.show_candlestick()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = SimpleChartApp()
    window.show()
    sys.exit(app.exec())
```

### 使用进度条组件

```python
from pyqt_gui import CircularProgressBar
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
import sys

class ProgressDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("进度条示例")
        self.setGeometry(100, 100, 300, 200)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建圆形进度条
        self.progress = CircularProgressBar()
        self.progress.set_value(75)  # 设置进度为75%
        self.progress.set_color("#3498db")  # 设置蓝色
        self.progress.set_text_visible(True)  # 显示百分比文本
        
        layout.addWidget(self.progress)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ProgressDemo()
    window.show()
    sys.exit(app.exec())
```

## 多线程处理

```python
from pyqt_gui import ThreadManager
from PyQt6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
import sys
import time

class ThreadDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("多线程示例")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建状态标签
        self.status_label = QLabel("准备就绪")
        layout.addWidget(self.status_label)
        
        # 创建按钮
        self.start_button = QPushButton("开始任务")
        self.start_button.clicked.connect(self.start_task)
        layout.addWidget(self.start_button)
        
        # 初始化线程管理器
        self.thread_manager = ThreadManager()
        
    def start_task(self):
        # 定义耗时任务
        def long_running_task():
            # 模拟耗时任务
            for i in range(10):
                time.sleep(0.5)
                # 更新进度
                self.thread_manager.update_progress(i * 10)
            return "任务完成"
        
        # 定义回调函数
        def on_task_complete(result):
            self.status_label.setText(result)
            self.start_button.setEnabled(True)
        
        # 禁用按钮
        self.start_button.setEnabled(False)
        self.status_label.setText("正在处理...")
        
        # 启动线程
        self.thread_manager.run_in_thread(
            task=long_running_task,
            on_complete=on_task_complete,
            on_progress=lambda p: self.status_label.setText(f"进度: {p}%")
        )

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ThreadDemo()
    window.show()
    sys.exit(app.exec())
```

## 系统要求

- **操作系统**: Windows 7+, macOS 10.13+, Linux
- **Python**: 3.7+
- **内存**: 最低4GB，推荐8GB+
- **硬盘空间**: 至少500MB可用空间
- **显示器**: 建议分辨率1366x768或更高

## 依赖项

本模块依赖以下主要库：

```
PyQt6>=6.2.0
pyqtgraph>=0.12.0
numpy>=1.20.0
pandas>=1.3.0
matplotlib>=3.4.0
```

可以通过以下命令安装：

```bash
pip install PyQt6 pyqtgraph numpy pandas matplotlib
```

## 常见问题

1. **Q: 应用程序启动时报错 "ImportError: DLL load failed"**  
   A: 通常是由于缺少某些系统DLL，可以安装最新的Visual C++ Redistributable解决。

2. **Q: 图表显示异常或空白**  
   A: 确保已安装正确版本的pyqtgraph和numpy，并检查数据源是否可用。

3. **Q: 在高分辨率显示器上字体太小**  
   A: 可以通过样式管理器调整应用程序的DPI设置：`StyleManager.set_high_dpi_mode(True)` 