# 开发环境文件
.idea/
venv/
__pycache__/
*.pyc
.vscode/
.DS_Store
.cursor
.trae
.cursorrules


# 敏感数据
config.ini
*.key
*.pem

# 日志和缓存
*.log
logs/
log.txt
last_refresh.txt
data/cache/sector_analysis/last_refresh.txt

# 标准缓存目录
#data/cache/
profiles/
*.sqlite3
ostest.py
test.py
*.json
test_cache.py
test2.py
osbuild.py
*.pkl
# 历史数据
data/historical/
fundamental_analysis/concept_rank.csv
fundamental_analysis/sector_rank.csv
factors_test.csv
*.yaml

# 各模块特定缓存（如仍需保留）
# 已移除的模块目录

# 性能分析文件
*.prof
*.profile
*.perf
data/cache/sector_analysis/last_refresh.txt
tests/