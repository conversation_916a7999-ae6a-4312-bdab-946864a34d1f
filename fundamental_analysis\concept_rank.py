# concept_rank.py
import akshare as ak
import pandas as pd
from typing import Dict, Union
import time
from data.cache.cache_manager import CacheManager
from data.cache.cache_decorators import cached_data
from utils.logging_config import get_logger


class ConceptRankAnalyzer:
    """
    概念板块资金流排名分析模块
    """

    def __init__(self, cache_dir="data/cache"):
        self.logger = get_logger(__name__)
        # 初始化缓存管理器
        self.cache_manager = CacheManager(cache_dir=cache_dir)
        self.column_mapping = {
            "序号": "rank",
            "名称": "concept_name",
            "今日涨跌幅": "change_percent",
            "今日主力净流入-净额": "main_net_amount",
            "今日主力净流入-净占比": "main_net_ratio",
            "今日超大单净流入-净额": "super_large_amount",
            "今日超大单净流入-净占比": "super_large_ratio",
            "今日大单净流入-净额": "large_amount",
            "今日大单净流入-净占比": "large_ratio",
            "今日中单净流入-净额": "medium_amount",
            "今日中单净流入-净占比": "medium_ratio",
            "今日小单净流入-净额": "small_amount",
            "今日小单净流入-净占比": "small_ratio",
            "今日主力净流入最大股": "top_stock"
        }

    def _retry_api_call(self, func, max_retries=3, delay=1, **kwargs):
        """带重试机制的API调用"""
        for attempt in range(max_retries):
            try:
                return func(**kwargs)
            except Exception as e:
                self.logger.warning(f"API调用失败，第{attempt + 1}次重试... 错误信息: {str(e)}")
                time.sleep(delay)
        raise ConnectionError(f"API请求失败，已达最大重试次数{max_retries}次")

    def get_concept_rank(self, indicator: str = "今日", use_cache: bool = False, force_refresh: bool = False) -> pd.DataFrame:
        """
        获取概念板块资金流排名
        :param indicator: 时间周期，可选 ["今日", "5日", "10日"]
        :param use_cache: 是否使用缓存，默认为False (不使用缓存，获取实时数据)
        :param force_refresh: 是否强制刷新数据，默认为False
        :return: 概念资金流排名DataFrame
        """
        valid_indicators = ["今日", "5日", "10日"]
        if indicator not in valid_indicators:
            raise ValueError(f"indicator参数必须为 {valid_indicators} 之一")

        # 根据indicator参数调整缓存名称
        cache_name = f"concept_rank_{indicator}"
        
        # 检查缓存
        if use_cache and not force_refresh and self.cache_manager.is_cache_valid(cache_name, max_age_minutes=30):
            self.logger.info(f"从缓存加载数据: {cache_name}")
            df = self.cache_manager.load_dataframe_from_cache(cache_name)
            if df is not None:
                return df

        try:
            # 从API获取数据
            self.logger.info(f"从API获取概念板块数据: {indicator}")
            df = self._retry_api_call(
                ak.stock_sector_fund_flow_rank,
                indicator=indicator,
                sector_type="概念资金流"
            )

            # 数据清洗
            df = df.rename(columns=self.column_mapping)
            df["update_time"] = pd.Timestamp.now()

            # 转换百分比数据
            percent_cols = [col for col in df.columns if "ratio" in col or "percent" in col]
            for col in percent_cols:
                df[col] = df[col].astype(float)

            # 保存数据到缓存
            if use_cache:
                self.cache_manager.save_dataframe_to_cache(df, cache_name)
                self.logger.info(f"数据已保存到缓存: {cache_name}")
            else:
                self.logger.info(f"使用实时数据，未保存到缓存")
                
            return df

        except Exception as e:
            self.logger.error(f"获取概念板块排名失败: {str(e)}")
            raise

    def get_top_concepts(self, top_n=10, indicator="今日", sort_by="change_percent"):
        """
        获取排名靠前的概念板块
        
        Args:
            top_n (int): 返回的概念数量
            indicator (str): 时间周期，可选 ["今日", "5日", "10日"]
            sort_by (str): 排序依据字段，默认为"change_percent"
            
        Returns:
            pd.DataFrame: 排名靠前的概念板块
        """
        try:
            # 获取全部概念排名
            df = self.get_concept_rank(indicator=indicator)
            
            # 确保排序字段存在
            if sort_by not in df.columns:
                self.logger.warning(f"排序字段{sort_by}不存在，使用默认字段'change_percent'")
                sort_by = "change_percent"
            
            # 按照指定字段降序排序并取前top_n个
            sorted_df = df.sort_values(by=sort_by, ascending=False).head(top_n)
            
            self.logger.info(f"已获取前{top_n}名概念板块")
            return sorted_df
            
        except Exception as e:
            self.logger.error(f"获取热门概念板块失败: {str(e)}")
            # 返回空DataFrame，保持结构一致
            return pd.DataFrame(columns=list(self.column_mapping.values()))

    def save_to_csv(self, df: pd.DataFrame, filename: str = "concept_rank.csv"):
        """保存数据到CSV"""
        try:
            df.to_csv(filename, index=False, encoding='utf_8_sig')
            self.logger.info(f"数据已保存至 {filename}")
        except Exception as e:
            self.logger.error(f"保存文件失败: {str(e)}")
            raise


if __name__ == "__main__":
    # 示例用法
    logger = get_logger(__name__)
    analyzer = ConceptRankAnalyzer()

    try:
        # 获取今日概念排名
        df = analyzer.get_concept_rank(indicator="今日")
        print("最新概念板块排名数据：")
        print(df.head(5))

        # 保存数据
        analyzer.save_to_csv(df)

    except Exception as e:
        print(f"程序运行出错: {e}")
