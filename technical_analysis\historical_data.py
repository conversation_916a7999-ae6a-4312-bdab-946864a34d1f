import os
import pandas as pd
import akshare as ak
import time  # 确保在文件顶部导入time模块
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple

# 使用统一的日志配置系统
from utils.logging_config import get_logger

# 导入数据验证和清洗模块
from utils.data_validator import DataValidator, validate_data
from utils.validation_rules import StockDataValidatorFactory
from utils.data_cleaner import DataCleaner, BASIC_STOCK_CLEANER, COMPREHENSIVE_CLEANER
from utils.validation_config import ValidationConfigManager

# 获取配置好的logger实例
logger = get_logger(__name__)

# 初始化验证和清洗器
validation_config_manager = ValidationConfigManager()
default_validator = StockDataValidatorFactory.create_basic_stock_validator()
default_cleaner = BASIC_STOCK_CLEANER

# 全局配置
ENABLE_VALIDATION = True
ENABLE_CLEANING = True

@validate_data(validator=default_validator)
def get_stock_history(stock_code: str, years: int = 5) -> pd.DataFrame:
    """获取个股历史行情数据(前复权)
    
    Args:
        stock_code: 股票代码
        years: 查询年份数，默认为5年
    
    Returns:
        pd.DataFrame: 包含历史行情数据的DataFrame
    """
    # 输入参数验证
    if not isinstance(stock_code, str) or not stock_code.strip():
        logger.error(f"无效的股票代码: {stock_code}")
        return pd.DataFrame()
    
    if not isinstance(years, int) or years <= 0 or years > 20:
        logger.error(f"无效的年份数: {years}，应为1-20之间的整数")
        return pd.DataFrame()
    
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=365*years)).strftime('%Y%m%d')
    
    try:
        df = ak.stock_zh_a_hist(
            symbol=stock_code,
            period="daily",
            start_date=start_date,
            end_date=end_date,
            adjust="qfq"
        )
        
        # 数据验证和清洗
        if not df.empty:
            df = _validate_and_clean_historical_data(df, stock_code)
        
        return df
    except Exception as e:
        logger.error(f"获取股票{stock_code}历史数据失败：{str(e)}")
        return pd.DataFrame()


@validate_data(validator=default_validator)
def get_recent_stock_history(stock_code: str, days: int = 5) -> pd.DataFrame:
    """获取个股最近几天的历史行情数据(前复权)
    
    Args:
        stock_code: 股票代码
        days: 查询天数，默认为5天
    
    Returns:
        pd.DataFrame: 包含最近历史行情数据的DataFrame
    """
    # 输入参数验证
    if not isinstance(stock_code, str) or not stock_code.strip():
        logger.error(f"无效的股票代码: {stock_code}")
        return pd.DataFrame()
    
    if not isinstance(days, int) or days <= 0 or days > 365:
        logger.error(f"无效的天数: {days}，应为1-365之间的整数")
        return pd.DataFrame()
    
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
    
    try:
        df = ak.stock_zh_a_hist(
            symbol=stock_code,
            period="daily",
            start_date=start_date,
            end_date=end_date,
            adjust="qfq"
        )
        
        # 数据验证和清洗
        if not df.empty:
            df = _validate_and_clean_historical_data(df, stock_code)
        
        return df
    except Exception as e:
        logger.error(f"获取股票{stock_code}最近历史数据失败：{str(e)}")
        return pd.DataFrame()


def _validate_and_clean_historical_data(df: pd.DataFrame, stock_code: str) -> pd.DataFrame:
    """验证和清洗历史数据
    
    Args:
        df: 原始历史数据DataFrame
        stock_code: 股票代码
        
    Returns:
        pd.DataFrame: 验证和清洗后的DataFrame
    """
    if df.empty:
        return df
    
    original_count = len(df)
    
    try:
        # 转换为记录列表进行验证
        records = df.to_dict('records')
        
        # 数据验证
        if ENABLE_VALIDATION and default_validator:
            validated_records = []
            validation_failures = 0
            
            for record in records:
                # 标准化字段名
                standardized_record = _standardize_record_fields(record)
                validated_record, validation_report = default_validator.validate_record(standardized_record)
                
                if validation_report.failures == 0:
                    validated_records.append(record)  # 保留原始记录
                else:
                    validation_failures += 1
                    logger.debug(f"历史数据验证失败 {stock_code}: {len(validation_report.errors)} 个错误")
            
            records = validated_records
            if validation_failures > 0:
                logger.info(f"历史数据验证完成 {stock_code}: 移除 {validation_failures} 条无效记录")
        
        # 数据清洗
        if ENABLE_CLEANING and default_cleaner and records:
            cleaned_records, cleaning_report = default_cleaner.clean_data(records)
            
            if cleaned_records:
                records = cleaned_records
                logger.info(f"历史数据清洗完成 {stock_code}: 清洗率 {cleaning_report.cleaning_rate:.2%}")
            else:
                logger.warning(f"历史数据清洗后为空 {stock_code}")
        
        # 转换回DataFrame
        if records:
            cleaned_df = pd.DataFrame(records)
            
            # 计算数据质量评分
            quality_score = len(cleaned_df) / original_count if original_count > 0 else 0.0
            logger.info(f"历史数据质量评分 {stock_code}: {quality_score:.2%}")
            
            return cleaned_df
        else:
            logger.warning(f"验证和清洗后历史数据为空 {stock_code}")
            return pd.DataFrame()
    
    except Exception as e:
        logger.error(f"历史数据验证和清洗失败 {stock_code}: {str(e)}")
        return df  # 返回原始数据


def _standardize_record_fields(record: Dict[str, Any]) -> Dict[str, Any]:
    """标准化记录字段名，适配验证器
    
    Args:
        record: 原始记录字典
        
    Returns:
        Dict[str, Any]: 标准化后的记录字典
    """
    standardized = {}
    
    # 字段映射关系
    field_mapping = {
        '日期': 'date',
        '开盘': 'open',
        '收盘': 'close', 
        '最高': 'high',
        '最低': 'low',
        '成交量': 'volume',
        '成交额': 'amount',
        '振幅': 'amplitude',
        '涨跌幅': 'pct_chg',
        '涨跌额': 'change',
        '换手率': 'turnover'
    }
    
    # 应用字段映射
    for chinese_name, english_name in field_mapping.items():
        if chinese_name in record:
            standardized[english_name] = record[chinese_name]
    
    # 保留未映射的字段
    for key, value in record.items():
        if key not in field_mapping:
            standardized[key] = value
    
    return standardized


def save_stock_history(stock_code: str, df: pd.DataFrame, data_dir: str = "data/historical") -> None:
    """保存个股历史数据到CSV文件
    
    Args:
        stock_code: 股票代码
        df: 历史行情数据DataFrame
        data_dir: 数据存储目录，默认为'data/historical'
    """
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    
    file_path = os.path.join(data_dir, f"{stock_code}_historical_data.csv")
    df.to_csv(file_path, index=False, encoding="utf-8")
    logger.info(f"已保存{stock_code}历史数据到{file_path}")


def update_stock_history(stock_code: str, df: pd.DataFrame, data_dir: str = "data/historical") -> None:
    """更新个股历史数据，追加新数据到现有文件
    
    Args:
        stock_code: 股票代码
        df: 新的历史行情数据DataFrame
        data_dir: 数据存储目录，默认为'data/historical'
    """
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    
    file_path = os.path.join(data_dir, f"{stock_code}_historical_data.csv")
    
    # 如果文件存在，读取现有数据并与新数据合并
    if os.path.exists(file_path):
        try:
            existing_df = pd.read_csv(file_path, encoding="utf-8")
            
            # 确保日期列是正确的格式
            if '日期' in existing_df.columns:
                existing_df['日期'] = pd.to_datetime(existing_df['日期'])
                df['日期'] = pd.to_datetime(df['日期'])
                
                # 合并数据并删除重复项，保留最新的数据
                combined_df = pd.concat([existing_df, df])
                combined_df = combined_df.drop_duplicates(subset=['日期'], keep='last')
                combined_df = combined_df.sort_values(by='日期')
                
                # 保存合并后的数据
                combined_df.to_csv(file_path, index=False, encoding="utf-8")
                logger.info(f"已更新{stock_code}历史数据到{file_path}")
            else:
                # 如果现有文件格式不正确，直接覆盖
                df.to_csv(file_path, index=False, encoding="utf-8")
                logger.warning(f"已覆盖{stock_code}历史数据到{file_path}")
        except Exception as e:
            logger.error(f"读取或合并数据时出错: {str(e)}，将覆盖原文件")
            # 如果读取现有文件出错，直接覆盖
            df.to_csv(file_path, index=False, encoding="utf-8")
            logger.warning(f"已覆盖{stock_code}历史数据到{file_path}")
    else:
        # 如果文件不存在，直接保存
        df.to_csv(file_path, index=False, encoding="utf-8")
        logger.info(f"已保存{stock_code}历史数据到{file_path}")


def process_single_stock(code: str, data_dir: str) -> tuple:
    """处理单个股票的历史数据获取和保存
    
    Args:
        code: 股票代码
        data_dir: 数据存储目录
        
    Returns:
        tuple: (股票代码, 是否成功)
    """
    try:
        import platform
        # time模块已在文件顶部导入，此处无需再次导入
        
        # 检测CPU类型
        cpu_info = platform.processor().lower()
        is_3d_vcache = any(vcache_model in cpu_info for vcache_model in ['7950x3d', '7900x3d', '7800x3d', 'x3d'])
        
        # 确保数据目录存在
        if not os.path.exists(data_dir):
            try:
                os.makedirs(data_dir)
                logger.info(f"创建数据目录: {data_dir}")
                # 验证目录创建成功
                if not os.path.exists(data_dir):
                    logger.error(f"数据目录创建失败: {data_dir}")
                    return code, False
            except Exception as e:
                logger.error(f"创建数据目录失败: {str(e)}")
                return code, False
        
        # 确保目录有写入权限
        try:
            test_file = os.path.join(data_dir, f"test_{code}.txt")
            with open(test_file, "w") as f:
                f.write("test")
            if os.path.exists(test_file):
                os.remove(test_file)
        except Exception as e:
            logger.error(f"目录写入权限测试失败: {str(e)}")
            return code, False
        
        file_path = os.path.join(data_dir, f"{code}_historical_data.csv")
        
        # 检查是否已有历史数据文件
        if os.path.exists(file_path):
            # 如果文件存在，只获取最近几天的数据进行更新
            logger.info(f"更新股票{code}历史数据...")
            try:
                df = get_recent_stock_history(code)
                if df.empty:
                    logger.warning(f"获取股票{code}最近历史数据为空")
                    return code, False
                    
                # 更新历史数据
                update_stock_history(code, df, data_dir)
                
                # 确保文件写入完成 - 使用多重保障机制
                success = ensure_file_written(file_path, is_3d_vcache)
                if success:
                    logger.info(f"股票{code}历史数据更新完成，文件大小: {os.path.getsize(file_path)}字节")
                    return code, True
                else:
                    logger.error(f"股票{code}历史数据文件验证失败")
                    return code, False
            except Exception as e:
                logger.error(f"更新股票{code}历史数据失败: {str(e)}")
                return code, False
        else:
            # 如果文件不存在，获取完整历史数据
            logger.info(f"获取股票{code}完整历史数据...")
            try:
                df = get_stock_history(code)
                if df.empty:
                    logger.warning(f"获取股票{code}历史数据为空")
                    return code, False
                
                # 保存历史数据
                save_stock_history(code, df, data_dir)
                
                # 如果是3D V-Cache处理器，增加额外延迟让系统处理文件操作
                if is_3d_vcache:
                    logger.info(f"为3D V-Cache处理器增加额外文件写入延迟...")
                    time.sleep(0.5)
                
                # 确保文件写入完成 - 使用多重保障机制
                success = ensure_file_written(file_path, is_3d_vcache)
                if success:
                    logger.info(f"股票{code}历史数据保存完成，文件大小: {os.path.getsize(file_path)}字节")
                    return code, True
                else:
                    logger.error(f"股票{code}历史数据文件验证失败")
                    return code, False
            except Exception as e:
                logger.error(f"获取股票{code}历史数据失败: {str(e)}")
                return code, False
        
        return code, False
    except Exception as e:
        logger.error(f"处理股票{code}时发生错误: {str(e)}")
        return code, False


def ensure_file_written(file_path: str, is_3d_vcache: bool = False) -> bool:
    """确保文件已成功写入到磁盘
    
    使用多种方法验证文件写入成功
    
    Args:
        file_path: 文件路径
        is_3d_vcache: 是否为3D V-Cache处理器
        
    Returns:
        bool: 是否成功验证文件写入
    """
    # time模块已在文件顶部导入，此处无需再次导入
    
    # 如果是3D V-Cache处理器，增加额外延迟和验证步骤
    if is_3d_vcache:
        time.sleep(0.3)  # 额外延迟以确保文件系统有足够时间
    
    try:
        # 1. 基本检查：文件存在且大小大于0
        if not os.path.exists(file_path) or os.path.getsize(file_path) <= 0:
            logger.error(f"文件验证失败：{file_path} 不存在或大小为0")
            return False
            
        # 2. 使用flush和fsync确保数据写入磁盘
        with open(file_path, "a", encoding="utf-8") as f:
            f.flush()
            os.fsync(f.fileno())
            
        # 3. 读取验证：尝试打开文件读取内容（使用UTF-8编码）
        with open(file_path, "r", encoding="utf-8") as f:
            first_line = f.readline()
            if not first_line:
                logger.error(f"文件验证失败：{file_path} 读取为空")
                return False
                
        # 4. 对于3D V-Cache处理器进行更严格的验证
        if is_3d_vcache:
            # 重新打开并进行多次读取测试
            try:
                time.sleep(0.2)  # 额外延迟
                with open(file_path, "r", encoding="utf-8") as f:
                    lines = [f.readline() for _ in range(5)]
                    if not any(lines):
                        logger.error(f"3D V-Cache严格验证失败：{file_path} 读取为空")
                        return False
            except Exception as e:
                logger.error(f"3D V-Cache严格验证异常：{str(e)}")
                return False
                
        return True
    except Exception as e:
        logger.error(f"文件写入验证发生异常：{str(e)}")
        return False


def batch_get_stock_history(stock_codes: list, max_workers: int = None) -> dict:
    """批量获取股票历史数据
    
    Args:
        stock_codes: 股票代码列表
        max_workers: 最大工作线程数，默认为 min(32, CPU核心数+4)
        
    Returns:
        dict: 成功和失败的股票代码
    """
    # 导入必要模块
    import multiprocessing
    import concurrent.futures
    import platform
    import tqdm
    from tqdm import tqdm as tqdm_func  # 确保tqdm_func在函数开始时就定义
    import shutil  # 预先导入，避免后面再导入
    
    # 确保tqdm_func已定义，预防导入失败时的回退方案
    if 'tqdm_func' not in locals():
        # 如果导入失败，使用一个简单的替代函数
        def tqdm_func(iterable, **kwargs):
            logger.info(f"开始处理 {kwargs.get('desc', '任务')}...")
            return iterable
            
    # 检查CPU类型
    cpu_info = platform.processor().lower()
    is_amd_cpu = 'amd' in cpu_info
    is_3d_vcache = any(vcache_model in cpu_info for vcache_model in ['7950x3d', '7900x3d', '7800x3d', 'x3d'])
    
    # 确保历史数据目录存在
    data_dir = "data/historical"
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    
    results = {"success": [], "failed": []}
    total_stocks = len(stock_codes)
    
    # 如果未指定max_workers，则根据CPU核心数和股票数量确定
    if max_workers is None:
        cpu_count = multiprocessing.cpu_count()
        
        # 针对AMD 3D V-Cache特别优化
        if is_3d_vcache:
            # 对于7950X3D等3D V-Cache CPU，使用严格限制的线程数
            max_workers = 2  # 仅使用2个线程，减少缓存争用
            logger.info(f"检测到AMD 3D V-Cache CPU ({cpu_info})，使用特别优化配置: {max_workers}个线程")
        # 针对普通AMD CPU优化
        elif is_amd_cpu:
            # AMD CPU上使用更少的线程，避免资源竞争
            max_workers = min(cpu_count // 4, total_stocks, 3)  # 最多3个线程，且不超过CPU核心数的四分之一
            logger.info(f"检测到AMD CPU ({cpu_info})，使用优化的线程配置: {max_workers}个线程")
        else:
            # 其他CPU使用正常配置
            max_workers = min(cpu_count, total_stocks, 8)  # 最多8个线程
    
    logger.info(f"开始并行获取{total_stocks}只股票的历史数据，使用{max_workers}个工作线程...")
    
    # 确保数据目录存在且可写
    try:
        test_file = os.path.join(data_dir, "test_write.txt")
        with open(test_file, "w") as f:
            f.write("test")
        if os.path.exists(test_file):
            os.remove(test_file)
    except Exception as e:
        logger.error(f"数据目录写入测试失败: {str(e)}")
        logger.error(f"请确保目录 {os.path.abspath(data_dir)} 存在且有写入权限")
        return {"success": [], "failed": stock_codes}
    
    # 针对3D V-Cache处理器，可能需要使用完全串行模式
    if is_3d_vcache:
        logger.info(f"为AMD 3D V-Cache处理器启用特别优化模式...")
        try:
            logger.info("使用串行处理模式...")
            
            # 为每个股票创建单独的目录，避免文件系统冲突
            stock_data_dir = os.path.join(data_dir, "temp_processing")
            if not os.path.exists(stock_data_dir):
                os.makedirs(stock_data_dir)
                
            for code in tqdm_func(stock_codes, desc="获取历史数据(串行)"):
                # 处理单个股票
                result_code, success = process_single_stock(code, data_dir)
                
                # 每处理一只股票后强制同步文件系统并暂停
                if success:
                    results["success"].append(code)
                    file_path = os.path.join(data_dir, f"{code}_historical_data.csv")
                    
                    # 确保文件写入完成 - 多重保障机制
                    if os.path.exists(file_path):
                        # 1. 关闭并重新打开文件进行读取验证
                        try:
                            with open(file_path, "r") as f:
                                # 读取几行验证文件内容
                                lines = [f.readline() for _ in range(3)]
                                if not any(lines):
                                    logger.warning(f"警告: 文件{code}_historical_data.csv可能为空")
                                    results["failed"].append(code)
                                    results["success"].remove(code)
                        except Exception as e:
                            logger.error(f"文件读取验证失败: {str(e)}")
                            
                        # 2. 尝试复制文件以确保写入
                        try:
                            backup_path = os.path.join(stock_data_dir, f"{code}_backup.csv")
                            shutil.copy2(file_path, backup_path)
                            os.remove(backup_path)  # 清理临时文件
                        except Exception as e:
                            logger.error(f"文件备份验证失败: {str(e)}")
                else:
                    results["failed"].append(code)
                
                # 在每个处理之间强制延迟，避免文件系统压力
                time.sleep(0.5)
            
            # 清理临时目录
            try:
                if os.path.exists(stock_data_dir):
                    shutil.rmtree(stock_data_dir)
            except Exception as e:
                logger.error(f"清理临时目录失败: {str(e)}")
                
        except Exception as e:
            logger.error(f"串行处理异常: {str(e)}")
            return {"success": results["success"], "failed": results["failed"] + [c for c in stock_codes if c not in results["success"] and c not in results["failed"]]}
    # 根据CPU类型选择不同的并行策略
    else:
        try:
            if is_amd_cpu:
                # 对于普通AMD CPU，使用进程池可能有更好的性能
                # 进程池避免了线程间的资源竞争问题
                logger.info("使用进程池执行任务...")
                try:
                    with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
                        # 提交所有任务
                        future_to_stock = {}
                        for code in stock_codes:
                            # 增加延迟，减轻AMD CPU的负担
                            time.sleep(0.1)
                            future = executor.submit(process_single_stock, code, data_dir)
                            future_to_stock[future] = code
                        
                        # 使用tqdm显示进度
                        with tqdm_func(total=total_stocks, desc="获取历史数据") as pbar:
                            for future in concurrent.futures.as_completed(future_to_stock):
                                try:
                                    code, success = future.result(timeout=60)  # 添加超时处理
                                    if success:
                                        results["success"].append(code)
                                    else:
                                        results["failed"].append(code)
                                except Exception as e:
                                    code = future_to_stock[future]
                                    logger.error(f"处理股票{code}时发生异常: {str(e)}")
                                    results["failed"].append(code)
                                pbar.update(1)
                                
                                # 每处理完一只股票，强制同步文件系统
                                if code in results["success"]:
                                    file_path = os.path.join(data_dir, f"{code}_historical_data.csv")
                                    try:
                                        if os.path.exists(file_path):
                                            # 确保文件写入完成
                                            with open(file_path, "a") as f:
                                                f.flush()
                                                os.fsync(f.fileno())
                                    except Exception as file_e:
                                        logger.error(f"文件同步失败: {str(file_e)}")
                except Exception as proc_e:
                    logger.error(f"并行处理异常: {str(proc_e)}")
                    # 如果进程池执行失败，回退到串行处理
                    logger.warning("进程池执行失败，回退到串行处理模式...")
                    for code in tqdm_func(stock_codes, desc="获取历史数据(串行)"):
                        result_code, success = process_single_stock(code, data_dir)
                        if success:
                            if result_code not in results["success"]:  # 避免重复添加
                                results["success"].append(result_code)
                        else:
                            if result_code not in results["failed"]:  # 避免重复添加
                                results["failed"].append(result_code)
            else:
                # 对于其他CPU，使用线程池
                logger.info("使用线程池执行任务...")
                try:
                    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                        # 提交所有任务
                        future_to_stock = {}
                        for code in stock_codes:
                            # 添加小延迟，避免同时发起太多请求
                            time.sleep(0.05)
                            future = executor.submit(process_single_stock, code, data_dir)
                            future_to_stock[future] = code
                        
                        # 使用tqdm显示进度
                        with tqdm_func(total=total_stocks, desc="获取历史数据") as pbar:
                            for future in concurrent.futures.as_completed(future_to_stock):
                                try:
                                    code, success = future.result()
                                    if success:
                                        results["success"].append(code)
                                    else:
                                        results["failed"].append(code)
                                except Exception as e:
                                    code = future_to_stock[future]
                                    logger.error(f"处理股票{code}时发生异常: {str(e)}")
                                    results["failed"].append(code)
                                pbar.update(1)
                                
                                # 强制刷新文件系统缓存
                                if code in results["success"]:
                                    file_path = os.path.join(data_dir, f"{code}_historical_data.csv")
                                    if os.path.exists(file_path):
                                        with open(file_path, "a") as f:
                                            f.flush()
                                            os.fsync(f.fileno())
                except Exception as thread_e:
                    logger.error(f"线程池执行异常: {str(thread_e)}")
                    # 如果线程池执行失败，回退到串行处理
                    logger.warning("线程池执行失败，回退到串行处理模式...")
                    for code in tqdm_func(stock_codes, desc="获取历史数据(串行)"):
                        result_code, success = process_single_stock(code, data_dir)
                        if success:
                            if result_code not in results["success"]:  # 避免重复添加
                                results["success"].append(result_code)
                        else:
                            if result_code not in results["failed"]:  # 避免重复添加
                                results["failed"].append(result_code)
        except Exception as e:
            logger.error(f"并行处理异常: {str(e)}")
            # 如果并行处理失败，回退到串行处理
            logger.warning("回退到串行处理模式...")
            try:
                for code in tqdm_func(stock_codes, desc="获取历史数据(串行)"):
                    result_code, success = process_single_stock(code, data_dir)
                    if success:
                        if result_code not in results["success"]:  # 避免重复添加
                            results["success"].append(result_code)
                    else:
                        if result_code not in results["failed"]:  # 避免重复添加
                            results["failed"].append(result_code)
            except Exception as serial_e:
                logger.error(f"串行处理异常: {str(serial_e)}")
                # 确保所有未处理的股票都被添加到失败列表中
                for code in stock_codes:
                    if code not in results["success"] and code not in results["failed"]:
                        results["failed"].append(code)
    
    logger.info(f"历史数据获取完成，成功: {len(results['success'])}，失败: {len(results['failed'])}")
    if results["failed"]:
        logger.warning(f"获取失败的股票: {results['failed']}")
    
    return results


def configure_historical_data_validation(enable_validation: bool = True, 
                                       enable_cleaning: bool = True,
                                       validator: Optional[DataValidator] = None,
                                       cleaner: Optional[DataCleaner] = None):
    """配置历史数据验证和清洗
    
    Args:
        enable_validation: 是否启用数据验证
        enable_cleaning: 是否启用数据清洗
        validator: 自定义验证器
        cleaner: 自定义清洗器
    """
    global ENABLE_VALIDATION, ENABLE_CLEANING, default_validator, default_cleaner
    
    ENABLE_VALIDATION = enable_validation
    ENABLE_CLEANING = enable_cleaning
    
    if validator:
        default_validator = validator
    
    if cleaner:
        default_cleaner = cleaner
    
    logger.info(f"历史数据验证配置更新: 验证={'启用' if enable_validation else '禁用'}, 清洗={'启用' if enable_cleaning else '禁用'}")


def get_historical_data_statistics() -> Dict[str, Any]:
    """获取历史数据处理统计信息
    
    Returns:
        Dict[str, Any]: 统计信息字典
    """
    return {
        'validation_enabled': ENABLE_VALIDATION,
        'cleaning_enabled': ENABLE_CLEANING,
        'validator_type': type(default_validator).__name__ if default_validator else None,
        'cleaner_type': type(default_cleaner).__name__ if default_cleaner else None,
        'config_manager': validation_config_manager.get_current_config() if validation_config_manager else None
    }


def reset_historical_data_config():
    """重置历史数据配置为默认值"""
    global ENABLE_VALIDATION, ENABLE_CLEANING, default_validator, default_cleaner
    
    ENABLE_VALIDATION = True
    ENABLE_CLEANING = True
    default_validator = StockDataValidatorFactory.create_basic_stock_validator()
    default_cleaner = BASIC_STOCK_CLEANER
    
    logger.info("历史数据配置已重置为默认值")


# 创建历史数据处理类，提供面向对象的接口
class HistoricalDataProcessor:
    """历史数据处理器类
    
    提供面向对象的历史数据获取、验证和清洗接口
    """
    
    def __init__(self, 
                 enable_validation: bool = True,
                 enable_cleaning: bool = True,
                 validator: Optional[DataValidator] = None,
                 cleaner: Optional[DataCleaner] = None):
        """初始化历史数据处理器
        
        Args:
            enable_validation: 是否启用数据验证
            enable_cleaning: 是否启用数据清洗
            validator: 自定义验证器
            cleaner: 自定义清洗器
        """
        self.enable_validation = enable_validation
        self.enable_cleaning = enable_cleaning
        self.validator = validator or StockDataValidatorFactory.create_basic_stock_validator()
        self.cleaner = cleaner or BASIC_STOCK_CLEANER
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'validation_failures': 0,
            'cleaning_applied': 0,
            'data_quality_score': 0.0
        }
    
    def get_stock_history(self, stock_code: str, years: int = 5) -> pd.DataFrame:
        """获取股票历史数据
        
        Args:
            stock_code: 股票代码
            years: 查询年份数
            
        Returns:
            pd.DataFrame: 历史数据DataFrame
        """
        self.stats['total_requests'] += 1
        
        try:
            # 使用全局函数获取数据
            df = get_stock_history(stock_code, years)
            
            if not df.empty:
                self.stats['successful_requests'] += 1
                
                # 应用自定义验证和清洗
                if self.enable_validation or self.enable_cleaning:
                    df = self._process_dataframe(df, stock_code)
            
            return df
        
        except Exception as e:
            logger.error(f"历史数据处理器获取数据失败 {stock_code}: {str(e)}")
            return pd.DataFrame()
    
    def get_recent_history(self, stock_code: str, days: int = 5) -> pd.DataFrame:
        """获取最近历史数据
        
        Args:
            stock_code: 股票代码
            days: 查询天数
            
        Returns:
            pd.DataFrame: 最近历史数据DataFrame
        """
        self.stats['total_requests'] += 1
        
        try:
            # 使用全局函数获取数据
            df = get_recent_stock_history(stock_code, days)
            
            if not df.empty:
                self.stats['successful_requests'] += 1
                
                # 应用自定义验证和清洗
                if self.enable_validation or self.enable_cleaning:
                    df = self._process_dataframe(df, stock_code)
            
            return df
        
        except Exception as e:
            logger.error(f"历史数据处理器获取最近数据失败 {stock_code}: {str(e)}")
            return pd.DataFrame()
    
    def _process_dataframe(self, df: pd.DataFrame, stock_code: str) -> pd.DataFrame:
        """处理DataFrame数据
        
        Args:
            df: 原始DataFrame
            stock_code: 股票代码
            
        Returns:
            pd.DataFrame: 处理后的DataFrame
        """
        if df.empty:
            return df
        
        original_count = len(df)
        
        try:
            # 转换为记录列表
            records = df.to_dict('records')
            
            # 数据验证
            if self.enable_validation and self.validator:
                validated_records = []
                validation_failures = 0
                
                for record in records:
                    standardized_record = _standardize_record_fields(record)
                    validation_result = self.validator.validate(standardized_record)
                    
                    if validation_result.is_valid or not validation_result.has_errors():
                        validated_records.append(record)
                    else:
                        validation_failures += 1
                
                records = validated_records
                self.stats['validation_failures'] += validation_failures
            
            # 数据清洗
            if self.enable_cleaning and self.cleaner and records:
                cleaned_records, cleaning_report = self.cleaner.clean_data(records)
                
                if cleaned_records:
                    records = cleaned_records
                    self.stats['cleaning_applied'] += 1
            
            # 转换回DataFrame
            if records:
                processed_df = pd.DataFrame(records)
                
                # 更新数据质量评分
                quality_score = len(processed_df) / original_count if original_count > 0 else 0.0
                self.stats['data_quality_score'] = (
                    self.stats['data_quality_score'] * (self.stats['successful_requests'] - 1) + quality_score
                ) / self.stats['successful_requests'] if self.stats['successful_requests'] > 0 else quality_score
                
                return processed_df
            else:
                return pd.DataFrame()
        
        except Exception as e:
            logger.error(f"DataFrame处理失败 {stock_code}: {str(e)}")
            return df
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        stats = self.stats.copy()
        
        # 计算成功率
        if stats['total_requests'] > 0:
            stats['success_rate'] = stats['successful_requests'] / stats['total_requests']
        else:
            stats['success_rate'] = 0.0
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'validation_failures': 0,
            'cleaning_applied': 0,
            'data_quality_score': 0.0
        }


# 创建默认的历史数据处理器实例
default_historical_processor = HistoricalDataProcessor()