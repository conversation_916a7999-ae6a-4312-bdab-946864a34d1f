#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
日志配置模块

为整个应用提供统一的日志配置，避免重复配置导致的日志输出重复
"""

import os
import sys
import logging
from datetime import datetime

def configure_logging(level=logging.INFO, log_file=None, module_name=None, third_party_level=logging.WARNING):
    """
    配置日志系统

    Args:
        level: 日志级别
        log_file: 日志文件路径，如果为None，则使用默认路径
        module_name: 模块名称，用于标识日志源
        third_party_level: 第三方库的日志级别，默认为WARNING

    Returns:
        logger: 已配置的日志器
    """
    # 如果日志已经配置过，则直接返回对应的logger
    if hasattr(logging, '_configured'):
        return logging.getLogger(module_name)

    # 确保logs目录存在
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)

    # 如果未指定日志文件，使用默认命名
    if log_file is None:
        log_file = os.path.join(log_dir, f"app_{datetime.now().strftime('%Y%m%d')}.log")

    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    # 清除已有的handlers，确保配置生效
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
        handler.close()

    # 配置日志
    logging.basicConfig(
        level=level,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file, mode='w', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    # 设置第三方库的日志级别

    # 设置其他常见第三方库的日志级别
    third_party_loggers = [
        'matplotlib',
        'urllib3',
        'requests',
        'chardet',
        'torch',
        'tensorflow',
        'sklearn',
        'numba'
    ]

    for logger_name in third_party_loggers:
        logging.getLogger(logger_name).setLevel(third_party_level)

    # 标记日志已被配置，避免重复配置
    setattr(logging, '_configured', True)

    # 返回logger实例
    return logging.getLogger(module_name)

def get_logger(module_name=None, level=logging.DEBUG, third_party_level=logging.WARNING):
    """
    获取配置好的logger实例

    Args:
        module_name: 模块名称，用于标识日志源
        level: 日志级别，默认为INFO
        third_party_level: 第三方库的日志级别，默认为WARNING

    Returns:
        logger: 配置好的日志器
    """
    if not hasattr(logging, '_configured'):
        return configure_logging(level=level, module_name=module_name, third_party_level=third_party_level)

    # 即使日志已配置，也确保第三方库的日志级别设置正确
    if hasattr(logging, '_configured') and not hasattr(logging, '_third_party_configured'):
        # 第三方库日志级别设置

        # 设置其他常见第三方库的日志级别
        third_party_loggers = [
            'matplotlib',
            'urllib3',
            'requests',
            'chardet',
            'torch',
            'tensorflow',
            'sklearn',
            'numba'
        ]

        for logger_name in third_party_loggers:
            logging.getLogger(logger_name).setLevel(third_party_level)

        # 标记第三方库日志已配置
        setattr(logging, '_third_party_configured', True)

    return logging.getLogger(module_name)